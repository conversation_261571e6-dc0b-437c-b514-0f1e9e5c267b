/* eslint-disable @typescript-eslint/naming-convention */
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import { setAllSystemIntegrationTableDataFetched } from '@/slices/sourceSystemCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

interface SystemIntegrationOfflineTableProps {
  onOfflineConnectFilteredDataCountChange: (count: string) => void;
  scrollPosition?: any;
  allConnections?: any;
}

const SystemIntegrationOfflineTable: React.FC<
  SystemIntegrationOfflineTableProps
> = ({
  onOfflineConnectFilteredDataCountChange,
  allConnections = [],
  scrollPosition,
}) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [searchItem, setSearchItem] = useState('');
  const [data, setData] = useState<{
    [key: string]: any;
  }>({
    values: [],
  });
  const [filteredData, setFilteredData] = useState([]);
  const [RowID, setRowID] = useState(null);

  // Constants
  const header: any = [
    {
      title: 'Connection Name',
      optionsEnabled: false,
      options: [],
      inputType: 'checkbox',
    },
    {
      title: 'File Name',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Type',
      optionsEnabled: true,
      options: ['sort'],
    },
    { title: 'Last Update', optionsEnabled: true, options: ['sort'] },
    { title: 'Created by', optionsEnabled: true, options: ['sort'] },
    { title: 'Status', optionsEnabled: false, options: ['sort'] },
  ];
  const enableOptions: boolean = true;

  // Selectors
  const warningState = useSelector(
    (state: RootState) => state.metadata.warningAlert,
  );

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.values.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOfflineConnections.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(setAllSystemIntegrationTableDataFetched(false));
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Connection ${RowID} deleted successfully`,
              content: `Connection ${RowID} has been successfully deleted and all the items connected with the connection 01 has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };

  const menuData: any = [
    {
      option: 'Delete Connection',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete Entry ${key}`,
            message: `Are you sure? Do you want to delete the Entity ${key} Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete',
          }),
        );
      },
    },
  ];

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  const resolveData = (result: any) => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const data: any = [];
    result.forEach((conn: any) => {
      const row: any = {
        id: conn.id,
        selected: false,
        isFavourite: conn?.isFavourite ? conn?.isFavourite : false,
        components: [],
      };
      row.components.push({
        value: conn.connection_name,
        disabled: false,
        userAvatarLink: null,
        type: 'checkbox',
      });
      row.components.push({
        value: conn.connection_details.file_name,
        // disabled: false,
        userAvatarLink: null,
        // type: 'checkbox',
      });
      row.components.push({
        value: conn.connection_details.file_type,
        // disabled: false,
        userAvatarLink: null,
        // type: 'checkbox',
      });
      row.components.push({
        value: new Date(conn.created_at).toLocaleDateString('en-US'),
        userAvatarLink: null,
        disabled: false,
        type: 'text',
      });
      row.components.push({
        value: conn.created_by || '',
        userAvatarLink: null,
        disabled: false,
        type: 'avatar',
      });
      row.components.push({
        value: conn.is_enabled,
        disabled: false,
        type: 'toggle',
      });

      data.push(row);
    });
    return data;
  };

  // Effects
  useEffect(() => {
    onOfflineConnectFilteredDataCountChange(String(filteredData.length));
  }, [filteredData, onOfflineConnectFilteredDataCountChange]);

  useEffect(() => {
    const formattedData = resolveData(
      allConnections.filter((resp: any) => resp.connection_type === 'offline'),
    );
    setData({
      ...data,
      values: formattedData,
    });
    setFilteredData(formattedData);
  }, []);

  return (
    <div className="flex h-full w-full flex-col py-4">
      <div className="w-[20vw]">
        <Searchbar
          value={searchItem}
          placeholder="Search Connection"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="h-[70vh] w-full overflow-auto">
        <Table
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          scrollPosition={scrollPosition}
          enableOptions={enableOptions}
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>
      <WarningDialog
        onConfirm={
          warningState.actionbuttonText === 'Yes, make it Inactive'
            ? () => {
                console.log('do nothing');
              }
            : deleteItem
        }
      />
    </div>
  );
};

export default SystemIntegrationOfflineTable;
