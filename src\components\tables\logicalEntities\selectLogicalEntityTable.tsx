import eData from 'public/testdata/logicalEntities/selectLogicalEntry.json';
import React, { useState } from 'react';

import Table from '@/components/ui/Table';

import { Searchbar } from '../../ui/Searchbar';

const SelectLogicalEntityTable: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Constants
  const header: any = [
    {
      title: 'DataSET',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Table Name',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Logical Entity',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Enriched Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
  ];
  const data: any = eData;

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState(data);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  return (
    <div className="flex   h-[66vh] w-full flex-col space-y-6 pt-6">
      <div className="w-[40vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className=" w-full overflow-auto">
        <Table
          isCondensedTable
          isDashTable
          header={header}
          data={filteredData}
          enableOptions={false}
          isView={props.mode === 'View'}
        />
      </div>
    </div>
  );
};

export default SelectLogicalEntityTable;
