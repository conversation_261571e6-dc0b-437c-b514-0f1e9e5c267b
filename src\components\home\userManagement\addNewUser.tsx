/* eslint-disable tailwindcss/no-custom-classname */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import eData from 'public/testdata/userManagement/viewUser.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Button } from '../../ui/Button';
import AddNewUserForm from './forms/addNewUserForm';

const AddNewUser: React.FC = () => {
  // Essentials
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  // States
  const [userFormvalues, setUserFormValues] = useState<any>({
    username: '',
    email: '',
    password: '',
    name: '',
  });

  // Derivatives
  const mode: any = searchParams?.get('mode');

  // Selector
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Methods
  const goBack = () => {
    router.push('/userManagement');
  };

  const handleCreateUser = () => {
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.craeteUser.url;
    const params = userFormvalues;
    dispatch(setIsLoading(true));
    apiService
      .postRequest(url, params)
      .then((res: any) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          goBack();
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'User Created successfully',
              content: 'New User has been created successfully',
            }),
          );
        } else {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: 'Something Went wrong',
            }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Error',
            content: err.response.data.message,
          }),
        );
        dispatch(setIsLoading(false));
      });
  };

  const handelCancel = () => {
    goBack();
  };

  useEffect(() => {
    if (
      mode !== 'Create' &&
      (!userFormvalues.name ||
        !userFormvalues.username ||
        !userFormvalues.email ||
        !userFormvalues.password)
    ) {
      setUserFormValues({
        username: 'Rahul',
        email: '<EMAIL>',
        password: 'ABCD',
        name: 'rahul32',
      });
    }
  });

  return (
    <div className="user-management-cred edit-user-management view-user-management flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} User{' '}
          {eData.name && mode !== 'Create' && <span>- {eData.name}</span>}
        </span>
      </div>
      <div className="flex h-[82vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        {mode !== 'View' ? (
          <AddNewUserForm
            isEdit={mode === 'Edit'}
            userFormvalues={userFormvalues}
            setUserFormValues={setUserFormValues}
          />
        ) : (
          <div className="flex h-full w-full flex-col space-y-5 p-3 ">
            <div className="flex h-fit w-full flex-row space-x-11">
              <div className="flex flex-col space-y-2 ">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Name (Display)
                </span>
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {isTourOpen ? eData.name : userFormvalues.name}
                </span>
              </div>
              <div className="flex flex-col space-y-2 ">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Username
                </span>
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {isTourOpen ? eData.username : userFormvalues.username}
                </span>
              </div>
              <div className="flex flex-col space-y-2 ">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Email ID
                </span>
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {isTourOpen ? eData.email : userFormvalues.email}
                </span>
              </div>
              <div className="flex flex-col space-y-2 ">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Password
                </span>
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {isTourOpen ? eData.password : userFormvalues.password}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
      {mode !== 'View' && (
        <div className="flex h-[40px] w-full flex-row items-center justify-end space-x-4">
          {' '}
          <Button
            className="flex h-[40px] items-center"
            type="button"
            onClick={handelCancel}
            intent="secondary"
          >
            Cancel
          </Button>
          <Button
            className="flex h-[40px] items-center"
            type="button"
            disabled={
              !userFormvalues.name ||
              !userFormvalues.username ||
              !userFormvalues.email ||
              !userFormvalues.password
            }
            onClick={() => {
              if (mode === 'Create') {
                handleCreateUser();
              }
            }}
          >
            {mode !== 'Create' ? 'Update' : 'Create'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default AddNewUser;
