'use client';

import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Eye,
  Settings,
  Download,
  Loader2,
} from 'lucide-react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/Button';
import V2DataGrid from '@/components/ui/V2DataGrid';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface TransformationProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onTransformationComplete?: (data: any) => void;
}

interface TransformationRule {
  id: number;
  rule_name: string;
  description: string;
  condition: string;
  action: string;
  records_affected: number;
  status: string;
}

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const Transformation = ({ onNext, uploadedFiles, onTransformationComplete }: TransformationProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);
  
  const [showPreview, setShowPreview] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  const getTransformationRules = async () => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproTransformationRules?.url || '/transformation/rules'}`;
    return await apiService.genproGetRequest(url);
  };

  const applyTransformation = async (workflowId: string, rules: any[]) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproTransformationApply?.url || '/transformation/apply'}`;
    return await apiService.genproPostRequest(url, { workflow_id: workflowId, rules });
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  // Get transformation file URL from the previous stage response
  const transformationFileUrl = (uploadedFiles as any)?.transformationResponse?.result?.transformation_file_url;
  
  // Mock transformation rules data
  const transformationRules: TransformationRule[] = [
    {
      id: 1,
      rule_name: 'Solar to CYP Mapping',
      description: 'Map all Solar entities to BSM CYP',
      condition: 'SMC contains "Solar"',
      action: 'Map to BSM CYP',
      records_affected: 150,
      status: 'Active'
    },
    {
      id: 2,
      rule_name: 'SEACHEF Logic',
      description: 'Apply 50% BF allocation for SEACHEF entities',
      condition: 'SMC equals "SEACHEF"',
      action: 'Apply 50% BF allocation',
      records_affected: 75,
      status: 'Active'
    },
    {
      id: 3,
      rule_name: 'Frontline Management',
      description: 'Keep original SMC for Frontline entities',
      condition: 'SMC equals "Frontline"',
      action: 'Keep original SMC',
      records_affected: 200,
      status: 'Active'
    },
    {
      id: 4,
      rule_name: 'Default Case',
      description: 'Standard mapping for all other entities',
      condition: 'All other cases',
      action: 'Apply standard mapping',
      records_affected: 60,
      status: 'Active'
    }
  ];

  // Mock preview data
  const previewData = [
    {
      id: 1,
      original_smc: 'Solar Z',
      original_vessel: 'Solar Vessel A',
      final_smc: 'BSM CYP',
      final_vessel: 'Solar Vessel A',
      bf_allocation: '100%',
      rule_applied: 'Solar to CYP Mapping'
    },
    {
      id: 2,
      original_smc: 'SEACHEF',
      original_vessel: 'SEACHEF Vessel B',
      final_smc: 'SEACHEF',
      final_vessel: 'SEACHEF Vessel B',
      bf_allocation: '50%',
      rule_applied: 'SEACHEF Logic'
    },
    {
      id: 3,
      original_smc: 'Frontline',
      original_vessel: 'Frontline Vessel C',
      final_smc: 'Frontline',
      final_vessel: 'Frontline Vessel C',
      bf_allocation: '100%',
      rule_applied: 'Frontline Management'
    },
    {
      id: 4,
      original_smc: 'BSM HEL',
      original_vessel: 'Standard Vessel D',
      final_smc: 'BSM HEL',
      final_vessel: 'Standard Vessel D',
      bf_allocation: '100%',
      rule_applied: 'Default Case'
    },
    {
      id: 5,
      original_smc: 'BSM CYP',
      original_vessel: 'Standard Vessel E',
      final_smc: 'BSM CYP',
      final_vessel: 'Standard Vessel E',
      bf_allocation: '100%',
      rule_applied: 'Default Case'
    }
  ];

  // Transform rules data for grid display
  const rulesGridData = transformationRules.map((rule) => ({
    id: rule.id,
    'RULE NAME': (
      <div className="flex items-center space-x-2">
        <Settings className="w-4 h-4 text-gray-500" />
        <span className="font-medium">{rule.rule_name}</span>
      </div>
    ),
    'DESCRIPTION': rule.description,
    'CONDITION': rule.condition,
    'ACTION': rule.action,
    'RECORDS AFFECTED': rule.records_affected.toLocaleString(),
    'STATUS': (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700">
        {rule.status}
      </span>
    )
  }));

  // Transform preview data for grid display
  const previewGridData = previewData.map((item) => ({
    id: item.id,
    'ORIGINAL SMC': item.original_smc,
    'ORIGINAL VESSEL': item.original_vessel,
    'FINAL SMC': (
      <span className={item.original_smc !== item.final_smc ? 'font-semibold text-blue-600' : ''}>
        {item.final_smc}
      </span>
    ),
    'FINAL VESSEL': (
      <span className={item.original_vessel !== item.final_vessel ? 'font-semibold text-blue-600' : ''}>
        {item.final_vessel}
      </span>
    ),
    'BF ALLOCATION': (
      <span className={item.bf_allocation !== '100%' ? 'font-semibold text-orange-600' : ''}>
        {item.bf_allocation}
      </span>
    ),
    'RULE APPLIED': (
      <span className="text-xs bg-gray-100 px-2 py-1 rounded">
        {item.rule_applied}
      </span>
    )
  }));

  // Grid column configuration for rules
  const rulesHeaderList = [
    { key: 'rule_name', name: 'RULE NAME', width: 200, filterType: '' },
    { key: 'description', name: 'DESCRIPTION', width: 250, filterType: '' },
    { key: 'condition', name: 'CONDITION', width: 180, filterType: '' },
    { key: 'action', name: 'ACTION', width: 180, filterType: '' },
    { key: 'records_affected', name: 'RECORDS AFFECTED', width: 140, filterType: '' },
    { key: 'status', name: 'STATUS', width: 100, filterType: '' }
  ];

  // Grid column configuration for preview
  const previewHeaderList = [
    { key: 'original_smc', name: 'ORIGINAL SMC', width: 140, filterType: '' },
    { key: 'original_vessel', name: 'ORIGINAL VESSEL', width: 160, filterType: '' },
    { key: 'final_smc', name: 'FINAL SMC', width: 140, filterType: '' },
    { key: 'final_vessel', name: 'FINAL VESSEL', width: 160, filterType: '' },
    { key: 'bf_allocation', name: 'BF ALLOCATION', width: 120, filterType: '' },
    { key: 'rule_applied', name: 'RULE APPLIED', width: 180, filterType: '' }
  ];

  const handleProceed = async () => {
    setSaveLoading(true);
    
    try {
      // For now, just proceed to next step
      // In a real implementation, you would apply transformation rules here
      onTransformationComplete?.(uploadedFiles);
      showSuccess('Transformation completed successfully.');
      onNext?.();
    } catch (error: any) {
      console.error('Transformation failed:', error);
      showError('Failed to complete transformation', error);
    } finally {
      setSaveLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full space-y-6 p-6">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden flex-shrink-0">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-800">
                {showPreview ? 'Transformation Preview' : 'Business Rules Configuration'}
              </h2>
              <div className="flex space-x-2">
                <Button
                  onClick={() => setShowPreview(!showPreview)}
                  className="flex items-center px-3 py-1.5 text-sm"
                  intent="secondary"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  {showPreview ? 'View Rules' : 'Preview Results'}
                </Button>

                {/* Download Transformation Result Button */}
                {transformationFileUrl && (
                  <Button
                    onClick={() => window.open(transformationFileUrl, '_blank')}
                    className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white hover:bg-green-700"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download Result
                  </Button>
                )}
              </div>
            </div>
            {showPreview && (
              <div className="mt-2 text-sm text-gray-600">
                Showing transformation results for {previewData.length} sample records
              </div>
            )}
          </div>
          
          <div className="p-4">
            {loading ? (
              <LoadingSpinner text="Loading transformation data..." />
            ) : showPreview ? (
              <V2DataGrid
                headerList={previewHeaderList}
                data={previewGridData}
              />
            ) : (
              <V2DataGrid
                headerList={rulesHeaderList}
                data={rulesGridData}
              />
            )}
          </div>
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">
              Transformation rules configured
            </span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">
              {transformationRules.length} active rules | {transformationRules.reduce((sum, rule) => sum + rule.records_affected, 0)} total records
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              onClick={handleProceed}
              disabled={saveLoading}
            >
              {saveLoading ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : (
                'Continue to Distribution →'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Transformation;