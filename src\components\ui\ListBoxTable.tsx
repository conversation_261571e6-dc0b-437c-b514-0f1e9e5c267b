/* eslint-disable no-nested-ternary */
/* eslint-disable eqeqeq */

'use client';

import { Listbox, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Tooltip } from 'react-tooltip';

import { Checkbox, CheckBoxStates } from './Checkbox';

interface IItem {
  name: string;
}

interface IListBoxProps {
  isInDataQualityDashboard?: boolean;
  items: Array<IItem>;
  name: string;
  label?: string;
  idName?: string;
  multiselect?: boolean;
  selectedValue?: string;
  hasValue: boolean;
  placeholder: string;
  isInTable?: boolean;
  refId?: any;
  scrollPosition?: any;
  onSelectionChange: (selectedValue: any) => void;
}

export default function ListBoxTable({
  isInDataQualityDashboard,
  items,
  multiselect,
  name,
  label,
  hasValue,
  idName,
  placeholder,
  selectedValue,
  isInTable,
  refId,
  scrollPosition,
  onSelectionChange,
}: IListBoxProps) {
  // Essentials
  const selectButtonRef: any = useRef(null);

  // Constants
  const selectedOption: any = { name: selectedValue };

  // States
  const [openBottom, setOpenBottom] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = useState<any>(
    multiselect ? [items[0]] : hasValue ? selectedOption : '',
  );
  const [firstLoad, setFirstLoad] = useState<any>(true);
  const id = idName || 'name';

  // Methods
  const calculatePosition = () => {
    const buttonPosition = selectButtonRef.current.getBoundingClientRect();
    if (buttonPosition && scrollPosition / 26 + 420 < buttonPosition.bottom) {
      setOpenBottom(true);
    } else {
      setOpenBottom(false);
    }
  };

  const handleSelectionChange = (newValue: any) => {
    setSelectedOptions(newValue);
    onSelectionChange(newValue);
  };

  // Effects
  useEffect(() => {
    if (firstLoad) {
      setFirstLoad(false);
      onSelectionChange(selectedOptions);
    }
  }, []);

  useEffect(() => {
    calculatePosition();
    // Recalculate the position when the window is resized
    window.addEventListener('resize', calculatePosition);
    return () => {
      window.removeEventListener('resize', calculatePosition);
    };
  }, [refId, selectButtonRef, scrollPosition]);

  useEffect(() => {
    if (selectedValue) {
      setSelectedOptions({ name: selectedValue });
    } else {
      setSelectedOptions({ name: '' });
    }
  }, [selectedValue]);

  return (
    <div className=" w-full" ref={selectButtonRef}>
      <Listbox
        value={selectedOptions}
        onChange={handleSelectionChange}
        multiple={multiselect}
        name={name}
      >
        {label ? (
          <Listbox.Label className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
            {label}
          </Listbox.Label>
        ) : (
          ''
        )}
        <div className="relative">
          <Listbox.Button
            className={`relative w-full cursor-pointer rounded px-[11px] text-left  text-sm font-normal text-blueGray-300 focus:outline-none focus-visible:border-blue-200 focus-visible:ring-2 focus-visible:ring-[white]/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm ${
              isInTable ? 'py-[4px]' : 'py-[14px]'
            }`}
          >
            {hasValue && (
              <span
                className={` ${
                  isInDataQualityDashboard ? 'text-xl font-medium' : ''
                } mr-3 block truncate`}
              >
                {multiselect
                  ? selectedOptions
                      ?.map((option: any) => option[id])
                      .join(', ') || placeholder
                  : selectedOptions?.[id] || placeholder}
              </span>
            )}
            {!hasValue && (
              <span className="font-sans text-sm font-normal text-gray-400">
                {placeholder}
              </span>
            )}
            <span className=" pointer-events-none absolute inset-y-0  right-0 flex items-center pr-2">
              <ChevronDownIcon
                className="w-5  text-gray-400 ui-open:rotate-180"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={` absolute z-50  mt-[6px] max-h-32 w-full overflow-auto rounded bg-white-200 px-1 py-2 text-base shadow-list  focus:outline-none sm:text-sm ${
                openBottom ? 'bottom-12' : ''
              } `}
            >
              {items.map((item: any) => (
                <Listbox.Option
                  key={item[id]}
                  className={({ active }) =>
                    `cursor-pointer flex relative  px-2 py-2 text-sm items-center  border-lightgray-100 font-normal [&:not(:last-child)]:border-b-[1px] ${
                      active ? 'bg-blue-200/10 text-blue-200 ' : 'text-gray-500'
                    } ${
                      hasValue && selectedOptions.name == item[id]
                        ? 'bg-blue-200/10 text-blue-200'
                        : ''
                    } `
                  }
                  value={item}
                >
                  {({ selected }) =>
                    multiselect ? (
                      <Checkbox
                        labelText={item[id]}
                        value={
                          selected
                            ? CheckBoxStates.Checked
                            : CheckBoxStates.Empty
                        }
                      />
                    ) : (
                      <span
                        data-tooltip-id="select-tooltip"
                        data-tooltip-content={item[id]}
                        className={`block truncate ${
                          hasValue && selectedOptions.name == item[id]
                            ? 'font-medium text-blue-200'
                            : 'font-normal'
                        }`}
                      >
                        {item[id]}
                      </span>
                    )
                  }
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
      <Tooltip
        className="custom-tooltip"
        id="select-tooltip"
        place="top"
        variant="info"
        positionStrategy="fixed"
      />
    </div>
  );
}
