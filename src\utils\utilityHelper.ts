/* eslint-disable no-param-reassign */
export const formatString = (str: string, args: any[]) => {
  let formatted = str;
  for (let i = 0; i < args.length; i += 1) {
    const regexp = new RegExp(`\\{${i}\\}`, 'gi');
    formatted = formatted.replace(regexp, args[i]);
  }
  console.log(formatted);
  return formatted;
};

export const formatNotification = (data: any) => {
  if (data) {
    if (data.status === 'FILE_PROCESSING_SUCCEEDED') {
      const message = `has been successfully processed.`;
      const alertMessage = `File ${data.originalFileName} has been successfully processed.`;
      const notification: any = {
        id: data.id,
        text: message,
        alertMessage,
        timestamp: data.uploadedAt,
        status: 'info',
        fileName: data.originalFileName,
        fileDetail: data,
      };
      return notification;
    }
    if (data.status === 'FILE_PROCESSING_FAILED') {
      const message = `could not be processed successfully`;
      const alertMessage = `File ${data.originalFileName} could not be processed successfully`;
      const notification: any = {
        id: data.id,
        text: message,
        timestamp: data.uploadedAt,
        alertMessage,
        status: 'error',
        fileName: data.originalFileName,
        fileDetail: data,
      };
      return notification;
    }
  }
  return null;
};

export const formatNotificationArray = (dataArray: any[]) => {
  if (dataArray && Array.isArray(dataArray)) {
    return dataArray.map((data: any) => {
      if (data.status === 'FILE_PROCESSING_SUCCEEDED') {
        const message = `has been successfully processed.`;
        const alertMessage = `File ${data.originalFileName} has been successfully processed.`;
        const notification: any = {
          id: data.id,
          text: message,
          timestamp: data.uploadedAt,
          alertMessage,
          status: 'info',
          fileName: data.originalFileName,
          fileDetail: data,
        };
        return notification;
      }
      if (data.status === 'FILE_PROCESSING_FAILED') {
        const message = `could not be processed successfully`;
        const alertMessage = `File ${data.originalFileName} could not be processed successfully`;
        const notification: any = {
          id: data.id,
          text: message,
          timestamp: data.uploadedAt,
          alertMessage,
          status: 'error',
          fileName: data.originalFileName,
          fileDetail: data,
        };
        return notification;
      }
      return null;
    });
  }
  return [];
};

export const getBgColor = (score: any) => {
  if (score === 1) {
    return 'bg-red-400';
  }
  if (score === 2) {
    return 'bg-yellow-300';
  }
  if (score === 3) {
    return 'bg-green-200';
  }
  return '';
};

export const formatSelectListData = (data: any) => {
  return data.map((item: any) => ({
    id: item.id,
    name: item.country,
  }));
};

export const getIcon = (type: any) => {
  if (type === 'error') {
    return '/assets/images/error.svg';
  }
  if (type === 'success') {
    return '/assets/images/success.svg';
  }
  if (type === 'warning') {
    return '/assets/images/warning.svg';
  }
  return '/assets/images/warning.svg';
};

export const getLighterBgColor = (score: any) => {
  if (score === 2) {
    return 'bg-red-400/20';
  }
  if (score === 1) {
    return 'bg-blue-200/20';
  }
  if (score === 3) {
    return 'bg-yellow-200/20';
  }
  if (score === 4) {
    return 'bg-green-200/20';
  }
  return 'bg-white-200';
};

export const resetAllFilters = () => {
  localStorage.removeItem('datasetDashboardFilters');
  localStorage.removeItem('fileUploadFilters');
  localStorage.removeItem('businessViewFilters');
  localStorage.removeItem('selectedDataQualityTab');
  localStorage.removeItem('fileUploadTab');
};

export const getMonthNumber = (monthName: string) => {
  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const formattedMonthName =
    monthName.charAt(0).toUpperCase() + monthName.slice(1).toLowerCase();
  const monthIndex = monthNames.indexOf(formattedMonthName);
  if (monthIndex === -1) {
    throw new Error('Invalid month name. Please provide a valid month name.');
  }
  return monthIndex + 1;
};

export const getLastAlphaNumericCharacter = (str: any) => {
  str = str.trim();
  if (str.length === 0) {
    throw new Error('The input string is empty.');
  }
  for (let i = str.length - 1; i >= 0; i -= 1) {
    if (/[a-zA-Z0-9]/.test(str[i])) {
      return str[i];
    }
  }
  throw new Error('No alphanumeric character found in the input string.');
};

export const getMonthName = (monthNumber: any) => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  if (monthNumber < 1 || monthNumber > 12) {
    return 'Invalid month number';
  }

  return months[monthNumber - 1];
};

export const formatSimpleTableData = (input: any) => {
  const { headers, data } = input;
  const formatValue: any = (value: any) => {
    if (typeof value === 'number' && value % 1 !== 0) {
      return value.toFixed(2);
    }
    return value;
  };
  const formattedHeaders = headers.map((header: any) => ({
    data: header,
    hasSorting: true,
  }));
  const formattedRows = data.map((row: any) => {
    const rowObj: any = {};
    headers.forEach((header: any, index: number) => {
      rowObj[header] = { data: formatValue(row[index]) };
    });
    return rowObj;
  });
  const result = {
    tableData: {
      headers: formattedHeaders,
      rows: formattedRows,
    },
  };

  return result;
};

export const convertToCSV = (objArray: any) => {
  const array = typeof objArray !== 'object' ? JSON.parse(objArray) : objArray;
  let csv = '';

  // Add headers
  const headers = array.headers.map((header: any) => header.data);
  csv += `${headers.join(',')}\n`;

  // Add rows
  array.rows.forEach((row: any) => {
    const values = headers.map((header: any) => {
      let value = row[header].data;
      if (typeof value === 'string' && value.includes(',')) {
        value = `"${value}"`;
      }
      return value;
    });
    csv += `${values.join(',')}\n`;
  });

  return csv;
};

export const jsonToCsv = (jsonData: any) => {
  const array = typeof jsonData !== 'object' ? JSON.parse(jsonData) : jsonData;
  let csv = '';
  // Add headers
  const headers = array.headers.map((header: any) => header.name);
  csv += `${headers.join(',')}\n`;
  // Add rows
  array.rows.forEach((row: any) => {
    const values = headers.map((header: any) => {
      const camelCaseKey = header
        .replace(/\s+\(.*?\)|[^a-zA-Z0-9]+(.)/g, (chr: any) =>
          chr ? chr.toUpperCase() : '',
        )
        .replace(/^[A-Z]/, (chr: any) => chr.toLowerCase());
      let value = row[camelCaseKey];
      if (typeof value === 'string' && value.includes(',')) {
        value = `"${value}"`;
      }
      return value;
    });
    csv += `${values.join(',')}\n`;
  });

  return csv;
};
export const formatDateString = (dateString: string) => {
  if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(dateString)) {
    const date = new Date(dateString);
    const day = String(date.getUTCDate()).padStart(2, '0');
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const year = date.getUTCFullYear();
    return `${day}-${month}-${year}`;
  }
  return dateString;
};
