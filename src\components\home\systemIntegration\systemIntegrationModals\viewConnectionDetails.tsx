/* eslint-disable import/no-cycle */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Tabs } from '@/components/ui/Tabs';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setOnlineConnectionName } from '@/slices/sourceSystemCrudSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import Domain from './connectionDetailsTabs/domain';
import ERP from './connectionDetailsTabs/erp';
import { JobSchedules } from './connectionDetailsTabs/integration';

const ViewConnectionDtails: React.FC<any> = ({ RowID }) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [selectedTab, setSelectedTab] = useState(0);
  const [servedData, setServedData] = useState(0);

  // Constants
  const tabData: any = [
    {
      title: 'Application Details',
      icon: '',
      component: <ERP servedData={servedData} />,
    },
    {
      icon: '',
      title: 'Domain & Table',
      component: <Domain viewOnly servedData={servedData} />,
    },
    {
      title: 'Integration Schedule',
      icon: '',
      component: <JobSchedules servedData={servedData} />,
    },
  ];

  // Effects
  useEffect(() => {
    const fetchData = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrl +
          ApiUtilities.apiPath.getOnlineConnections.url
        }/${RowID}`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            dispatch(setOnlineConnectionName(res.data.connection_name));
            setServedData(res.data);
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);
  return (
    <div className="h-full w-full overflow-hidden">
      <div className="h-full w-full rounded  bg-white-200 p-4">
        <Tabs
          data={tabData}
          selectedIndex={selectedTab}
          onChange={(e: number) => {
            setSelectedTab(e);
          }}
        />
      </div>
    </div>
  );
};

export default ViewConnectionDtails;
