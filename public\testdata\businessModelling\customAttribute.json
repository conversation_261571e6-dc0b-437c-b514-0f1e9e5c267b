[{"id": "Attribute 01", "selected": true, "components": [{"value": "DEVIATION", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "DUE_DATE_DEVIATION", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}, {"id": "Attribute 02", "selected": true, "components": [{"value": "Attribute 02", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}, {"id": "Attribute 03", "selected": true, "components": [{"value": "Attribute 03", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}, {"id": "Attribute 04", "selected": true, "components": [{"value": "Attribute 04", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}, {"id": "Attribute 05", "selected": true, "components": [{"value": "Attribute 05", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}, {"id": "Attribute 06", "selected": true, "components": [{"value": "Attribute 06", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "type": "formula", "disabled": false, "userAvatarLink": null}]}]