/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useEffect, useState } from 'react';

export const ChatModal: React.FC<any> = ({
  headerTitle,
  component,
  isOpen,
  closeModal,
  sendMessage,
  removePannelBorder,
  isModalExpand,
}) => {
  // State
  const [isModalExpanded, setIsModalExpanded] = useState<boolean>(false);
  const [modalWidth, setModalWidth] = useState('w-full');
  const [modalHeight, setModalHeight] = useState('h-full');
  const [message, setMessage] = useState('');
  const dummyQuestions: any = [
    'What is the highest growing expense in past 4 quarters?',
    'What is the trend of income and expenses quarter on quarter?',
    'What is the income trend of past 4 quarters?',
  ];
  // Method

  const maximizeOrMinimizeModal = (value: boolean) => {
    if (value) {
      isModalExpand(true);
      setIsModalExpanded(true);
      setModalWidth('w-[100vw]');
      setModalHeight('h-[100vh]');
    } else {
      isModalExpand(false);
      setIsModalExpanded(false);
      setModalWidth('w-full');
      setModalHeight('h-full');
    }
  };

  const handleEnterPress = (e: any) => {
    if (e.key === 'Enter') {
      if (message !== '') {
        sendMessage(message);
        setMessage('');
      }
    }
  };

  useEffect(() => {
    setModalWidth(modalWidth);
    setModalHeight(modalHeight);
    setIsModalExpanded(isModalExpanded);
  }, [isModalExpanded, modalWidth, modalHeight]);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-40 h-3/4" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className=" fixed inset-0 bg-blueGray-500" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`
                  ${modalHeight} overflow-hidden bg-gray-300 px-4 pb-3 pt-0 text-left align-middle shadow-xl transition-all`}
              >
                <Dialog.Title
                  as="h3"
                  className="flex flex-row items-center justify-between px-2 pt-[10px] text-lg font-medium  text-blueGray-300"
                >
                  <div className="flex flex-col">
                    <div className="flex flex-row space-x-2">
                      <img src="/assets/images/message.svg" alt="chat" />
                      <span>{headerTitle}</span>
                    </div>
                    <span className="font-sans text-[12px] leading-6 text-gray-400">
                      Explore your data in an interesting way. Ask Archimedes
                      about your data.
                    </span>
                  </div>

                  <div className="flex space-x-2">
                    {isModalExpanded ? (
                      <button
                        type="button"
                        className=" border-none outline-none"
                        onClick={() => maximizeOrMinimizeModal(false)}
                      >
                        <img
                          src="/assets/images/minimize.svg"
                          alt="exapand modal"
                          className="h-[24px] w-[24px]"
                        />
                      </button>
                    ) : (
                      <button
                        type="button"
                        className=" border-none outline-none"
                        onClick={() => maximizeOrMinimizeModal(true)}
                      >
                        <img
                          src="/assets/images/maximize.svg"
                          alt="exapand modal"
                          className="h-[24px] w-[24px]"
                        />
                      </button>
                    )}

                    <button
                      type="button"
                      className=" border-none outline-none"
                      onClick={closeModal}
                    >
                      <img
                        src="/assets/images/gray-cross.svg"
                        alt="Close modal"
                        className="h-[24px] w-[24px]"
                      />
                    </button>
                  </div>
                </Dialog.Title>
                <div
                  className={`${
                    isModalExpanded
                      ? 'h-[calc(100vh-140px)]'
                      : 'h-[calc(100vh-270px)] w-[80vw] lg:w-[60vw]'
                  } overflow-y-hidden  border-t border-lightgray-100`}
                >
                  <div
                    className={` ${
                      isModalExpanded ? 'h-[10%]' : 'h-[12%]'
                    } flex w-full items-center justify-center bg-white-200   p-2`}
                  >
                    <div className=" flex w-full flex-row">
                      {dummyQuestions.map((item: any) => (
                        <span
                          onClick={() => {
                            sendMessage(item);
                            setMessage('');
                          }}
                          key={item}
                          className="mx-1 w-[900px] cursor-pointer rounded-md border-[1px] border-lightgray-100 bg-gray-300/40 px-2 py-1 text-xs font-normal text-gray-400"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div
                    className={`${
                      isModalExpanded ? 'h-[89.5%]' : 'h-[87%]'
                    }   overflow-auto bg-white-200  py-2 ${
                      removePannelBorder
                        ? 'mt-1 border-none'
                        : 'border-[1px] border-lightgray-100'
                    }`}
                  >
                    {component}
                  </div>
                </div>
                <div className=" relative flex flex-row items-center justify-between pb-2 pt-3">
                  <input
                    type="text"
                    value={message}
                    placeholder="Type your message here ..."
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleEnterPress}
                    className=" w-full rounded border-none
          border-lightgray-100
          bg-white-200
          px-3
          py-4
          font-sans
          text-sm
          font-normal
          leading-4
          text-blueGray-300
          placeholder:text-gray-400 hover:text-gray-400 focus:border-none focus:border-purple-200 focus:text-gray-400 focus:shadow-blue focus:outline-none focus:ring-0 active:text-blueGray-300"
                  />
                  <button type="button">
                    {' '}
                    <img
                      src="/assets/images/send.svg"
                      alt="Enter"
                      onClick={() => {
                        if (message !== '') {
                          sendMessage(message);
                          setMessage('');
                        }
                      }}
                      className="absolute bottom-5 right-3 h-[24px] w-[24px] cursor-pointer"
                    />{' '}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
