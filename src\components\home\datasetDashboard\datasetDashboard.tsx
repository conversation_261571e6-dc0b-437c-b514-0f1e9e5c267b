/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import React, { useState } from 'react';

import DatasetDashboardTable from '@/components/tables/datasetDashboard/datasetDashboardTable';

const DatasetDashboard: React.FC = () => {
  const [datasetCount, setDatasetCount] = useState(0);

  const handleOnDatasetCountChange = (count: any) => {
    setDatasetCount(count);
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full flex-row items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Datasets
        </span>
        <span className="font-sans text-sm  font-medium leading-8 text-blueGray-400">
          Total <span className=" text-lightblue-200">{datasetCount}</span>{' '}
          datasets
        </span>
      </div>
      <div className="dataSet-table flex h-[calc(100vh-100px)] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <DatasetDashboardTable
          onDatasetCountChange={handleOnDatasetCountChange}
        />
      </div>
    </div>
  );
};

export default DatasetDashboard;
