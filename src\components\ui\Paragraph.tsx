import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import React from 'react';

const paragraph = cva('font-sans', {
  variants: {
    intent: {
      p300: ['text-base font-semibold leading-6 text-gray-400'],
      p200: ['text-sm font-normal leading-5 text-blueGray-300'],
      p100: ['text-xs font-normal leading-4 text-blueGray-300'],
    },
  },
  compoundVariants: [
    {
      intent: 'p100',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'p100',
  },
});

export interface ParagraphProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    VariantProps<typeof paragraph> {}

export const Paragraph: React.FC<ParagraphProps> = ({
  className,
  intent,
  content,
  ...props
}) => (
  <p {...props} className={paragraph({ intent, className })}>
    {content}
  </p>
);
