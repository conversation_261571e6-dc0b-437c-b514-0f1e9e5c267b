//  reference: https://redux-toolkit.js.org/tutorials/quick-start

import { configureStore } from '@reduxjs/toolkit';

import appReducer from '../slices/appSlice';
import businessModelCrudReducer from '../slices/businessModelCrudSlice';
import datasetReducer from '../slices/datasetCrudSlice';
import fileUploadReducer from '../slices/fileUploadSlice';
import logicalEntityCrudReducer from '../slices/logicalEntityCrudSlice';
import metaDataReducer from '../slices/metaDataSlice';
import resetPasswordEmail from '../slices/resetPasswordSlice';
import sideBarExpanded from '../slices/sideBarExpandedSlice';
import sourceSystemCrudReducer from '../slices/sourceSystemCrudSlice';
import sourceSystemReducer from '../slices/sourceSystemSlice';

export const store = configureStore({
  reducer: {
    sideBarExpand: sideBarExpanded,
    resetPassword: resetPasswordEmail,
    sourceSystems: sourceSystemReducer,
    sourceSystemsCrud: sourceSystemCrudReducer,
    metadata: metaDataReducer,
    datasetCrud: datasetReducer,
    appConfig: appReducer,
    logicalEntityCrud: logicalEntityCrudReducer,
    businessModelCrud: businessModelCrudReducer,
    fileUploadCred: fileUploadReducer,
  },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
