/* eslint-disable tailwindcss/no-custom-classname */

'use client';

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { createMenuName } from '@/constants/menuSvg';
import {
  setActivatedAttributes,
  setCustomAttributes,
  setJoinConfig,
  setLogicalEntityName,
  setMappedAttributes,
  setSelectedDatasets,
} from '@/slices/logicalEntityCrudSlice';

import { Link } from '../../ui/Link';
import LogicalEntityTabs from './logicalEntityTabs';

const LogicalEntities: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();

  // Methods
  const goToAddNewEntity = () => {
    const parameter: any = {
      mode: 'Create',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/logicalEntities/addNewEntity?${queryString}`);
  };

  const clearStore = () => {
    dispatch(setLogicalEntityName(''));
    dispatch(setActivatedAttributes({}));
    dispatch(setSelectedDatasets([]));
    dispatch(setMappedAttributes({}));
    dispatch(setCustomAttributes({}));
    dispatch(setJoinConfig({}));
  };

  // Effects
  useEffect(() => {
    clearStore();
  });

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Logical Entities
        </span>
        <div className="create-logical-entities">
          <Link
            content={createMenuName('Create Entity')}
            onClick={goToAddNewEntity}
          />
        </div>
      </div>
      <div className="logical-entities-table flex h-[88vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 ">
        <LogicalEntityTabs />
      </div>
    </div>
  );
};

export default LogicalEntities;
