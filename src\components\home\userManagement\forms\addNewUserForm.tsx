'use client';

/* eslint-disable import/no-extraneous-dependencies */
import { Formik } from 'formik';
import React, { useState } from 'react';

import { Input } from '@/components/ui/Input';

const AddNewUserForm: React.FC<{
  isEdit: boolean;
  userFormvalues: any;
  setUserFormValues: any;
}> = ({ isEdit, userFormvalues, setUserFormValues }) => {
  // const roleList = [
  //   { name: 'Analyst' },
  //   { name: 'Developer' },
  //   { name: 'Tester' },
  // ];
  const [showPassword, setShowPassword] = useState(false);

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleInputChange = (e: any, name: any) => {
    setUserFormValues((prevUserFormValues: any) => ({
      ...prevUserFormValues,
      [name]: e.target.value,
    }));
  };
  const isRequired = (value: string) => {
    return value ? undefined : 'Required.';
  };

  const isValidEmail = (value: string) => {
    return /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(value)
      ? undefined
      : 'Invalid email address.';
  };
  return (
    <div className="flex h-[88vh] w-full flex-col overflow-auto rounded-md   bg-white-200 ">
      <Formik
        initialValues={
          isEdit
            ? {
                Lname: userFormvalues.name ? userFormvalues.name : 'Rahul',
                username: userFormvalues.username
                  ? userFormvalues.username
                  : 'Rahul123',
                email: userFormvalues.email
                  ? userFormvalues.email
                  : '<EMAIL>',
                // role: '',
                password: userFormvalues.password
                  ? userFormvalues.password
                  : 'password',
              }
            : {
                Lname: '',
                username: '',
                email: '',
                // role: '',
                password: '',
              }
        }
        validate={(values) => {
          const errors: any = {};
          errors.Lname = isRequired(values.Lname);
          errors.username = isRequired(values.username);
          errors.password = isRequired(values.password);
          errors.email = isRequired(values.email) || isValidEmail(values.email);
          return errors;
        }}
        onSubmit={(values, { setSubmitting }) => {
          setTimeout(() => {
            console.log(values);
            // can show loader if wanted
            setSubmitting(false);
            // router.push('/dashboard');
          }, 400);
        }}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          // setFieldValue,
        }) => (
          <form
            className="flex h-full w-full flex-col space-y-5 p-3 "
            onSubmit={handleSubmit}
          >
            <div className="flex h-fit w-full flex-row space-x-6">
              <div className="w-[20%]">
                {' '}
                <Input
                  label="Name (Display)"
                  name="Lname"
                  type="text"
                  disabled={isEdit}
                  className=""
                  placeholder="Enter Name (Display)"
                  onChange={(e) => {
                    handleChange(e);
                    handleInputChange(e, 'name');
                  }}
                  onBlur={handleBlur}
                  value={values.Lname}
                  intent={
                    errors.Lname && touched.Lname ? 'hasError' : 'enabled'
                  }
                  error={errors.Lname && touched.Lname && errors.Lname}
                />
              </div>
              <div className="w-[20%]">
                {' '}
                <Input
                  label="Username"
                  name="username"
                  type="text"
                  className=""
                  disabled={isEdit}
                  placeholder="Enter Username"
                  onChange={(e) => {
                    handleChange(e);
                    handleInputChange(e, 'username'); // Update state
                  }}
                  onBlur={handleBlur}
                  value={values.username}
                  intent={
                    errors.username && touched.username ? 'hasError' : 'enabled'
                  }
                  error={errors.username && touched.username && errors.username}
                />
              </div>
              <div className="w-[20%]">
                {' '}
                <Input
                  label="Email ID"
                  name="email"
                  type="text"
                  className=""
                  placeholder="Enter Email"
                  disabled={isEdit}
                  onChange={(e) => {
                    handleChange(e);
                    handleInputChange(e, 'email');
                  }}
                  onBlur={handleBlur}
                  value={values.email}
                  intent={
                    errors.email && touched.email ? 'hasError' : 'enabled'
                  }
                  error={errors.email && touched.email && errors.email}
                />
              </div>
              {/* <div className="w-[20%]">
                {' '}
                <div className="flex h-full w-full flex-col space-y-1">
                  <span className="font-sans text-sm font-semibold text-gray-500">
                    Role
                  </span>
                  <div className="">
                    <ListBox
                      items={roleList}
                      name="role"
                      multiselect={false}
                      onSelectionChange={(selectedValue) => {
                        // const array: any = [selectedValue.name];
                        setFieldValue('role', selectedValue.name);
                      }}
                    />
                  </div>
                </div>
              </div> */}
              <div className="relative flex w-[20%] flex-row items-center">
                {' '}
                <Input
                  className="w-full"
                  label="Password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Password"
                  onChange={(e) => {
                    handleChange(e);
                    handleInputChange(e, 'password');
                  }}
                  onBlur={handleBlur}
                  value={values.password}
                  error={errors.password && touched.password && errors.password}
                  intent={
                    errors.password && touched.password ? 'hasError' : 'enabled'
                  }
                />
                <img
                  className="absolute right-2 top-10 h-[24px] w-[24px] "
                  src="/assets/images/show.svg"
                  alt="show password"
                  onMouseEnter={toggleShowPassword}
                  onMouseLeave={toggleShowPassword}
                />
              </div>
            </div>
          </form>
        )}
      </Formik>
    </div>
  );
};

export default AddNewUserForm;
