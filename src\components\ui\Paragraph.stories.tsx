import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Paragraph } from './Paragraph';

const meta = {
  title: 'UI/Paragraph',
  component: Paragraph,
  args: {
    intent: 'p100',
  },
} satisfies Meta<typeof Paragraph>;

export default meta;
type Story = StoryObj<typeof meta>;

const P300: Story = {
  args: {
    content:
      'This is Paragraph 300 This is Heading Next.js Boilerplate is a starter code for your Next ',
    intent: 'p300',
  },
};
const P200: Story = {
  args: {
    content:
      'This is Paragraph 200  This is Heading Next.js Boilerplate is a starter code for your Next',
    intent: 'p200',
  },
};
const P100: Story = {
  args: {
    content:
      'This is Paragraph 100 This is Heading Next.js Boilerplate is a starter code for your Next',
    intent: 'p100',
  },
};

export { P100, P200, P300 };
