/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-danger */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import { useSearchParams } from 'next/navigation';
import React from 'react';

// import SelectData from './addLogicalEntitiesTabs/selectData';

const InsightDashboard: React.FC = () => {
  // States
  // const router = useRouter();
  const searchParams = useSearchParams();
  const name: any = searchParams?.get('name');
  const url: any = searchParams?.get('url');
  const [selectedDashboard] = React.useState<any>({
    url: url || '',
    name: name || '',
  });

  // const goBack = () => {
  //   router.back();
  // };

  const generateIframeElement = (url: string): any => {
    return `<iframe width=100% height=100% src="${url}" frameBorder=0 allowFullScreen />`;
  };

  return (
    <div className="-mx-10 -mt-6">
      {selectedDashboard.name && selectedDashboard.url && (
        <div className="flex size-full flex-col space-y-4">
          <div className="flex w-full flex-col rounded bg-white-200">
            <div
              className="h-[calc(100vh-0px)] w-full flex-col bg-white-200"
              dangerouslySetInnerHTML={{
                __html: generateIframeElement(selectedDashboard.url),
              }}
            />
          </div>
        </div>
      )}{' '}
    </div>
  );
};

export default InsightDashboard;
