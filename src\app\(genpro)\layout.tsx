'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { HomeIcon } from '@heroicons/react/24/outline';

import { GenProWorkflowProvider } from '@/contexts/GenProWorkflowContext';
import Breadcrumb from '@/components/ui/Breadcrumb';

export default function GenProLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Generate breadcrumb items based on current path
  const getBreadcrumbItems = () => {
    const items = [
      {
        label: 'Home',
        href: '/home',
        icon: <HomeIcon className="w-4 h-4" />
      },
      {
        label: 'GenPro Hub',
        href: '/genpro'
      }
    ];

    // Add specific breadcrumb for current page
    if (pathname === '/genpro/workflow') {
      items.push({ label: 'Workflow', href: '/genpro/workflow' });
    } else if (pathname === '/genpro/analytics') {
      items.push({ label: 'Analytics', href: '/genpro/analytics' });
    } else if (pathname === '/genpro/configuration') {
      items.push({ label: 'Configuration', href: '/genpro/configuration' });
    } else if (pathname === '/genpro/logs') {
      items.push({ label: 'Process Logs', href: '/genpro/logs' });
    }
    else if (pathname === '/genpro/mapping') {
      items.push({ label: 'Entity Management', href: '/genpro/mapping' });
    }

    return items;
  };

  return (
    <GenProWorkflowProvider>
      <div className="h-screen bg-lightgray-100/40 flex flex-col overflow-hidden">
        {/* Breadcrumb header */}
        <div className="bg-white border-b border-gray-200 px-6 py-3 flex-shrink-0">
          <Breadcrumb items={getBreadcrumbItems()} />
        </div>
        
        {/* Main content */}
        <div className="flex-1 overflow-hidden p-2">
          {children}
        </div>
      </div>
    </GenProWorkflowProvider>
  );
}