/* eslint-disable no-unsafe-optional-chaining */
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setSourceSystemForms } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const ERP: React.FC<any> = ({ servedData }) => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [sourceSystemFields, setSourceSystemFields] = useState<any>([]);

  // Selectors
  const sourceSystems = useSelector(
    (state: any) => state.metadata.mappedSourceSystems,
  );
  const sourceSystemForm = useSelector(
    (state: any) => state.sourceSystemsCrud.sourceSystemForm,
  );

  // Effects
  useEffect(() => {
    if (sourceSystemForm && sourceSystemForm[servedData?.source_system_id]) {
      setSourceSystemFields(sourceSystemForm[servedData?.source_system_id]);
    } else if (servedData) {
      dispatch(setIsLoading(true));
      const url =
        ApiUtilities.getApiServerUrl +
        ApiUtilities.apiPath.getSourceSystemFormFields.url +
        servedData?.source_system_id;
      apiService
        .getRequest(url)
        .then((res) => {
          setSourceSystemFields(res.data);
          const ssf = structuredClone(sourceSystemForm);
          const ss = parseInt(servedData?.source_system_id, 10);
          let ssForm: any = {};
          ssForm[ss] = structuredClone(res.data);
          ssForm = { ...ssf, ...ssForm };
          dispatch(setSourceSystemForms(ssForm));
        })
        .finally(() => dispatch(setIsLoading(false)));
    }
  }, [servedData]);

  return (
    <div className="flex h-[57vh] w-full flex-col space-y-6 overflow-auto pl-2 pt-6">
      <div className="flex flex-col space-y-2">
        <span className="font-sans text-xs font-medium text-gray-400">
          Application Name
        </span>{' '}
        <span className="font-sans text-sm font-semibold text-gray-500">
          {sourceSystems?.[servedData?.source_system_id]?.source_system_name}
        </span>
      </div>
      <div className="flex flex-row space-x-11">
        {sourceSystemFields?.map((field: any) => (
          <div key={field.id} className="flex flex-col space-y-2">
            <span className="font-sans text-xs font-medium text-gray-400">
              {field.field_label}
            </span>{' '}
            <span className="font-sans text-sm font-semibold text-gray-500">
              {
                servedData?.connection_details?.connection_config[
                  field.field_name
                ]
              }
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ERP;
