import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Pills } from './Pills';

const meta = {
  title: 'UI/Pills',
  component: Pills,
  args: {
    intent: 'success',
  },
} satisfies Meta<typeof Pills>;

export default meta;
type Story = StoryObj<typeof meta>;
const Success: Story = {
  args: {
    intent: 'success',
    title: '500',
  },
};

const Info: Story = {
  args: {
    intent: 'info',
    title: '25',
  },
};

const Warning: Story = {
  args: {
    intent: 'warning',
    title: '56',
  },
};
const Error: Story = {
  args: {
    intent: 'error',
    title: '40',
  },
};
const Neutral: Story = {
  args: {
    intent: 'neutral',
    title: '089',
  },
};
export { Error, Info, Neutral, Success, Warning };
