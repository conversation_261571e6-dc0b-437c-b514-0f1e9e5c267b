import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { setIsWarningAlertOpen } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

import { Button } from './Button';

export const WarningDialog: React.FC<any> = ({ onConfirm }) => {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const warningState = useSelector(
    (state: RootState) => state.metadata.warningAlert,
  );

  // Methods
  const closeModal = () => {
    if (warningState.isWarningOpen) {
      dispatch(setIsWarningAlertOpen(false));
    }
  };
  return (
    <Transition appear show={warningState.isWarningOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-blueGray-200 opacity-60" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="min-h-[20vh] w-[53vw]  overflow-hidden rounded bg-white-200 shadow-xl transition-all">
                <div className="flex h-full w-full flex-col items-center justify-center">
                  <img
                    className="mb-[32px] mt-[42px]"
                    src="/assets/images/warning.svg"
                    alt="warning"
                  />
                  <div className="mb-[16px]">
                    <span className="font-sans text-2xl font-semibold text-blueGray-300">
                      {warningState.headerTitle}
                    </span>
                  </div>
                  <div className="flex max-w-[380px] justify-center ">
                    <span className="font-sans text-sm font-semibold text-gray-500">
                      {warningState.message}
                    </span>
                  </div>
                  <div className="mt-[32px] flex w-full flex-row items-center justify-center space-x-4 pb-[40px]">
                    <Button
                      type="button"
                      onClick={closeModal}
                      intent="secondary"
                    >
                      {warningState.cancelButtonText || ' No, Discard'}
                    </Button>
                    <Button type="button" onClick={onConfirm} className="ml-4">
                      {warningState.actionbuttonText}
                    </Button>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
