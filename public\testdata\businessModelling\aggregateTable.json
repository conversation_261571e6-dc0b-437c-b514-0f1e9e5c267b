[{"id": "Attribute 01", "selected": true, "components": [{"value": "LEGAL_ENTITY", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "LEGAL_ENTITY"}, {"name": "FISCAL_YEAR"}, {"name": "POSTING_PERIOD"}, {"name": "BUSINESS_UNIT"}]}, {"value": "INT", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "INT"}, {"name": "LONG"}, {"name": "INT"}]}, {"value": "Entity_Name", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Entity_Name"}]}, {"id": "Attribute 02", "selected": true, "components": [{"value": "FISCAL_YEAR", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "LEGAL_ENTITY"}, {"name": "FISCAL_YEAR"}, {"name": "POSTING_PERIOD"}, {"name": "BUSINESS_UNIT"}]}, {"value": "INT", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "INT"}, {"name": "LONG"}, {"name": "INT"}]}, {"value": "FISCAL_YEAR", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "FISCAL_YEAR"}]}, {"id": "Attribute 03", "selected": true, "components": [{"value": "POSTING_PERIOD", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "LEGAL_ENTITY"}, {"name": "FISCAL_YEAR"}, {"name": "POSTING_PERIOD"}, {"name": "BUSINESS_UNIT"}]}, {"value": "INT", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "INT"}, {"name": "LONG"}, {"name": "INT"}]}, {"value": "PERIOD", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "PERIOD"}]}, {"id": "Attribute 04", "selected": true, "components": [{"value": "BUSINESS_UNIT", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "LEGAL_ENTITY"}, {"name": "FISCAL_YEAR"}, {"name": "POSTING_PERIOD"}, {"name": "BUSINESS_UNIT"}]}, {"value": "INT", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "INT"}, {"name": "LONG"}, {"name": "INT"}]}, {"value": "BU MAPPING", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "BU_MAPPING"}]}]