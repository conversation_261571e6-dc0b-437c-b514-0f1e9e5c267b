/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-nested-ternary */

'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { createMenuName } from '@/constants/menuSvg';
import { APIService } from '@/service/api.service';
import { setAllSystemIntegrationTableDataFetched } from '@/slices/sourceSystemCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import SystemIntegrationOfflineTable from '../../tables/systemIntegration/systemIntegrationOfflineConnection';
import SystemIntegrationTable from '../../tables/systemIntegration/systemIntegrationTable';
import { Badge } from '../../ui/Badge';
import { Dropdown } from '../../ui/Dropdown';
import { Tabs } from '../../ui/Tabs';

const SystemIntegration: React.FC = () => {
  // essentials
  const router = useRouter();
  const searchParams = useSearchParams();
  const mode: any = searchParams?.get('mode');
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [showDefaultMenu] = useState(mode === 'Create');
  const [filteredDataCount, setFilteredDataCount] = useState('0');
  const [filteredDataCountOffline, setFilteredDataCountOffline] =
    useState<any>('0');
  const [selectedTab, setSelectedTab] = useState(0);
  const [allConnections, setAllConnections] = useState<any>([]);
  const [dataUpdated, setIsDataUpdated] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const allSystemIntegrationTableDataFetched = useSelector(
    (state: RootState) =>
      state.sourceSystemsCrud.allSystemIntegrationTableDataFetched,
  );

  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Methods
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const fetchSystemIntegrationData = async () => {
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url;
    apiService.getRequest(url).then((res) => {
      dispatch(setAllSystemIntegrationTableDataFetched(true));
      setAllConnections(res.data);
      setFilteredDataCountOffline(
        res.data.filter((resp: any) => resp.connection_type === 'offline')
          .length,
      );
      setIsDataUpdated(false);
    });
  };

  const handleOnlineConnectFilterCountUpdate = (count: any) => {
    setFilteredDataCount(count);
  };

  const handleOfflineConnectFilterCountUpdate = (count: any) => {
    setFilteredDataCountOffline(count);
  };

  const tabData: any = [
    {
      title: 'Online Connections',
      icon: (
        <Badge
          intent={selectedTab === 0 ? 'counter' : 'neutral'}
          content={isTourOpen ? '5' : filteredDataCount}
        />
      ),
      component: (
        <SystemIntegrationTable
          scrollPosition={scrollPosition}
          onOnlineConnectFilteredDataCountChange={
            handleOnlineConnectFilterCountUpdate
          }
          allConnections={allConnections}
        />
      ),
    },
    {
      icon: (
        <Badge
          intent={selectedTab === 1 ? 'counter' : 'neutral'}
          content={filteredDataCountOffline}
        />
      ),
      title: 'Offline Connections',
      component: (
        <SystemIntegrationOfflineTable
          scrollPosition={scrollPosition}
          allConnections={allConnections}
          onOfflineConnectFilteredDataCountChange={
            handleOfflineConnectFilterCountUpdate
          }
        />
      ),
    },
  ];

  function goToOnlineConnection() {
    const parameter: any = {
      mode: 'Create',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/systemIntegration/addNewOnlineConnection?${queryString}`);
  }

  function goToOfflineConnection() {
    const parameter: any = {
      mode: 'Create',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/systemIntegration/addNewOfflineConnection?${queryString}`);
  }

  const menuData: any = [
    { option: 'Online Connection', clickFn: goToOnlineConnection },
    { option: 'Offline Connection', clickFn: goToOfflineConnection },
  ];

  // Effects
  useEffect(() => {
    if (dataUpdated || !allSystemIntegrationTableDataFetched) {
      fetchSystemIntegrationData();
    }
  });

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full flex-row items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          System Integration
        </span>
        <div className="create-system-integration">
          <Dropdown
            menuTitle={createMenuName('Create Connection')}
            data={menuData}
            showMenu={showDefaultMenu}
          />
        </div>
      </div>
      <div
        className="system-integration-table flex h-fit min-h-[88vh] w-full flex-col overflow-hidden rounded border-[1px] border-lightgray-100 bg-white-200 p-4 pb-0"
        onScroll={handleScroll}
      >
        <Tabs
          data={tabData}
          selectedIndex={selectedTab}
          onChange={(e: number) => {
            setSelectedTab(e);
          }}
        />
      </div>
    </div>
  );
};

export default SystemIntegration;
