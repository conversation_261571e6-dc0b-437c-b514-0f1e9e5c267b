/* eslint-disable jsx-a11y/anchor-is-valid */

import { useRouter } from 'next/navigation';
import dataQualityTables from 'public/testdata/dataQuality/dataQualityTableData.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DataQualityTable from '@/components/ui/DataQualityTable';
import SimpleTable from '@/components/ui/SimpleTable';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

const ReconTest: React.FC<{ selectedYear?: any }> = ({ selectedYear }) => {
  const localService = new LocalService();
  const [selectedYearForView, setSelectedYearForView] =
    useState<any>(selectedYear);
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const [tableData, setTableData] = useState<any>(null);
  const [isWarningSelected, setIsWarningSelected] = useState<any>(false);
  const [isSuccessSelected, setIsSuccessSelected] = useState<any>(false);
  const [selectedTableData, setSelectedTableData] = useState<any>(null);
  const [selectedTable, setSelectedTable] = useState<string>('');

  const formatTableData = (data: any) => {
    return data.map((item: any) => {
      let status;
      if (item.status === null || item.status === 'Data not found') {
        status = 'warning';
      } else if (item.status === 'Failure') {
        status = 'error';
      } else {
        status = 'success';
      }

      return {
        id: { data: item.id },
        'Test Name': { data: item.reconTestName },
        Status: { data: status },
        detail: {
          tableName: item.reconTestName,
          name: item.viewName,
          url: item.reconTestURL,
        },
      };
    });
  };

  const fetchReconStatusTable = (
    data: any,
    name: any,
    url: any,
    tableName: any,
  ) => {
    return data.map((item: any) => {
      return {
        Year: { data: item.year },
        Month: { data: Number(item.month) > 0 ? Number(item.month) : 'N/A' },
        'Deviation Count': { data: item.deviationCount },
        Action: {
          isLink: true,
          data: 'View Insight',
          name,
          // eslint-disable-next-line prefer-template, prettier/prettier
          url: `${url}&$filter=${name}/Year eq '${item.year.toString()}' and ${name}/Month eq ${item.month.toString()}`,
          tableName,
        },
      };
    });
  };

  const getStatusTableData = async (
    id: any,
    name: any,
    urlForRecon: any,
    tableName: any,
  ) => {
    setIsWarningSelected(false);
    setIsSuccessSelected(false);
    try {
      const url = `
      ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getReconStatusTableData.url}${id}/${selectedYearForView} 
      `;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const tempTableData: any = {
              tableData: {
                headers: [
                  { data: 'Year' },
                  { data: 'Month' },
                  { data: 'Deviation Count' },
                  { data: 'Action' },
                ],
                rows: fetchReconStatusTable(
                  res.data,
                  name,
                  urlForRecon,
                  tableName,
                ).sort((a: any, b: any) => a.Month.data - b.Month.data),
              },
            };
            setSelectedTableData(tempTableData);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const fetchReconTableData = async () => {
    try {
      const url = `
      ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getReconTableData.url}${selectedYearForView} 
      `;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const tempTableData: any = {
              tableData: {
                headers: [{ data: 'Test Name' }, { data: 'Status' }],
                rows: formatTableData(res.data).sort((a: any) =>
                  a.Status.data === 'error' ? -1 : 1,
                ),
              },
            };
            setTableData(tempTableData);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    setSelectedYearForView(selectedYear);
  }, [selectedYear]);

  useEffect(() => {
    if (!isTourOpen) {
      fetchReconTableData();
    } else {
      setTableData(dataQualityTables);
    }
  }, [selectedYearForView]);

  return (
    <div className="flex h-[calc(100vh-250px)] w-full flex-col overflow-auto px-2 py-4 xl:h-[calc(100vh-210px)]">
      <div className="flex flex-col">
        <div className="mb-3 flex w-full flex-row items-center">
          <div className="w-[40%]">
            <span className=" w-full text-left font-sans text-sm font-medium capitalize text-gray-500">
              Recon Test List
            </span>
          </div>
          <div className="w-[60%] pl-3">
            {selectedTable && (
              <span className=" w-full text-left font-sans text-sm font-medium capitalize text-gray-500">
                {selectedTable}
              </span>
            )}
          </div>
        </div>
        <div className="flex h-full w-full flex-row space-x-4">
          {tableData && (
            <div className="h-full w-[40%]">
              <DataQualityTable
                selectedYear={selectedYearForView}
                headers={tableData.tableData.headers}
                rows={tableData.tableData.rows}
                onRowClick={(row: any, index: number) => {
                  if (row) {
                    setSelectedTable(row.detail.tableName);
                    localService.setItem('selectedRecon', String(index));
                    if (row.Status.data === 'error') {
                      getStatusTableData(
                        row.id.data,
                        row.detail.name,
                        row.detail.url,
                        row.detail.tableName,
                      );
                    } else if (row.Status.data === 'warning') {
                      setIsWarningSelected(true);
                    } else {
                      setIsSuccessSelected(true);
                    }
                  } else {
                    setSelectedTable('');
                    setSelectedTableData(null);
                  }
                }}
              />
            </div>
          )}
          <div className="h-full w-[60%] overflow-auto">
            {selectedTableData &&
            tableData &&
            !isWarningSelected &&
            !isSuccessSelected ? (
              <div className="flex flex-col items-center justify-start space-y-2 ">
                <SimpleTable
                  isReconTable
                  headers={selectedTableData?.tableData.headers}
                  tableRows={selectedTableData?.tableData.rows}
                />
              </div>
            ) : (
              <div className="flex h-[200px] w-full items-center justify-center">
                {tableData && !isWarningSelected && !isSuccessSelected && (
                  <span className="font-sans text-sm font-normal text-gray-400 ">
                    Please Select a table to View Data.
                  </span>
                )}
                {tableData && isWarningSelected && !isSuccessSelected && (
                  <span className="font-sans text-sm font-normal text-gray-400 ">
                    No Deviation.
                  </span>
                )}
                {tableData && !isWarningSelected && isSuccessSelected && (
                  <span className="font-sans text-sm font-normal text-gray-400 ">
                    No Data Available.
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReconTest;
