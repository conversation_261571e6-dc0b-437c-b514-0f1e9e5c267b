import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Searchbar } from './Searchbar';

const meta = {
  title: 'UI/Searchbar',
  component: Searchbar,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof Searchbar>;

export default meta;
type Story = StoryObj<typeof meta>;

const Enabled: Story = {
  args: {
    value: 'Sonika Sharan',
    placeholder: 'Placeholder',
    name: 'Item',
  },
};
const Disabled: Story = {
  args: {
    value: 'Sonika Sharan',
    placeholder: 'Placeholder',
    intent: 'disabled',
    name: 'Item',
  },
};

const HasError: Story = {
  args: {
    value: '<PERSON><PERSON> Sharan',
    placeholder: 'Placeholder',
    intent: 'hasError',
    name: 'Item',
    error: 'Error message.',
  },
};

export { Disabled, Enabled, HasError };
