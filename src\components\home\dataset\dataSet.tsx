/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import React from 'react';

import DataSetTable from '../../tables/dataset/dataSetTable';

const Dataset: React.FC = () => {
  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Datasets
        </span>
      </div>
      <div className="dataSet-table flex h-[88vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <DataSetTable />
      </div>
    </div>
  );
};

export default Dataset;
