'use client';

// Dummy data service for GenPro - returns mock data instead of API calls
export class GenProDummyService {
  // Helper to simulate API delay
  private async simulateDelay(ms: number = 1000) {
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  // File Management Methods
  async uploadFile(file: File, fileType: string, originalFileName?: string) {
    await this.simulateDelay(2000);
    return {
      data: {
        id: Math.floor(Math.random() * 1000) + 1,
        original_file_name: originalFileName || file.name,
        file_name: file.name,
        file_size: file.size,
        content_type: file.type,
        file_type: fileType,
        validation_status: 'PENDING',
        uploaded_at: new Date().toISOString(),
        file_year: new Date().getFullYear(),
        file_month: new Date().getMonth() + 1,
        s3_path: `/uploads/${fileType}/${file.name}`,
        file_metadata: {
          records_count: Math.floor(Math.random() * 1000) + 100,
          columns_count: Math.floor(Math.random() * 20) + 5,
          size_mb: (file.size / (1024 * 1024)).toFixed(2)
        }
      }
    };
  }

  async getFileList(_fileType?: string, _validationStatus?: string) {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          file_name: 'purchase_orders_2024.csv',
          file_type: 'PO',
          validation_status: 'VALID',
          uploaded_at: '2024-01-15T10:30:00Z',
          file_size: 1024000,
          records_count: 500
        },
        {
          id: 2,
          file_name: 'brokerage_overview_jan.xlsx',
          file_type: 'BF_OVERVIEW',
          validation_status: 'PENDING',
          uploaded_at: '2024-01-16T14:20:00Z',
          file_size: 2048000,
          records_count: 200
        },
        {
          id: 3,
          file_name: 'vessel_mapping_2024.csv',
          file_type: 'VESSEL_MAPPING',
          validation_status: 'INVALID',
          uploaded_at: '2024-01-17T09:15:00Z',
          file_size: 512000,
          records_count: 150
        }
      ]
    };
  }

  async validateFile(fileId: number) {
    await this.simulateDelay(3000);
    return {
      data: {
        file_id: fileId,
        validation_status: 'VALID',
        validation_results: {
          total_records: 500,
          valid_records: 485,
          invalid_records: 15,
          warnings: 23,
          errors: [
            { row: 45, column: 'amount', message: 'Invalid currency format' },
            { row: 127, column: 'date', message: 'Date format not recognized' }
          ],
          warnings_list: [
            { row: 23, column: 'description', message: 'Missing product description' },
            { row: 89, column: 'quantity', message: 'Unusual quantity value' }
          ]
        }
      }
    };
  }

  async getFileDetails(fileId: number) {
    await this.simulateDelay();
    return {
      data: {
        id: fileId,
        file_name: 'purchase_orders_2024.csv',
        file_type: 'PO',
        validation_status: 'VALID',
        uploaded_at: '2024-01-15T10:30:00Z',
        file_size: 1024000,
        records_count: 500,
        columns: ['order_id', 'product_name', 'quantity', 'price', 'vendor', 'date'],
        sample_data: [
          { order_id: 'PO-001', product_name: 'Product A', quantity: 100, price: 25.50, vendor: 'Vendor 1', date: '2024-01-15' },
          { order_id: 'PO-002', product_name: 'Product B', quantity: 50, price: 15.75, vendor: 'Vendor 2', date: '2024-01-16' }
        ]
      }
    };
  }

  async deleteFile(fileId: number) {
    await this.simulateDelay();
    return {
      data: {
        message: 'File deleted successfully',
        file_id: fileId
      }
    };
  }

  // Workflow Management Methods
  async createWorkflow(workflowName: string, automationEnabled: boolean, fileUploadIds: number[]) {
    await this.simulateDelay();
    return {
      data: {
        id: Math.floor(Math.random() * 100) + 1,
        workflow_name: workflowName,
        automation_enabled: automationEnabled,
        file_upload_ids: fileUploadIds,
        status: 'CREATED',
        created_at: new Date().toISOString(),
        current_stage: 'INGESTION'
      }
    };
  }

  async getWorkflowList() {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          workflow_name: 'Q1 2024 Processing',
          automation_enabled: true,
          status: 'COMPLETED',
          created_at: '2024-01-15T10:00:00Z',
          current_stage: 'EXPORT'
        },
        {
          id: 2,
          workflow_name: 'January Batch Processing',
          automation_enabled: false,
          status: 'IN_PROGRESS',
          created_at: '2024-01-20T14:30:00Z',
          current_stage: 'TRANSFORMATION'
        },
        {
          id: 3,
          workflow_name: 'February Data Pipeline',
          automation_enabled: true,
          status: 'IN_PROGRESS',
          created_at: '2024-02-01T09:15:00Z',
          current_stage: 'VALIDATION'
        },
        {
          id: 4,
          workflow_name: 'March Monthly Run',
          automation_enabled: false,
          status: 'PENDING',
          created_at: '2024-03-01T08:00:00Z',
          current_stage: 'INGESTION'
        }
      ]
    };
  }

  async startWorkflow(workflowId: number, automationEnabled: boolean) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        status: 'STARTED',
        automation_enabled: automationEnabled,
        started_at: new Date().toISOString(),
        current_stage: 'VALIDATION'
      }
    };
  }

  async getWorkflowStatus(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        status: 'IN_PROGRESS',
        current_stage: 'TRANSFORMATION',
        progress: 65,
        stages: {
          ingestion: { status: 'COMPLETED', completed_at: '2024-01-15T10:30:00Z' },
          validation: { status: 'COMPLETED', completed_at: '2024-01-15T11:00:00Z' },
          transformation: { status: 'IN_PROGRESS', started_at: '2024-01-15T11:30:00Z' },
          distribution: { status: 'PENDING' },
          export: { status: 'PENDING' }
        }
      }
    };
  }

  async addFilesToWorkflow(workflowId: number, fileUploadIds: number[]) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        added_files: fileUploadIds,
        message: 'Files added to workflow successfully'
      }
    };
  }

  async nextStage(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        previous_stage: 'VALIDATION',
        current_stage: 'TRANSFORMATION',
        status: 'STAGE_ADVANCED'
      }
    };
  }

  // Business Logic Methods
  async calculateBf(workflowRunId: number) {
    await this.simulateDelay(2000);
    return {
      data: {
        workflow_run_id: workflowRunId,
        calculation_status: 'COMPLETED',
        total_bf_to_distribute: 125000.50,
        bf_results: {
          total_records: 500,
          processed_records: 485,
          brokerage_amount: 125000.50,
          commission_rate: 2.5,
          total_commission: 3125.01
        }
      }
    };
  }

  async applyTransformation(workflowRunId: number) {
    await this.simulateDelay(2000);
    return {
      data: {
        workflow_run_id: workflowRunId,
        transformation_status: 'COMPLETED',
        records_processed: 485,
        transformation_results: {
          input_records: 500,
          output_records: 485,
          transformations_applied: 12,
          validation_errors: 15,
          data_quality_score: 97.0
        }
      }
    };
  }

  async generatePivotTables(workflowRunId: number) {
    await this.simulateDelay(1500);
    return {
      data: {
        workflow_run_id: workflowRunId,
        pivot1_total_amount: 750000.50,
        pivot2_total_amount: 450000.25,
        pivot_tables: [
          {
            id: 1,
            name: 'Sales by Region',
            rows: 25,
            columns: 8,
            created_at: new Date().toISOString(),
            total_amount: 750000.50
          },
          {
            id: 2,
            name: 'Product Analysis',
            rows: 15,
            columns: 6,
            created_at: new Date().toISOString(),
            total_amount: 450000.25
          }
        ]
      }
    };
  }

  async calculateDistribution(workflowRunId: number) {
    await this.simulateDelay(1500);
    return {
      data: {
        workflow_run_id: workflowRunId,
        total_distributed: 1000000.00,
        distribution_records: 485,
        distribution_results: {
          total_amount: 1000000.00,
          distributions: [
            { category: 'Region A', amount: 450000.00, percentage: 45.0 },
            { category: 'Region B', amount: 350000.00, percentage: 35.0 },
            { category: 'Region C', amount: 200000.00, percentage: 20.0 }
          ]
        }
      }
    };
  }

  // Data Management Methods
  async getVesselEntityMappings() {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          vessel_name: 'Ocean Carrier 1',
          entity_code: 'OC001',
          mapping_type: 'VESSEL_TO_ENTITY',
          is_active: true
        },
        {
          id: 2,
          vessel_name: 'Sea Transport 2',
          entity_code: 'ST002',
          mapping_type: 'VESSEL_TO_ENTITY',
          is_active: true
        }
      ]
    };
  }

  async getActiveVesselMappings() {
    await this.simulateDelay();
    return {
      data: {
        results: [
          {
            id: 1,
            vessel_name: 'Ocean Carrier 1',
            entity_code: 'OC001',
            is_active: true
          },
          {
            id: 2,
            vessel_name: 'Sea Transport 2',
            entity_code: 'ST002',
            is_active: true
          }
        ]
      }
    };
  }

  async bulkCreateVesselMappings(fileUploadId: number) {
    await this.simulateDelay();
    return {
      data: {
        file_upload_id: fileUploadId,
        created_mappings: 25,
        updated_mappings: 5,
        errors: 0
      }
    };
  }

  async getSmcList() {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          smc_code: 'SMC001',
          smc_name: 'Shipping Management Company 1',
          is_active: true
        },
        {
          id: 2,
          smc_code: 'SMC002',
          smc_name: 'Shipping Management Company 2',
          is_active: true
        }
      ]
    };
  }

  async getActiveSmcList() {
    await this.simulateDelay();
    return {
      data: {
        results: [
          {
            id: 1,
            smc_code: 'SMC001',
            smc_name: 'Shipping Management Company 1',
            is_active: true
          },
          {
            id: 2,
            smc_code: 'SMC002',
            smc_name: 'Shipping Management Company 2',
            is_active: true
          }
        ]
      }
    };
  }

  // Export Methods
  async exportResults(workflowRunId: number, exportFormat: string, _includePivotTables: boolean, _includeDistribution: boolean) {
    await this.simulateDelay(3000);
    return {
      data: {
        export_id: Math.floor(Math.random() * 1000) + 1,
        workflow_run_id: workflowRunId,
        export_format: exportFormat,
        file_url: `/exports/export_${workflowRunId}.${exportFormat}`,
        file_size: 2048000,
        created_at: new Date().toISOString(),
        status: 'COMPLETED'
      }
    };
  }

  async getExportList() {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          workflow_run_id: 1,
          export_format: 'xlsx',
          file_url: '/exports/export_1.xlsx',
          file_size: 2048000,
          created_at: '2024-01-15T15:30:00Z',
          status: 'COMPLETED'
        },
        {
          id: 2,
          workflow_run_id: 2,
          export_format: 'csv',
          file_url: '/exports/export_2.csv',
          file_size: 1024000,
          created_at: '2024-01-16T10:45:00Z',
          status: 'COMPLETED'
        }
      ]
    };
  }

  async sendExportEmail(exportId: number, recipients: string[]) {
    await this.simulateDelay();
    return {
      data: {
        export_id: exportId,
        recipients: recipients,
        sent_at: new Date().toISOString(),
        status: 'SENT'
      }
    };
  }

  async getExportHistory(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        results: [
          {
            id: 1,
            workflow_id: workflowId,
            export_format: 'xlsx',
            created_at: '2024-01-15T15:30:00Z',
            file_size: 2048000,
            status: 'COMPLETED'
          },
          {
            id: 2,
            workflow_id: workflowId,
            export_format: 'pdf',
            created_at: '2024-01-16T10:45:00Z',
            file_size: 1024000,
            status: 'COMPLETED'
          }
        ]
      }
    };
  }

  async getFinalMapping(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        results: [
          {
            source_field: 'order_id',
            target_field: 'ORDER_ID',
            transformation: 'UPPERCASE',
            validation_status: 'VALID'
          },
          {
            source_field: 'amount',
            target_field: 'AMOUNT_USD',
            transformation: 'CURRENCY_CONVERSION',
            validation_status: 'VALID'
          }
        ],
        mapping_data: [
          {
            source_field: 'order_id',
            target_field: 'ORDER_ID',
            transformation: 'UPPERCASE',
            validation_status: 'VALID'
          },
          {
            source_field: 'amount',
            target_field: 'AMOUNT_USD',
            transformation: 'CURRENCY_CONVERSION',
            validation_status: 'VALID'
          }
        ]
      }
    };
  }

  async finalizeReport(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        status: 'FINALIZED',
        finalized_at: new Date().toISOString(),
        report_url: `/reports/final_report_${workflowId}.pdf`
      }
    };
  }

  async unlockReport(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        status: 'UNLOCKED',
        unlocked_at: new Date().toISOString()
      }
    };
  }

  async exportReport(workflowId: number, options: any) {
    await this.simulateDelay(2000);
    return {
      data: {
        workflow_id: workflowId,
        export_id: Math.floor(Math.random() * 1000) + 1,
        file_url: `/exports/report_${workflowId}.${options.format || 'pdf'}`,
        download_url: `/downloads/report_${workflowId}.${options.format || 'pdf'}`,
        status: 'COMPLETED',
        created_at: new Date().toISOString()
      }
    };
  }

  async sendReportEmail(workflowId: number, emailOptions: any) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        recipients: emailOptions.recipients,
        sent_at: new Date().toISOString(),
        status: 'SENT'
      }
    };
  }

  async previewReport(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        preview_url: `/preview/report_${workflowId}.html`,
        generated_at: new Date().toISOString()
      }
    };
  }

  async completeWorkflow(workflowId: number) {
    await this.simulateDelay();
    return {
      data: {
        workflow_id: workflowId,
        status: 'COMPLETED',
        completed_at: new Date().toISOString()
      }
    };
  }

  // Analytics Methods
  async getAnalyticsSummary() {
    await this.simulateDelay();
    return {
      data: {
        total_workflows: 45,
        active_workflows: 8,
        completed_workflows: 37,
        total_files_processed: 1250,
        average_processing_time: 45.5,
        success_rate: 98.2
      }
    };
  }

  async getAnalyticsTrends() {
    await this.simulateDelay();
    return {
      data: {
        monthly_trends: [
          { month: 'Jan', workflows: 12, files: 345, success_rate: 97.5 },
          { month: 'Feb', workflows: 15, files: 423, success_rate: 98.1 },
          { month: 'Mar', workflows: 18, files: 482, success_rate: 98.8 }
        ]
      }
    };
  }

  async getDashboardAnalytics() {
    await this.simulateDelay();
    return {
      data: {
        total_workflows: 45,
        active_workflows: 8,
        completed_workflows: 37,
        failed_workflows: 0,
        total_files: 30,
        processed_files: 1225,
        failed_files: 25,
        processing_time_avg: 45.5,
        success_rate: 98.0,
        completed_today: 2,
        pending_validation: 5,
        total_processed: 30,
        avg_processing_time: '14.5 minutes',
        recent_activity: [
          {
            id: 1,
            type: 'WORKFLOW_COMPLETED',
            workflow_name: 'Q1 Processing',
            timestamp: '2024-01-15T14:30:00Z'
          },
          {
            id: 2,
            type: 'FILE_UPLOADED',
            file_name: 'january_data.csv',
            timestamp: '2024-01-15T13:45:00Z'
          }
        ]
      }
    };
  }

  async getRecentActivity() {
    await this.simulateDelay();
    return {
      data: {
        results: [
          {
            id: 1,
            type: 'WORKFLOW_COMPLETED',
            message: 'Q1 Processing workflow completed successfully',
            time: '2024-01-15T14:30:00Z',
            status: 'success' as const
          },
          {
            id: 2,
            type: 'FILE_UPLOADED',
            message: 'january_data.csv uploaded and validated',
            time: '2024-01-15T13:45:00Z',
            status: 'success' as const
          },
          {
            id: 3,
            type: 'VALIDATION_COMPLETED',
            message: 'February Batch validation completed with warnings',
            time: '2024-01-15T12:15:00Z',
            status: 'warning' as const
          },
          {
            id: 4,
            type: 'TRANSFORMATION_STARTED',
            message: 'Data transformation started for March processing',
            time: '2024-01-15T11:30:00Z',
            status: 'success' as const
          },
          {
            id: 5,
            type: 'EXPORT_FAILED',
            message: 'Export process failed for April data - retrying',
            time: '2024-01-15T10:45:00Z',
            status: 'error' as const
          }
        ]
      }
    };
  }

  async getSystemStatus() {
    await this.simulateDelay();
    return {
      data: {
        status: 'HEALTHY',
        uptime: '99.9%',
        last_maintenance: '2024-01-01T02:00:00Z',
        database_status: 'online',
        storage_status: 'online',
        email_status: 'online',
        services: {
          api: 'HEALTHY',
          database: 'HEALTHY',
          file_storage: 'HEALTHY',
          processing_queue: 'HEALTHY'
        },
        performance: {
          response_time: '150ms',
          throughput: '1000 req/min',
          error_rate: '0.1%'
        }
      }
    };
  }

  // System Configuration Methods
  async getSystemConfig() {
    await this.simulateDelay();
    return {
      data: {
        max_file_size: 100000000,
        supported_formats: ['csv', 'xlsx', 'xls'],
        processing_timeout: 3600,
        max_concurrent_workflows: 10,
        retention_days: 90
      }
    };
  }

  // Audit Log Methods
  async getAuditLogs() {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          action: 'WORKFLOW_CREATED',
          user: '<EMAIL>',
          timestamp: '2024-01-15T10:00:00Z',
          details: { workflow_name: 'Q1 Processing' }
        },
        {
          id: 2,
          action: 'FILE_UPLOADED',
          user: '<EMAIL>',
          timestamp: '2024-01-15T10:30:00Z',
          details: { file_name: 'data.csv', file_size: 1024000 }
        }
      ]
    };
  }

  async getAuditLogsByWorkflow(workflowRunId: number) {
    await this.simulateDelay();
    return {
      data: [
        {
          id: 1,
          workflow_run_id: workflowRunId,
          action: 'VALIDATION_STARTED',
          timestamp: '2024-01-15T10:30:00Z',
          details: { stage: 'VALIDATION' }
        },
        {
          id: 2,
          workflow_run_id: workflowRunId,
          action: 'VALIDATION_COMPLETED',
          timestamp: '2024-01-15T11:00:00Z',
          details: { stage: 'VALIDATION', status: 'SUCCESS' }
        }
      ]
    };
  }

  // Helper method to determine file type from filename
  static getFileTypeFromName(filename: string): string {
    const name = filename.toLowerCase();
    
    if (name.includes('po') || name.includes('purchase_order')) {
      return 'PO';
    } else if (name.includes('bf') || name.includes('brokerage')) {
      return 'BF_OVERVIEW';
    } else if (name.includes('expense')) {
      return 'EXPENSE';
    } else if (name.includes('vessel') || name.includes('mapping')) {
      return 'VESSEL_MAPPING';
    } else if (name.includes('template')) {
      return 'IMPORT_TEMPLATE';
    }
    
    return 'PO'; // Default to PO if can't determine
  }
}