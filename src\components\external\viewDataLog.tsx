'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState } from 'react';

import { Searchbar } from '@/components/ui/Searchbar';
import Table from '@/components/ui/Table';
import { TestDataService } from '@/service/testdata.service';

import Avatar from '../ui/Avatar';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { UploadDetails } from './uploadDetails';

const ViewDataLog: React.FC = () => {
  // Essentials
  const testDataService = new TestDataService();

  // Constants
  const menuData: any = [
    {
      option: 'Mark as Favourite',
    },
    {
      option: 'Edit',
    },
    {
      option: 'Delete',
    },
  ];
  const header: any = [
    {
      title: 'Name',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'ENTRY ID',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Template Category',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Uploaded On',
      optionsEnabled: true,
      options: [],
    },
  ];

  // States
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [modalData, setPopupModalData] = useState('');
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    // const filteredValues = filteredData.values.filter((row: any) =>
    //   row.components.some((cell: any) => {
    //     if (typeof cell.value === 'string') {
    //       return cell.value.toLowerCase().includes(value.toLowerCase());
    //     }
    //     return false;
    //   }),
    // );
    // setFilteredData(filteredValues);
  };

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const openUploadPopup = () => {
    setIsPopupOpen(true);
    setPopupModalData('Upload file');
  };

  const closePopupModal = () => {
    setIsPopupOpen(false);
    setPopupModalData('');
  };

  // Effects
  useEffect(() => {
    // dispatch(setIsLoading(true));
    const fetchData = async () => {
      // const url = `${
      //   ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
      // }/${selectedDataset.id}/tables`;
      // apiService
      //   .getRequest(url)
      //   .then((res) => {
      //     dispatch(setIsLoading(false));
      //     setTableData(res.data);
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //     dispatch(setIsLoading(false));
      //   });
      const response = await testDataService.getAllTestDataFromFile(
        'dataCollaboration/dataset',
      );
      setFilteredData(response);
    };
    fetchData();
  }, []);

  return (
    <div className="flex w-full flex-col space-y-4">
      {/* <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Data Log
        </span>
      </div> */}
      <div className="flex h-full w-full flex-col bg-white-200 p-4 py-6">
        <div className="flex h-[96px] justify-between bg-blue-50">
          <div className="flex">
            <div className="relative flex h-full items-center justify-start px-6">
              <Avatar name="A" />
              <div className="absolute left-14 z-10 rounded-full border-2 border-blue-500 bg-white-100">
                <Avatar name="B" />
              </div>
            </div>
            <div className="flex flex-col justify-center border-b-[0.5px] py-2 pl-6">
              <span className="font-sans text-base font-semibold text-blueGray-300">
                Nike_Walmart_Data
              </span>
              <span className="font-sans text-sm font-normal text-gray-500">
                <EMAIL>
              </span>
              <span className="font-sans text-xs font-normal text-gray-400">
                Updated on 23 Sep 2023 15:54:26
              </span>
            </div>
          </div>
          <div className="flex items-center px-6">
            <Button
              // disabled={disablePrimaryFooterBtn}
              type="button"
              // onClick={footerPrimarySubmitHandler}
              className="ml-4 bg-white-200"
              intent="secondary"
            >
              Download Templates
            </Button>
            <Button
              // disabled={disablePrimaryFooterBtn}
              type="button"
              onClick={openUploadPopup}
              className="ml-4"
            >
              Upload File
            </Button>
          </div>
        </div>
        <div className="mb-4 mt-8 w-[20vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        <div className="h-full w-full overflow-auto" onScroll={handleScroll}>
          <Table
            isDashTable
            menuData={menuData}
            header={header}
            data={filteredData}
            scrollPosition={scrollPosition}
            enableOptions
            isDownloadVisible
            isView
          />
        </div>
      </div>
      <Modal
        isOpen={isPopupOpen}
        headerTitle={modalData}
        component={<UploadDetails />}
        closeModal={closePopupModal}
        panelWidth="w-[50vw]"
        isActionButtonVisible
        actionbuttonText="Upload"
      />
    </div>
  );
};

export default ViewDataLog;
