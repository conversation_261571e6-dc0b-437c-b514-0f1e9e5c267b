[{"business_entity_id": 103, "business_entity_name": "GL_ACCOUNTING_VIEW", "business_entity_description": null, "is_default": true, "join_config": {"0": {"join_type": "LEFT JOIN", "join_sequence": 8, "child_table_id": "FACT_AP_ACCOUNTING_DOCUMENT_LINES", "join_condition": "", "parent_table_id": "FACT_GL_ACCOUNTING_DOCUMENT_LINES", "where_condition": "", "child_attribute_id": "DOC_LINE_ITEM", "parent_attribute_id": "DOC_LINE_ITEM"}, "1": {"join_type": "LEFT JOIN", "join_sequence": 12, "child_table_id": "DIM_DISPUTE_REASON", "join_condition": "", "parent_table_id": "FACT_DISPUTE_CASE_DETAILS", "where_condition": "", "child_attribute_id": "DISPUTE_REASON_CODE", "parent_attribute_id": "DISPUTE_REASON_CODE"}, "2": {"join_type": "LEFT JOIN", "join_sequence": 12, "child_table_id": "DIM_DISPUTE_REASON", "join_condition": "", "parent_table_id": "FACT_DISPUTE_CASE_DETAILS", "where_condition": "", "child_attribute_id": "CASE_TYPE", "parent_attribute_id": "CASE_TYPE"}}, "updated_at": null, "updated_by": null, "attribute_count": 83, "status": "true"}, {"business_entity_id": 107, "business_entity_name": "AR_ACCOUNTING_VIEW_1", "business_entity_description": null, "join_config": {}, "updated_at": null, "updated_by": null, "attribute_count": 89, "status": "true"}, {"business_entity_id": 105, "business_entity_name": "AP_LOGISTICS_VIEW", "business_entity_description": null, "join_config": {}, "updated_at": null, "updated_by": null, "is_default": false, "attribute_count": 129, "status": "true"}, {"business_entity_id": 106, "business_entity_name": "AP_ACCOUNTING_VIEW_2", "business_entity_description": null, "join_config": {}, "updated_at": null, "updated_by": null, "attribute_count": 91, "status": "true"}, {"business_entity_id": 104, "business_entity_name": "AR_BILLING_VIEW", "business_entity_description": null, "join_config": {}, "updated_at": null, "updated_by": null, "attribute_count": 82, "status": "true"}, {"business_entity_id": 108, "business_entity_name": "GL_PAY_HEADER", "business_entity_description": "GL_PAY_HEADER", "join_config": {"0": {"join_type": "Left Join", "child_table": "DIM_COMPANY", "parent_table": "DIM_BANK_ACCOUNT", "join_condition": "", "child_attribute": "LAST_UPDATE_DATE", "parent_attribute": "HOUSE_BANK_CODE"}}, "updated_at": "2024-02-22 08:24:27", "updated_by": "None", "attribute_count": 2, "status": "RUNNING"}]