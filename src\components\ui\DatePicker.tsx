/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable tailwindcss/no-custom-classname */
import '@wojtekmaj/react-daterange-picker/dist/DateRangePicker.css';
import 'react-calendar/dist/Calendar.css';

import DateRangePicker from '@wojtekmaj/react-daterange-picker';
import React, { useState } from 'react';

type ValuePiece = Date | null;

type Value = ValuePiece | [ValuePiece, ValuePiece];

const DateRangeInput: React.FC = () => {
  const [value, onChange] = useState<Value>([new Date(), new Date()]);

  const calendarIcon = () => (
    <img src="/assets/images/calander.svg" alt="Custom Calendar" />
  );

  return (
    <div className="dateRange">
      <DateRangePicker
        calendarClassName="DatePicker"
        calendarIcon={calendarIcon}
        onChange={onChange}
        value={value}
        format="dd-MM-yyyy"
        dayPlaceholder="dd"
        monthPlaceholder="mm"
        yearPlaceholder="yyyy"
        rangeDivider="to"
      />
    </div>
  );
};

export default DateRangeInput;
