/* eslint-disable no-param-reassign */
import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface LogicalEntityCrudState {
  logicalEntityTableFetched: boolean;
  logicalEntityName: any;
  activatedAttributes: any;
  selectedDatasets: any;
  mappedAttributes: any;
  customAttributes: any;
  joinConfig: any;
  tableAttributeMap: any;
  checkedTableNames: any;
}

const initialState: LogicalEntityCrudState = {
  logicalEntityTableFetched: false,
  logicalEntityName: '',
  activatedAttributes: {},
  selectedDatasets: [],
  mappedAttributes: {},
  customAttributes: {},
  joinConfig: {},
  tableAttributeMap: {},
  checkedTableNames: {},
};

export const logicalEntityCrudSlice = createSlice({
  name: 'logicalEntityCrud',
  initialState,
  reducers: {
    setLogicalEntityTableFetched: (state, action) => {
      state.logicalEntityTableFetched = action.payload;
    },
    setLogicalEntityName: (state, action) => {
      state.logicalEntityName = action.payload;
    },
    setActivatedAttributes: (state, action) => {
      state.activatedAttributes = action.payload;
    },
    setCheckedTableNames: (state, action) => {
      state.checkedTableNames = action.payload;
    },
    setSelectedDatasets: (state, action) => {
      state.selectedDatasets = action.payload;
    },
    setMappedAttributes: (state, action) => {
      state.mappedAttributes = action.payload;
    },
    appendMappedAttributes: (state, action) => {
      const newState = {
        ...state,
        mappedAttributes: {
          ...state.mappedAttributes,
          ...action.payload,
        },
      };
      return newState;
    },
    setCustomAttributes: (state, action) => {
      state.customAttributes = action.payload;
    },
    setJoinConfig: (state, action) => {
      state.joinConfig = action.payload;
    },
    setTableAttributeMap: (state, action) => {
      state.tableAttributeMap = action.payload;
    },
    resetState: () => {
      return initialState;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setLogicalEntityName,
  setActivatedAttributes,
  setSelectedDatasets,
  setMappedAttributes,
  setCustomAttributes,
  setJoinConfig,
  setTableAttributeMap,
  setLogicalEntityTableFetched,
  resetState,
  setCheckedTableNames,
  appendMappedAttributes,
} = logicalEntityCrudSlice.actions;

export default logicalEntityCrudSlice.reducer;
