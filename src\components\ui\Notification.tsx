/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { crossIcon, notificationIcon } from '@/constants/menuSvg';
import { setNotificationAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

const notification = cva(
  'flex w-[488px] flex-col rounded-lg border-[1px] px-4 py-3 font-sans',
  {
    variants: {
      intent: {
        success: ['border-green-100', 'bg-green-100/10', 'text-green-100'],
        error: ['border-red-100', 'bg-red-300/10', 'text-red-100'],
        warning: ['border-yellow-100', 'bg-yellow-10/10', 'text-yellow-100'],
        info: ['border-blue-100', 'bg-blue-100/10', 'text-blue-100'],
      },
    },
    compoundVariants: [
      {
        intent: 'success',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'success',
    },
  },
);

export interface NotificationProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof notification> {}

export const Notification: React.FC<NotificationProps> = ({
  className,
  ...props
}) => {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const notificationState = useSelector(
    (state: RootState) => state.metadata.notificationAlert,
  );

  // Constants
  const { intent } = notificationState;

  const getColor = () => {
    if (intent === 'success') {
      return '#1CA675';
    }
    if (intent === 'error') {
      return '#E5333E';
    }
    if (intent === 'info') {
      return '#00AAE7';
    }
    return '#00AAE7';
  };

  const [selectedColor, setSelectedColor] = useState<string>(getColor);

  // Methods
  const closeNotification = () => {
    if (notificationState.isNotificationOpen) {
      dispatch(
        setNotificationAlert({
          isNotificationOpen: false,
          intent: '',
          title: '',
          content: '',
        }),
      );
    }
  };

  // Effects
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (notificationState.isNotificationOpen) {
      timeoutId = setTimeout(() => {
        closeNotification();
      }, 3000);
    }
    return () => clearTimeout(timeoutId);
  }, [notificationState.isNotificationOpen]);

  useEffect(() => {
    setSelectedColor(getColor);
  }, [intent]);

  return (
    <div
      className={` z-50 ${
        notificationState.isNotificationOpen
          ? ' fixed right-4 top-3 flex rounded-lg bg-white-200'
          : 'hidden'
      }`}
    >
      <div className={notification({ intent, className })} {...props}>
        <div className="flex flex-row items-center justify-between pb-[8px]">
          <div className="flex flex-row items-center space-x-2">
            <div
              dangerouslySetInnerHTML={{
                __html: notificationIcon(selectedColor),
              }}
            />
            <span className="font-sans text-sm font-semibold leading-6 ">
              {notificationState.title}
            </span>
          </div>
          <div
            onClick={closeNotification}
            className="ml-auto cursor-pointer"
            dangerouslySetInnerHTML={{ __html: crossIcon(selectedColor) }}
          />
        </div>
        <div className="pl-8 font-sans text-sm font-normal leading-5 text-gray-400">
          {notificationState.content}
        </div>
      </div>
    </div>
  );
};
