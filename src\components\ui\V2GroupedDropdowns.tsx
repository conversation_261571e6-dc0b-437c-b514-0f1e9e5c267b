import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListSubheader from '@mui/material/ListSubheader';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { Box } from '@mui/material';

interface V2ChatCompProps {
  options: any;
  placeholder?: string;
  noneOption?: boolean;
  disabled?: boolean;
  onChange?: (e: any) => void;
  multiSelect?: boolean;
  selectedOptions?: string[];
  value?: string;
  boxStyle?: string;
}

const V2GroupedDropdowns: React.FC<V2ChatCompProps> = ({
  // const V2GroupedDropdowns: React.FC<any> = ({
  options,
  noneOption,
  placeholder,
  disabled = false,
  value,
  multiSelect = false,
  selectedOptions,
  onChange,
  boxStyle,
}) => {
  return (
    <div>
      <Box sx={{ minWidth: 0, width: '100%' }}>
        <FormControl className="select-core w-full">
          <Select
            id="grouped-select"
            value={value}
            onChange={onChange}
            label=""
            defaultValue=""
            className={boxStyle}
            disabled={disabled}
            variant="outlined"
            IconComponent={() => (
              <img
                alt="select-box"
                src="/assets/images/arrow-grey.svg"
                style={{ height: '6px', paddingRight: 19, cursor: 'pointer' }}
              />
            )}
            displayEmpty
            MenuProps={{ MenuListProps: { disablePadding: true } }}
            inputProps={{ 'aria-label': 'Without label' }}
            renderValue={(selected: string) => {
              if (!selected) {
                return (
                  <div className="text-xs font-normal leading-4 text-gray-400">
                    {placeholder}
                  </div>
                );
              }
              return selected;
            }}
          >
            {noneOption && (
              <MenuItem
                sx={{
                  color: '#7B7C7D',
                  '&:hover': {
                    backgroundColor: 'rgba(47, 128, 237, 0.1)',
                    color: '#2F80ED',
                  },
                  '&:focus': {
                    backgroundColor: 'rgba(47, 128, 237, 0.1)',
                    color: '#2F80ED',
                  },
                  fontSize: 12,
                  fontWeight: 400,
                  lineHeight: '1rem',
                }}
                value=""
                disabled
                selected
              >
                <em>Select Model Driver</em>
              </MenuItem>
            )}
            {Object.keys(options).map((opt: any) => {
              const items = options[opt].map((subOpt: any) => {
                return multiSelect ? (
                  <MenuItem
                    value={subOpt.title}
                    className={`text-xs font-normal leading-4 ${
                      selectedOptions?.includes(subOpt.title)
                        ? 'bg-blue-500 text-blue-200'
                        : 'text-gray-400'
                    }`}
                  >
                    <span className="pl-10 text-xs">{subOpt.title}</span>
                  </MenuItem>
                ) : (
                  <MenuItem
                    value={subOpt.title}
                    className="text-xs font-normal leading-4 text-gray-400"
                  >
                    <span className="pl-10 text-xs text-gray-400">
                      {subOpt.title}
                    </span>
                  </MenuItem>
                );
              });
              return [
                <ListSubheader className="h-[36px] flex items-center">
                  {opt}
                </ListSubheader>,
                items,
              ];
            })}
          </Select>
        </FormControl>
      </Box>
    </div>
  );
};

export default V2GroupedDropdowns;
