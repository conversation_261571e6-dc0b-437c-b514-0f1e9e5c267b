/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';

import ListBoxTable from '@/components/ui/ListBoxTable';
import { Searchbar } from '@/components/ui/Searchbar';

const ConfigurationModal: React.FC<any> = ({ overallData, favItems }) => {
  const [favourite, setFavourite] = useState(favItems);
  const [allData, setAllData] = useState(overallData);
  const [error, setError] = useState(false);
  const selectList = [{ name: 'All' }];
  const [searchData, setSearchData] = useState('');
  const [filteredData, setFilteredData] = useState(allData);
  const search = (query: any) => {
    if (!query.trim()) {
      return [];
    }
    const results = allData.filter((item: any) =>
      item.name.toLowerCase().includes(query.toLowerCase()),
    );
    return results;
  };

  const handleItemSearch = (value: string) => {
    setSearchData(value);
    if (value) {
      const filteredValues = search(value);
      setFilteredData(filteredValues);
    } else {
      const filteredValues = allData;
      setFilteredData(filteredValues);
    }
  };

  const addItem = (data: any) => {
    if (favourite.length >= 0 && favourite.length < 10) {
      if (!favourite.some((item: any) => item.name === data.name)) {
        setError(false);
        setFavourite((prevFavourites: any) => [...prevFavourites, data]);
        setAllData((prevFavourites: any) =>
          prevFavourites.filter((item: any) => item.name !== data.name),
        );
      }
    } else {
      setError(true);
    }
  };

  const removeItem = (data: any) => {
    if (!allData.some((item: any) => item.name === data.name)) {
      setAllData((prevFavourites: any) => [...prevFavourites, data]);
    }
    setFavourite((prevFavourites: any) =>
      prevFavourites.filter((item: any) => item.name !== data.name),
    );
    if (favourite.length <= 10) {
      setError(false);
    }
  };

  useEffect(() => {
    setSearchData('');
    setFilteredData(allData);
  }, [allData]);

  return (
    <div className="flex h-full w-full flex-row space-x-4 bg-gray-300">
      <div className="flex h-full w-full flex-col  rounded bg-white-200 px-4 pb-4 pt-2">
        <div className="flex flex-row items-center justify-between pb-3 pt-2">
          <span className="font-sans text-base font-semibold text-blueGray-300">
            Overall Items
          </span>
          <div className="flex flex-row items-center space-x-[4px]">
            <span className=" font-sans text-sm font-normal text-gray-500">
              Show
            </span>
            <div className="w-[60px]">
              <ListBoxTable
                placeholder="Select"
                isInTable
                items={selectList}
                selectedValue="All"
                hasValue
                onSelectionChange={(selectedValue) => {
                  console.log(selectedValue);
                }}
                name="select"
              />
            </div>
          </div>
        </div>
        <div className="w-full pb-1">
          {' '}
          <Searchbar
            value={searchData}
            placeholder="Search"
            onChange={(e) => handleItemSearch(e.target.value)}
          />
        </div>
        <div className="h-[80%] overflow-auto">
          {allData.length > 0 ? (
            <div className="flex flex-col space-y-1">
              {filteredData.map((dashboard: any, rowIndex: any) => (
                <button
                  key={rowIndex}
                  type="button"
                  onClick={() => {
                    addItem(dashboard);
                  }}
                  className="flex h-[44px] w-full cursor-pointer flex-row items-center justify-between rounded-sm bg-slate-100 px-4 font-sans text-sm font-semibold  text-blue-200"
                >
                  <div className="flex flex-row items-center space-x-2 text-left">
                    {' '}
                    {dashboard.icon && (
                      <img
                        className="mr-2 h-[24px] w-[24px]"
                        src={`/assets/images/${dashboard.icon}`}
                        alt={dashboard.icon}
                      />
                    )}
                    {dashboard.name}
                  </div>
                  <div>
                    {' '}
                    <img
                      className="h-[15px] w-[15px]"
                      src="/assets/images/add.svg"
                      alt="add"
                    />
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="flex h-full flex-col items-center justify-center">
              <img src="/assets/images/empty.svg" alt="No data" />

              <span className="font-sans text-sm font-normal text-gray-400 ">
                No Dashboard Available
              </span>
            </div>
          )}
        </div>
      </div>
      <div className="flex h-full w-full flex-col overflow-auto rounded bg-white-200 px-4 pb-4 pt-2">
        <div className="flex flex-row items-center justify-between pb-4 pt-2">
          <span className="font-sans text-base font-semibold text-blueGray-300">
            Favourite Items
          </span>

          <span
            className={`font-sans  text-xxxs font-normal ${
              error ? 'text-red-200' : 'text-gray-400'
            }`}
          >
            Max 10 Items only
          </span>
        </div>

        <div className="h-[90%] overflow-auto">
          {favourite.length > 0 ? (
            <div className="flex flex-col space-y-1">
              {favourite.map((dashboard: any, rowIndex: any) => (
                <button
                  key={rowIndex}
                  type="button"
                  onClick={() => {
                    removeItem(dashboard);
                  }}
                  className="flex h-[44px] w-full cursor-pointer flex-row items-center justify-between rounded-sm bg-slate-100 px-4 font-sans text-sm font-semibold  text-blue-200"
                >
                  <div className="flex w-[90%] flex-row items-center space-x-2 text-left">
                    {' '}
                    {dashboard.icon && (
                      <img
                        className="mr-2 h-[24px] w-[24px]"
                        src={`/assets/images/${dashboard.icon}`}
                        alt={dashboard.icon}
                      />
                    )}
                    {dashboard.name}
                  </div>
                  <div>
                    {' '}
                    <img
                      className="h-[24px] w-[24px]"
                      src="/assets/images/remove.svg"
                      alt="remove"
                    />
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="flex h-full flex-col items-center justify-center">
              <img src="/assets/images/empty.svg" alt="No data" />

              <span className="font-sans text-sm font-normal text-gray-400 ">
                No Favourites added yet
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ConfigurationModal;
