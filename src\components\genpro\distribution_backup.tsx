'use client';

import React from 'react';
import {
  Pie<PERSON><PERSON>
} from 'lucide-react';

import { useGenProWorkflow } from '@/contexts/GenProWorkflowContext';

interface DistributionProps {
  onNext?: () => void;
  onPrevious?: () => void;
}

const Distribution = ({ onNext: _onNext, onPrevious: _onPrevious }: DistributionProps) => {
  const { currentWorkflow } = useGenProWorkflow();

  return (
    <div className="flex flex-col h-full p-6">
      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto pr-2">
        {/* No Workflow State */}
        {!currentWorkflow?.id && (
          <div className="text-center py-12">
            <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Workflow</h3>
            <p className="text-gray-500">Please start from the Ingestion step to create a workflow</p>
          </div>
        )}

        {/* Basic content for testing */}
        {currentWorkflow?.id && (
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-800">Distribution & Pivot Tables</h3>
            <p className="text-sm text-gray-600">This is a backup/alternate version of the distribution component.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Distribution;