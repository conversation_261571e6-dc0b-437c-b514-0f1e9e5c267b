/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */
// /* eslint-disable no-param-reassign */
// /* eslint-disable import/no-extraneous-dependencies */

'use client';

import 'reactflow/dist/style.css';
import './flow.scss';

import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import type { Connection, Edge } from 'reactflow';
import ReactFlow, {
  addEdge,
  Background,
  Controls,
  MiniMap,
  useEdgesState,
  useNodesState,
} from 'reactflow';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { FLOW } from './constants';
import ParentGrpNode from './parentGrpNode';
import SubGrpNode from './subGrpNode';
import TableGrpNode from './tableGrpNode';

const nodeTypes = {
  table: TableGrpNode,
  subgroup: SubGrpNode,
  parentGroup: ParentGrpNode,
};

const initialNodes: any = [];
const initialEdges: any = [];

const NestedFlow = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const searchParams = useSearchParams();
  const [flowData, setflowData] = useState({
    raw: {},
    enrich: {
      tables: {},
    },
    consume: {
      tables: {},
    },
  });
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [readyFlag, setReadyFlag] = useState(false);
  const proOptions = { hideAttribution: true };
  const tablesPerRow = 4;

  const id: any = searchParams?.get('id');

  const getSubGroupHeight = (tableHeightArray: number[]) => {
    let subGroupHeight = 0;
    for (let i = 0; i < tableHeightArray.length; i += tablesPerRow) {
      const rowArr = tableHeightArray.slice(i, i + tablesPerRow);
      const maxHeightTableInRow = Math.max(...rowArr);
      subGroupHeight += maxHeightTableInRow;
    }
    return subGroupHeight;
  };

  const getNodeWidthForTable = (columns: any) => {
    const largestCol = columns.reduce((a: any, b: any) =>
      a.length > b.length ? a : b,
    );
    return largestCol.length * 4 + 10;
  };

  const getTableWidth = (maxColWidth: number, header: any) => {
    if (header.length * 4 > maxColWidth) {
      return header.length * 4 + 10;
    }
    return maxColWidth + 10;
  };

  const getHeightTillPreviousRow = (
    tableHeightArray: number[],
    row: number,
  ) => {
    let heightTillPreviousRow = 20;
    for (let rowIndex = row; rowIndex >= 0; rowIndex--) {
      let maxHeightTableInRow = 0;
      const tableHeightsArrForPreviousRow = tableHeightArray.splice(
        rowIndex * tablesPerRow,
        tablesPerRow,
      );
      maxHeightTableInRow = Math.max(...tableHeightsArrForPreviousRow);
      heightTillPreviousRow += maxHeightTableInRow;
    }
    return heightTillPreviousRow;
  };

  const getHeightTillPreviousSubGroup = (subGroupsHeightArray: any) => {
    const numberOfSubgroups = subGroupsHeightArray.length;
    let subgroupsHeight = 0;
    for (let i = 0; i < numberOfSubgroups - 1; i++) {
      subgroupsHeight += subGroupsHeightArray[i];
    }
    return subgroupsHeight + numberOfSubgroups * 15;
  };

  const getGroupInfo = (flow: any) => {
    const groupInfo: any = {
      id: flow.id,
      data: { label: flow.label },
      position: { x: flow.xPos, y: flow.yPos },
      className: flow.classNames,
      style: {
        backgroundColor: flow.bgColor,
        width: flow.minWidth,
        height: flow.minHeight,
        zIndex: flow.zIndex,
      },
      type: 'parentGroup',
    };
    return groupInfo;
  };

  const generateNodesAndGroup = (
    flow: any,
    data: any,
    title: any,
    source: any,
    prevWidth: any,
  ) => {
    const nodesAndGroups: any = [];
    const grpEdges: any = [];
    const groupInfo = getGroupInfo(flow);
    const grpFlowData = data;
    const tablesData = grpFlowData.tables;
    const tables = Object.keys(tablesData);
    let maxCols = -1;
    const tableHeightArray: number[] = [];
    let tableRowWidth = flow.initialSubGrpWidth;
    let subGroupWidth = flow.initialSubGrpWidth;

    for (const table of tables) {
      const tableCols = Object.keys(tablesData[table]);
      const nodeWidth = getNodeWidthForTable(tableCols);
      const tableWidth = getTableWidth(nodeWidth, table);
      const tableInfo = {
        id: `${title}-table-${table}`,
        data: { label: table },
        position: { x: 15, y: 30 },
        className: 'table',
        style: {
          width: tableWidth,
          height: flow.datasetMinHeight,
          zIndex: flow.datasetZIndex,
          backgroundColor: flow.tableColor,
          fontSize: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        },
        parentNode: flow.id,
        extent: 'parent',
        type: 'table',
      };

      const columnsData = tablesData[table];
      const columns = Object.keys(columnsData);
      const numberOfColumns = columns.length;
      maxCols = Math.max(maxCols, numberOfColumns);

      for (const column of columns) {
        const columnInfo = {
          id: `${title}-${table}-${column}`,
          data: { label: columnsData[column].col_name },
          position: { x: 3, y: 10 * (columns.indexOf(column) + 1) },
          className: 'col',
          style: {
            backgroundColor: flow.tableColor,
            height: 10,
            paddingLeft: 5,
            paddingRight: 5,
            width: nodeWidth,
            zIndex: 1,
          },
          type: title === 'consume' ? 'output' : null,
          sourcePosition: title === 'enrich' ? 'right' : null,
          targetPosition: 'left',
          parentNode: `${title}-table-${table}`,
          extent: 'parent',
        };
        nodesAndGroups.push(columnInfo);

        for (const columnEdgeInfo of columnsData[column].connection_info) {
          const sourceId =
            title === 'enrich'
              ? `raw-${columnEdgeInfo.dataset_name}-${columnEdgeInfo.table_name}-${columnEdgeInfo.col_name}`
              : `${source}-${columnEdgeInfo.logical_entity_name}-${columnEdgeInfo.col_name}`;
          const targetId = `${title}-${table}-${column}`;
          grpEdges.push({
            id: `${sourceId}-${targetId}`,
            source: sourceId,
            target: targetId,
          });
        }
      }

      const tableRow = Math.floor(tables.indexOf(table) / tablesPerRow);
      const tableHeight = 10 * (numberOfColumns + 1) + 5;
      if (
        tables.indexOf(table) !== 0 &&
        tables.indexOf(table) % tablesPerRow !== 0
      ) {
        tableRowWidth += tableWidth + 10;
      } else {
        tableRowWidth = flow.initialSubGrpWidth + tableWidth + 10;
      }
      tableHeightArray.push(tableHeight);
      tableInfo.style.height = tableHeight;
      tableInfo.position.x =
        tableRowWidth - tableWidth - flow.initialSubGrpWidth;
      const heightTillPreviousRow = getHeightTillPreviousRow(
        [...tableHeightArray],
        tableRow - 1,
      );
      tableInfo.position.y = heightTillPreviousRow + 20 * (tableRow + 1);
      nodesAndGroups.push(tableInfo);
      subGroupWidth = Math.max(subGroupWidth, tableRowWidth);
    }

    const allTablesHeight = getSubGroupHeight([...tableHeightArray]);
    groupInfo.style.height =
      allTablesHeight + 20 + 30 * Math.ceil(tables.length / tablesPerRow);
    groupInfo.style.width = subGroupWidth;
    groupInfo.position.x = prevWidth + 200;
    nodesAndGroups.push(groupInfo);
    return { nodes: nodesAndGroups, edges: grpEdges };
  };

  const generateRawNodesAndGroups = () => {
    const rawNodesAndGroups: any = [];
    const rawGroupInfo = getGroupInfo(FLOW.RAW);
    const rawFlowData: any = flowData?.raw;
    const subGroups = Object.keys(rawFlowData);
    const subGroupsHeightArray: any = [];
    let totalSubGroupsHeight = 0;
    let totalSubgroupWidth = 0;
    for (let subGrpIndex = 0; subGrpIndex < subGroups.length; subGrpIndex++) {
      // adding subgroup
      const subgrp: any = subGroups[subGrpIndex] as keyof typeof rawFlowData;
      const subGrpInfo = {
        id: `raw-subgrp-${subgrp}`,
        data: { label: subgrp },
        position: { x: 15, y: 15 },
        className: 'subgroup',
        style: {
          backgroundColor: subgrp.toLowerCase().includes('sap')
            ? FLOW.RAW.sapBgColor
            : FLOW.RAW.oracleBgColor,
          width: FLOW.RAW.datasetMinWidth,
          height: FLOW.RAW.datasetMinHeight,
          zIndex: FLOW.RAW.datasetZIndex,
        },
        parentNode: FLOW.RAW.id,
        extent: 'parent',
        type: 'subgroup',
      };
      // adding table
      const tablesData: any = rawFlowData[subgrp]?.tables;
      const tables = Object.keys(tablesData);
      let maxCols = -1;
      let tableRowWidth = FLOW.RAW.initialSubGrpWidth;
      let subGroupWidth = FLOW.RAW.initialSubGrpWidth;
      const tableHeightArray: number[] = [];
      for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
        const table: any = tables[tableIndex] as keyof typeof tablesData;
        const tableCols = Object.keys(tablesData[table]);
        const nodeWidth = getNodeWidthForTable(tableCols);
        const tableWidth = getTableWidth(nodeWidth, table);
        const tableInfo = {
          id: `raw-table-${subgrp}-${table}`,
          data: { label: table },
          position: { x: 15, y: 30 },
          className: 'table',
          style: {
            height: 100,
            width: tableWidth,
            backgroundColor: subgrp.toLowerCase().includes('sap')
              ? FLOW.RAW.sapTableColor
              : FLOW.RAW.oracleTableColor,
            fontSize: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 0,
          },
          parentNode: `raw-subgrp-${subgrp}`,
          extent: 'parent',
          type: 'table',
        };
        // adding column
        const columnsData: any = tablesData[table];
        const columns = Object.keys(columnsData);
        const numberOfColumns = columns.length;
        maxCols = Math.max(maxCols, numberOfColumns);
        for (let colIndex = 0; colIndex < numberOfColumns; colIndex++) {
          const column: any = columns[colIndex];
          const columnInfo = {
            id: `raw-${subgrp}-${table}-${column}`,
            data: { label: columnsData[column].col_name },
            position: { x: 3, y: 10 * (colIndex + 1) },
            className: 'col',
            style: {
              height: 10,
              backgroundColor: subgrp.toLowerCase().includes('sap')
                ? FLOW.RAW.sapTableColor
                : FLOW.RAW.oracleTableColor,
              width: nodeWidth,
              zIndex: 1,
              paddingLeft: 5,
              paddingRight: 5,
            },
            sourcePosition: 'right',
            type: 'input',
            parentNode: `raw-table-${subgrp}-${table}`,
            extent: 'parent',
          };
          rawNodesAndGroups.push(columnInfo);
        }
        // const tableRow = Math.floor(tableIndex / tablesPerRow);
        const tableRow = Math.floor(tableIndex / tablesPerRow);
        const tableHeight = 10 * (numberOfColumns + 1) + 5;
        if (tableIndex !== 0 && tableIndex % tablesPerRow !== 0) {
          tableRowWidth += tableWidth + 10;
        } else {
          tableRowWidth = FLOW.RAW.initialSubGrpWidth + tableWidth + 10;
        }
        tableHeightArray.push(tableHeight);
        tableInfo.style.height = tableHeight;
        tableInfo.position.x =
          tableRowWidth - tableWidth - FLOW.RAW.initialSubGrpWidth;
        const heightTillPreviousRow = getHeightTillPreviousRow(
          structuredClone(tableHeightArray),
          tableRow - 1,
        );
        tableInfo.position.y = heightTillPreviousRow + 10 * (tableRow + 1);
        rawNodesAndGroups.push(tableInfo);
        subGroupWidth = Math.max(subGroupWidth, tableRowWidth);
      }
      const subGroupHeight = getSubGroupHeight(
        structuredClone(tableHeightArray),
      );
      subGrpInfo.style.height =
        subGroupHeight + 20 + 30 * Math.ceil(tables.length / tablesPerRow);
      subGroupsHeightArray.push(subGrpInfo.style.height);
      subGrpInfo.style.width = subGroupWidth;
      subGrpInfo.position.x = 15;
      const heightTillPreviousSubgroup =
        getHeightTillPreviousSubGroup(subGroupsHeightArray);
      subGrpInfo.position.y = heightTillPreviousSubgroup + 20;
      rawNodesAndGroups.push(subGrpInfo);
      totalSubGroupsHeight += subGrpInfo.style.height + 50; // 50 is for margin
      totalSubgroupWidth = Math.max(totalSubgroupWidth, subGroupWidth);
    }
    rawGroupInfo.style.height = totalSubGroupsHeight + 50;
    rawGroupInfo.style.width = totalSubgroupWidth + 30; // 30 is for margin
    rawNodesAndGroups.push(rawGroupInfo);
    return rawNodesAndGroups;
  };

  const generateNodesAndGroups = () => {
    const rawNodesAndGroups = generateRawNodesAndGroups();
    const rawGroupWidth =
      rawNodesAndGroups[rawNodesAndGroups.length - 1].style.width;
    const enrichNodesAndEdges = generateNodesAndGroup(
      FLOW.ENRICH,
      flowData?.enrich,
      'enrich',
      'raw',
      rawGroupWidth,
    );
    const enrichNodesAndGroups = enrichNodesAndEdges.nodes;
    const enrichWidth =
      enrichNodesAndGroups[enrichNodesAndGroups.length - 1].style.width;
    const enrichEdges = enrichNodesAndEdges.edges;
    const consumeNodesAndEdges = generateNodesAndGroup(
      FLOW.CONSUME,
      flowData?.consume,
      'consume',
      'enrich',
      enrichWidth + rawGroupWidth + 100,
    );
    const consumeNodesAndGroups = consumeNodesAndEdges.nodes;
    const consumeEdges = consumeNodesAndEdges.edges;
    const allNodesAndGroups = [
      ...rawNodesAndGroups,
      ...enrichNodesAndGroups,
      ...consumeNodesAndGroups,
    ];
    const allEdges = [...enrichEdges, ...consumeEdges];
    setTimeout(() => {
      setReadyFlag(true);
    }, 3000);
    setEdges(allEdges);
    setNodes(allNodesAndGroups);
  };

  const onConnect = useCallback((connection: Connection | Edge) => {
    setEdges((eds) => addEdge(connection, eds));
  }, []);

  const highlightEdge = (edge: Edge) => {
    const clonedEdges = structuredClone(edges);
    const updatedClonedEdges: any = clonedEdges.map((el) => {
      if (el.id === edge.id) {
        return {
          ...el,
          animated: true,
          style: { ...el.style, stroke: '#2F80ED' }, // Change the color on hover
        };
      }
      return {
        ...el,
        style: { ...el.style, stroke: null }, // Change the color on hover
      };
    });
    setEdges(updatedClonedEdges);
  };

  const highlightMultipleEdges = (edge: Edge[] | undefined) => {
    const clonedEdges = structuredClone(edges);
    if (!edge) {
      return;
    }
    const updatedClonedEdges: any = clonedEdges.map((el) => {
      if (edge.some((ed) => ed.id === el.id)) {
        return {
          ...el,
          animated: true,
          style: { ...el.style, stroke: '#2F80ED' }, // Change the color on hover
        };
      }
      return {
        ...el,
        animated: false,
        style: { ...el.style, stroke: null }, // Change the color on hover
      };
    });
    setEdges(updatedClonedEdges);
  };

  const hightlightNodes = (edge: Edge) => {
    const clonedNodes = structuredClone(nodes);
    const updatedNodes: any = clonedNodes.map((nd) => {
      if (nd.id === edge.source || nd.id === edge.target) {
        if (edge.source === nd.id) {
          if (nd.sourcePosition === 'right') {
            nd.className += ' highlighted-col visible-right-handle';
          } else {
            nd.className += ' highlighted-col visible-left-handle';
          }
        } else if (edge.target === nd.id) {
          if (nd.targetPosition === 'right') {
            nd.className += ' highlighted-col visible-right-handle';
          } else {
            nd.className += ' highlighted-col visible-left-handle';
          }
        }
      } else {
        nd.className = nd.className?.split(' ')[0];
      }
      return nd;
    });
    setNodes(updatedNodes);
  };

  const hightlightMultipleNodes = (edge: Edge[] | undefined) => {
    if (!edge) {
      return;
    }
    const clonedNodes = structuredClone(nodes);
    const updatedNodes: any = clonedNodes.map((nd) => {
      if (edge.some((ed) => ed.source === nd.id || ed.target === nd.id)) {
        if (edge.some((ed) => ed.source === nd.id)) {
          if (nd.sourcePosition === 'right') {
            nd.className += ' highlighted-col visible-right-handle';
          } else {
            nd.className += ' highlighted-col visible-left-handle';
          }
        } else if (edge.some((ed) => ed.target === nd.id)) {
          if (nd.targetPosition === 'right') {
            nd.className += ' highlighted-col visible-right-handle';
          } else {
            nd.className += ' highlighted-col visible-left-handle';
          }
        }
      } else {
        nd.className = nd.className?.split(' ')[0];
      }
      return nd;
    });
    setNodes(updatedNodes);
  };

  const unhighlightEdge = () => {
    const clonedEdges = structuredClone(edges);
    const updatedClonedEdges: any = clonedEdges.map((el) => {
      return {
        ...el,
        animated: false,
        style: { ...el.style, stroke: null }, // Change the color on hover
      };
    });
    setEdges(updatedClonedEdges);
  };

  const unhighlightNodes = () => {
    const clonedNodes = structuredClone(nodes);
    const updatedNodes: any = clonedNodes.map((nd) => {
      if (nd.className?.includes('highlighted-col')) {
        nd.className = nd.className?.split(' ')[0];
      }
      return nd;
    });
    setNodes(updatedNodes);
  };

  const onEdgeMouseEnter = (_evt: React.MouseEvent, edge: Edge) => {
    highlightEdge(edge);
    hightlightNodes(edge);
  };

  const onEdgeMouseLeave = (_evt: React.MouseEvent) => {
    unhighlightEdge();
    unhighlightNodes();
  };

  const onNodeMouseEnter = (_evt: React.MouseEvent, node: any) => {
    const filteredEdges: Edge[] = edges.filter(
      (edge) => node.id === edge.source || node.id === edge.target,
    );
    highlightMultipleEdges(filteredEdges);
    hightlightMultipleNodes(filteredEdges);
  };

  useEffect(() => {
    if (id) {
      const url = `${
        ApiUtilities.getApiServerUrl +
        ApiUtilities.apiPath.getBusinessModels.url
      }/${id}/lineage`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res: any) => {
          setflowData(res.data);
        })
        .finally(() => dispatch(setIsLoading(false)));
    }
  }, [id]);

  useEffect(() => {
    if (flowData) {
      generateNodesAndGroups();
    }
  }, [flowData]);

  // useEffect(() => {
  //   generateNodesAndGroups();
  // }, []);

  return (
    <div className="h-[90vh] w-[90vw]">
      {readyFlag && (
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onEdgeMouseEnter={onEdgeMouseEnter}
          onEdgeMouseLeave={onEdgeMouseLeave}
          onNodeMouseEnter={onNodeMouseEnter}
          onNodeMouseLeave={onEdgeMouseLeave}
          nodeTypes={nodeTypes}
          proOptions={proOptions}
          nodesDraggable={false}
          deleteKeyCode={null}
          nodesConnectable={false}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background />
        </ReactFlow>
      )}
    </div>
  );
};

export default NestedFlow;
