'use client';

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/dataset/dataset.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  setDatasets as setDs,
  setDatasetTableResponseFetched,
  setSelectedDataset,
} from '@/slices/datasetCrudSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

const DataSetTable: React.FC = () => {
  // essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [datasets, setDatasets] = useState<any>([]);
  const [RowID, setRowID] = useState(null);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const allDatasets = useSelector(
    (state: RootState) => state.datasetCrud.datasets,
  );
  const allDatasetsFetched = useSelector(
    (state: RootState) => state.datasetCrud.datasetTableResponseFetched,
  );
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Constants
  const header: any = [
    {
      title: 'Dataset Name',
      optionsEnabled: false,
      options: [],
      inputType: 'checkbox',
    },
    {
      title: 'Data Source',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Domain',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Source ConnectioN',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Latest Pull',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];

  const data: any = {
    values: datasets,
    enableOptions: true,
  };

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.values.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(setDatasetTableResponseFetched(false));
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Dataset ${RowID} deleted successfully`,
              content: `Dataset ${RowID} has been successfully deleted and all the items connected with the Dataset ${RowID} has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };
  const menuData: any = [
    {
      option: 'View',
      clickFn: (key: any) => {
        const selectedDs = allDatasets.filter((ds: any) => ds.id === key)[0];
        dispatch(setSelectedDataset(selectedDs));
        const parameter: any = {
          name: selectedDs.dataset_name,
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/datasets/datasetDetails?${queryString}`);
      },
    },
    {
      option: 'Delete',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete Dataset ${key}`,
            message: `Are you sure? Do you want to delete the Dataset ${key} Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete',
          }),
        );
      },
    },
  ];

  const formatDatasetForTable = (result: any) => {
    return result.map((ds: any) => {
      const row: any = {
        id: ds.id,
        selected: false,
        isFavourite: ds?.isFavourite ?? false,
        components: [
          {
            value: ds.dataset_name,
            disabled: false,
            userAvatarLink: null,
            type: 'checkbox',
          },
          {
            value: ds.source_system_name,
            disabled: false,
            userAvatarLink: null,
            type: 'text',
          },
          {
            value: ds.analytics.map((a: any) => a.name).join(', '),
            userAvatarLink: null,
            disabled: false,
            type: 'text',
          },
          {
            value: ds.connection_name,
            userAvatarLink: null,
            disabled: false,
            type: 'text',
          },
          {
            value: new Date(ds.created_at).toLocaleDateString('en-US'),
            userAvatarLink: null,
            disabled: false,
            type: 'text',
          },
        ],
      };
      return row;
    });
  };

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSets.url;
    apiService
      .getRequest(url)
      .then((res) => {
        const formattedData = formatDatasetForTable(res.data);
        setFilteredData(formattedData);
        setDatasets(formattedData);
        dispatch(setDatasetTableResponseFetched(true));
        dispatch(setDs(res.data));
      })
      .finally(() => dispatch(setIsLoading(false)));
  };

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  // Effects
  useEffect(() => {
    if (!isTourOpen) {
      if (!filteredData.length || !allDatasetsFetched) {
        fetchData();
      }
    }
  });

  useEffect(() => {
    if (!isTourOpen) {
      fetchData();
    } else {
      const formattedData: any = eData;
      setFilteredData(formattedData);
      setDatasets(formattedData);
      dispatch(setDatasetTableResponseFetched(true));
      dispatch(setDs(formattedData));
    }
  }, [isTourOpen]);

  return (
    <div className=" flex h-full w-full flex-col">
      <div className="w-[20vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="h-full w-full overflow-auto" onScroll={handleScroll}>
        <Table
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          scrollPosition={scrollPosition}
          enableOptions={data.enableOptions}
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>

      <WarningDialog onConfirm={deleteItem} />
    </div>
  );
};

export default DataSetTable;
