["01", "03/22 08:51:01 INFO   :.main: *************** RSVP Agent started ***************", "02", "03/22 08:51:01 INFO   :...locate_configFile: Specified configuration file: /u/user10/rsvpd1.conf", "03/22 08:51:01 INFO   :.main: Using log level 511", "03/22 08:51:01 INFO   :..settcpimage: Get TCP images rc - EDC8112I Operation not supported on socket.", "03", "03/22 08:51:01 INFO   :..settcpimage: Associate with TCP/IP image name = TCPCS", "03/22 08:51:02 INFO   :..reg_process: registering process with the system", "03/22 08:51:02 INFO   :..reg_process: attempt OS/390 registration", "03/22 08:51:02 INFO   :..reg_process: return from registration rc=0", "04", "03/22 08:51:06 TRACE  :...read_physical_netif: Home list entries returned = 7", "03/22 08:51:06 INFO   :...read_physical_netif: index #0, interface VLINK1 has address *********, ifidx 0", "03/22 08:51:06 INFO   :...read_physical_netif: index #1, interface TR1 has address ***********, ifidx 1", "03/22 08:51:06 INFO   :...read_physical_netif: index #2, interface LINK11 has address **********, ifidx 2", "03/22 08:51:06 INFO   :...read_physical_netif: index #3, interface LINK12 has address **********, ifidx 3", "03/22 08:51:06 INFO   :...read_physical_netif: index #4, interface CTCD0 has address ***********, ifidx 4", "03/22 08:51:06 INFO   :...read_physical_netif: index #5, interface CTCD2 has address ***********, ifidx 5", "03/22 08:51:06 INFO   :...read_physical_netif: index #6, interface LOOPBACK has address 127.0.0.1, ifidx 0", "03/22 08:51:06 INFO   :....mailslot_create: creating mailslot for timer", "03/22 08:51:06 INFO   :...mailbox_register: mailbox allocated for timer", "05", "03/22 08:51:06 INFO   :.....mailslot_create: creating mailslot for RSVP"]