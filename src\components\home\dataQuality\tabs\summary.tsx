/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable react/no-array-index-key */
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import dataQualityCards from 'public/testdata/dataQuality/dataQuality.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });
const Summary: React.FC<{ selectedYear?: any; setTab?: any }> = ({
  selectedYear,
  setTab,
}) => {
  const [cardData, setCardData] = useState<any>(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  const localService = new LocalService();
  const getOptions = (lable: any) => {
    const option: any = {
      colors: ['#35E246', '#0088EA'],
      dataLabels: {
        enabled: true,
        // formatter(val: any) {
        //   if (val % 1 !== 0) {
        //     return val.toFixed(1);
        //   }
        //   return val;
        // },
        style: {
          fontSize: '12px',
          fontFamily: 'OpenSans',
          fontWeight: 'medium',
          color: '#ffff',
        },
      },

      stroke: {
        show: false,
      },
      chart: {
        width: 'auto',
        height: 'auto',
        type: 'pie',
      },
      labels: lable,
      legend: {
        fontFamily: 'OpenSans',
        fontWeight: 'medium',
        fontSize: '13px',
        horizontalAlign: 'center',
        position: 'bottom',
        color: '#474D67',
      },
      plotOptions: {
        pie: {
          expandOnClick: false,
          dataLabels: {
            offset: -20,
          },
        },
      },
    };
    return option;
  };

  const formatTestData = (testData: any) => {
    const card = testData.map((test: any) => {
      return {
        name: test.testName,
        graph: {
          series: test.values,
          labels: test.labels,
        },
      };
    });
    const output = {
      name: 'Test Scenarios Summary',
      card,
    };
    return output;
  };

  const fetchCardData = () => {
    try {
      const url = `
        ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getDataQualityCardsData.url}${selectedYear}`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            dispatch(setIsLoading(false));
            const tempData: any = formatTestData(res.data);
            const desiredOrder = [
              'Recon Tests',
              'Reports Freshness',
              'Data File Validation',
              'Data Availability Monthly Files',
              'Data Availability Quarterly Files',
              'Data Availability Annual/Adhoc Files',
            ];
            const sortedData: any = tempData.card.sort((a: any, b: any) => {
              return (
                desiredOrder.indexOf(a.name) - desiredOrder.indexOf(b.name)
              );
            });

            setCardData({
              name: 'Test Scenarios Summary',
              card: sortedData,
            });
          }
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };
  useEffect(() => {
    if (!isTourOpen) {
      fetchCardData();
    } else {
      setCardData(dataQualityCards);
    }
  }, [selectedYear]);

  return (
    <div className="flex h-[calc(100vh-250px)] w-full flex-col space-y-4 overflow-y-auto px-2 py-4 xl:h-[calc(100vh-210px)]">
      <div className="data-quality-summary-cards flex h-full w-full flex-col">
        {cardData && (
          <div className="mt-1 grid h-fit w-full grid-cols-3 overflow-auto">
            {cardData.card.map((dashboardData: any, index: number) => (
              <div
                onClick={() => {
                  setTab(index + 1);
                  localService.setItem(
                    'selectedDataQualityTab',
                    String(index + 1),
                  );
                }}
                className={`col-span-1 m-2 flex h-[95%] cursor-pointer flex-col items-center rounded border-[1px] border-lightgray-100 p-2 shadow-list hover:border-blue-200 `}
                key={index}
              >
                <div className="flex h-10 items-center justify-center text-center font-sans text-sm font-normal text-blueGray-300 ">
                  {dashboardData.name}
                </div>
                <div className="h-[75%]">
                  <Chart
                    options={getOptions(dashboardData.graph.labels)}
                    series={dashboardData.graph.series}
                    type="pie"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Summary;
