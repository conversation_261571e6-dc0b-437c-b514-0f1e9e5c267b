import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { IconLink } from './IconLink';

const meta = {
  title: 'UI/IconLink',
  component: IconLink,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof IconLink>;

export default meta;
type Story = StoryObj<typeof meta>;

const Icon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C12.42 16 16 12.42 16 8C16 3.58 12.42 0 8 0ZM8 14C4.69 14 2 11.31 2 8C2 4.69 4.69 2 8 2C11.31 2 14 4.69 14 8C14 11.31 11.31 14 8 14ZM9 7.59V4C9 3.45 8.55 3 8 3C7.45 3 7 3.45 7 4V8C7 8.28 7.11 8.53 7.29 8.71L9.29 10.71C9.47 10.89 9.72 11 10 11C10.55 11 11 10.55 11 10C11 9.72 10.89 9.47 10.71 9.29L9 7.59Z"
    />
  </svg>
);

const Enabled: Story = {
  args: {
    children: Icon,
    href: '#',
  },
};
const Disabled: Story = {
  args: {
    children: Icon,
    href: '#',
    intent: 'disabled',
  },
};

export { Disabled, Enabled };
