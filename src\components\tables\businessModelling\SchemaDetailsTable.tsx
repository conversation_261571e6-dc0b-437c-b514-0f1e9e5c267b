/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable import/no-cycle */
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Link } from '@/components/ui/Link';
import SchemaJoinTable from '@/components/ui/SchemaJoinTable';
import { createMenuName } from '@/constants/menuSvg';
import { setJoinConfig } from '@/slices/businessModelCrudSlice';

const SchemaDetailTable: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const dispatch = useDispatch();

  // Constants
  const header: any = ['Parent', 'Join', 'Child'];

  // States
  const [data, setData]: any = useState();
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const joinConfig = useSelector(
    (state: any) => state.businessModelCrud.joinConfig,
  );
  const entityAttributes = useSelector(
    (state: any) => state.businessModelCrud.entityAttributes,
  );

  // Methods
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const handleJoinTablesChange = (rows: any) => {
    const tableData: any = {};
    let parentTable: any;
    let childTable: any;
    let value: any;
    rows.forEach((row: any, rowIndex: any) => {
      parentTable = '';
      childTable = '';
      row.components[0].data.forEach((component: any) => {
        value = null;
        if (component.columnKey === 'parent_table') {
          parentTable = component.value;
          value = component.value;
        }
        if (component.columnKey === 'parent_attribute') {
          const validAttributes = entityAttributes?.find(
            (entity: any) => entity.entity_name === parentTable,
          )?.attributes;
          value = validAttributes?.find(
            (attr: any) => attr?.attribute_name === component.value,
          )
            ? component.value
            : '';
        }
        tableData[rowIndex] = {
          ...tableData[rowIndex],
          [component.columnKey]: value,
        };
      });
      tableData[rowIndex] = {
        ...tableData[rowIndex],
        join_type: row.components[1].data.value,
        join_condition: row.components[1].input,
      };
      row.components[2].data.forEach((component: any) => {
        value = null;
        if (component.columnKey === 'child_table') {
          childTable = component.value;
          value = component.value;
        }
        if (component.columnKey === 'child_attribute') {
          const validAttributes = entityAttributes?.find(
            (entity: any) => entity.entity_name === childTable,
          )?.attributes;
          value = validAttributes?.find(
            (attr: any) => attr?.attribute_name === component.value,
          )
            ? component.value
            : '';
        }
        tableData[rowIndex] = {
          ...tableData[rowIndex],
          [component.columnKey]: value,
        };
      });
    });
    dispatch(setJoinConfig(tableData));
  };

  const addRow = () => {
    const parentTableList = entityAttributes?.map((attr: any) => ({
      name: attr.entity_name,
    }));

    const newRow = {
      id: `New Entry ${data.length + 1}`,
      components: [
        {
          type: 'joinItem',
          data: [
            {
              value: '',
              columnKey: 'parent_table',
              type: 'select',
              placeholder: 'Select Table',
              selectList: [...parentTableList],
            },
            {
              value: '',
              columnKey: 'parent_attribute',
              type: 'select',
              placeholder: 'Select Attribute',
              selectList: [],
            },
          ],
        },
        {
          type: 'join',
          data: {
            value: '',
            type: 'select',
            placeholder: 'Select Join',
            selectList: [
              { name: 'Right Join' },
              { name: 'Left Join' },
              { name: 'Inner Join' },
            ],
          },
          input: '',
        },
        {
          type: 'joinItem',
          data: [
            {
              value: '',
              columnKey: 'child_table',
              type: 'select',
              placeholder: 'Select Table',
              selectList: [...parentTableList],
            },
            {
              value: '',
              type: 'select',
              columnKey: 'child_attribute',
              placeholder: 'Select Attribute',
              selectList: [],
            },
          ],
        },
      ],
    };
    const updatedData = [newRow, ...data];
    setData(updatedData);
  };
  const getAttributes = (tableName: any) => {
    const attrList = entityAttributes?.find(
      (attr: any) => attr.entity_name === tableName,
    );
    return attrList?.attributes.map((attr: any) => ({
      name: attr.attribute_name,
    }));
  };
  const resolveData = () => {
    const tableData: any = [];
    Object.keys(joinConfig || {}).forEach((row: any) => {
      const rowData: any = {
        components: [],
      };
      const parentTableList = entityAttributes?.map((attr: any) => ({
        name: attr.entity_name,
      }));

      rowData.components.push({
        type: 'joinItem',
        data: [
          {
            value: joinConfig[row]?.parent_table,
            columnKey: 'parent_table',
            type: 'select',
            placeholder: 'Select Table',
            selectList: [...parentTableList],
          },
        ],
      });
      const parentAttributes =
        getAttributes(joinConfig[row]?.parent_table) || [];

      rowData.components[0]?.data.push({
        value: joinConfig[row]?.parent_attribute,
        columnKey: 'parent_attribute',
        type: 'select',
        placeholder: 'Select Attribute',
        selectList: [...parentAttributes],
      });
      rowData.components.push({
        type: 'join',
        data: {
          value: joinConfig[row]?.join_type,
          type: 'select',
          placeholder: 'Select Join',
          selectList: [
            { name: 'Left Join' },
            { name: 'Right Join' },
            { name: 'Inner Join' },
          ],
        },
        input: joinConfig[row]?.join_condition,
      });

      const childAttributes = getAttributes(joinConfig[row]?.child_table) || [];
      rowData.components.push({
        type: 'joinItem',
        data: [
          {
            value: joinConfig[row]?.child_table,
            columnKey: 'child_table',
            type: 'select',
            placeholder: 'Select Table',
            selectList: [...parentTableList],
          },
          {
            value: joinConfig[row]?.child_attribute,
            columnKey: 'child_attribute',
            type: 'select',
            placeholder: 'Select Attribute',
            selectList: [...childAttributes],
          },
        ],
      });
      tableData.push(rowData);
    });
    return tableData;
  };

  // Effects
  useEffect(() => {
    const temp = resolveData();
    setData(temp);
  }, [joinConfig, entityAttributes]);
  // useEffect(() => {
  //   setData(data);
  // }, [data]);
  if (!data) {
    return null;
  }
  return (
    <div className="flex h-full  flex-col">
      <div className=" flex w-full flex-row items-center justify-between">
        <span className="font-sans text-base font-semibold text-blueGray-300">
          Table Joins
        </span>
        {props.mode !== 'View' && (
          <Link content={createMenuName('Add Row')} onClick={addRow} />
        )}
      </div>
      <div
        className="mt-4 h-[85%] w-full overflow-auto bg-gray-300"
        onScroll={handleScroll}
      >
        <SchemaJoinTable
          isDashTable={false}
          header={header}
          data={data}
          enabledCross={props.mode !== 'View'}
          enableOptions={false}
          onRowChange={handleJoinTablesChange}
          scrollPosition={scrollPosition}
        />
      </div>
    </div>
  );
};

export default SchemaDetailTable;
