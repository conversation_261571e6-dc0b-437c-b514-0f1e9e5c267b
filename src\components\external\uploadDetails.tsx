/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

import { Formik, useFormik } from 'formik';

import { Input } from '@/components/ui/Input';

export const UploadDetails = () => {
  const { setFieldValue, values, errors, touched, handleBlur, handleSubmit } =
    useFormik({
      initialValues: {
        FName: '',
        desc: '',
        eId: '',
        templateCat: '',
        file: {},
      },
      onSubmit: () => {},
    });
  return (
    <div className=" h-full w-full rounded bg-white-200 p-2">
      <div className="flex h-full w-full flex-col space-y-4 overflow-auto p-2">
        <Formik
          initialValues={{
            FName: '',
            desc: '',
            eId: '',
            templateCat: '',
            file: null,
          }}
          enableReinitialize
          validate={(value) => {
            const error: any = {};
            if (!value.FName) {
              error.CName = 'Required.';
            }
            if (!value.desc) {
              error.desc = 'Required.';
            }
            if (!value.eId) {
              error.OName = 'Required.';
            }
            if (!value.templateCat) {
              error.PName = 'Required.';
            }
            if (!value.file) {
              error.PMailID = 'Required.';
            }
            return error;
          }}
          onSubmit={(v, { setSubmitting }) => {
            setTimeout(() => {
              console.log(v);
              // can show loader if wanted
              setSubmitting(false);
              // router.push('/dashboard');
            }, 400);
          }}
        >
          <form
            className="flex h-full w-full flex-col space-y-6 "
            onSubmit={handleSubmit}
            id="myform"
          >
            <div className="flex h-full flex-wrap justify-between">
              <div className="w-[30%]">
                <Input
                  label="File Name"
                  name="FName"
                  type="text"
                  className="h-[50px]"
                  placeholder="Enter File Name"
                  onChange={(e) => setFieldValue('FName', e.target.value)}
                  onBlur={handleBlur}
                  value={values.FName}
                  intent={
                    errors.FName && touched.FName ? 'hasError' : 'enabled'
                  }
                  error={errors.FName && touched.FName && errors.FName}
                />
              </div>

              <div className="w-[30%]">
                <Input
                  label="Entry ID"
                  name="eId"
                  type="text"
                  className="h-[50px]"
                  placeholder="Enter Entry ID"
                  onChange={(e) => setFieldValue('eId', e.target.value)}
                  onBlur={handleBlur}
                  value={values.eId}
                  intent={errors.eId && touched.eId ? 'hasError' : 'enabled'}
                  error={errors.eId && touched.eId && errors.eId}
                />
              </div>

              <div className="w-[30%]">
                <Input
                  label="Template Category"
                  name="templateCat"
                  type="text"
                  className="h-[50px]"
                  placeholder="Template Category"
                  onChange={(e) => setFieldValue('templateCat', e.target.value)}
                  onBlur={handleBlur}
                  value={values.templateCat}
                  intent={
                    errors.templateCat && touched.templateCat
                      ? 'hasError'
                      : 'enabled'
                  }
                  error={
                    errors.templateCat &&
                    touched.templateCat &&
                    errors.templateCat
                  }
                />
              </div>

              <div className="w-[65%]">
                <Input
                  label="Description"
                  name="desc"
                  type="text"
                  className="h-[50px]"
                  placeholder="Enter Description"
                  onChange={(e) => setFieldValue('desc', e.target.value)}
                  onBlur={handleBlur}
                  value={values.desc}
                  intent={errors.desc && touched.desc ? 'hasError' : 'enabled'}
                  error={errors.desc && touched.desc && errors.desc}
                />
              </div>
            </div>
            <label
              htmlFor="file"
              className="flex flex-col items-center justify-center rounded border-2 border-dotted bg-blue-50 p-8 "
            >
              <div className="text-blue-500">
                <img src="/assets/images/upload_drop.svg" alt="" width={80} />
              </div>
              <p className="mt-4 text-blue-500">
                Drag and drop or click here to upload file
              </p>
              <input
                type="file"
                id="file"
                className="hidden"
                onChange={(event: any) => {
                  setFieldValue('file', event?.target?.files[0]);
                }}
              />
            </label>
            <div className="relative w-[45%]">
              <div className="h-[50px] w-full rounded border-[1px] bg-white-200 px-3 py-4 font-sans text-sm font-normal leading-4">
                {(values.file as File).name
                  ? (values.file as File).name
                  : 'File name'}
              </div>
              {values.file && (
                <img
                  src="/assets/images/delete.svg"
                  className="absolute right-4 top-3 cursor-pointer"
                  alt=""
                  onClick={() => {
                    setFieldValue('file', '');
                  }}
                />
              )}
            </div>
          </form>
        </Formik>
      </div>
    </div>
  );
};
