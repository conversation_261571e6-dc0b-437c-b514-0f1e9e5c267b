'use client';

/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-danger */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useRef, useState } from 'react';

import ListBoxTable from '@/components/ui/ListBoxTable';
import { dqYearList } from '@/constants/appConstants';
import { LocalService } from '@/service/local.service';

import DataQualityTabs from './dataQualityTabs';
// import SelectData from './addLogicalEntitiesTabs/selectData';
const DataQuality: React.FC = () => {
  const localService = new LocalService();
  const divRef = useRef<any>(null);
  const [selectedTab, setSelectedTab] = useState(
    Number(localService.getItem('selectedDataQualityTab')) || 0,
  );
  const [selectedYear, setSelectedYear] = useState<any>(
    localService.getItem('selectedYear') || '2024',
  );

  return (
    <div className="flex size-full flex-col space-y-2">
      <div className="flex w-fit flex-row items-center space-x-2">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Quality Dashboard | Year
        </span>
        <div className="flex w-[7vw] rounded border-2 border-lightgray-100">
          <ListBoxTable
            isInDataQualityDashboard
            placeholder="Select Year"
            isInTable
            items={dqYearList}
            selectedValue={selectedYear}
            hasValue
            onSelectionChange={(selectedValue) => {
              setSelectedYear(selectedValue.name);
              localService.setItem('selectedYear', selectedValue.name);
            }}
            name="select"
          />
        </div>
      </div>
      <div className="rounded border border-lightgray-100 bg-white-200 p-6 ">
        <div
          className="flex h-[calc(100vh-150px)] w-full flex-col space-y-4 overflow-hidden"
          ref={divRef}
        >
          <div className="data-quality-summary-tabs size-full overflow-auto">
            <DataQualityTabs
              selectedTabIndex={selectedTab}
              selectedYear={selectedYear}
              onTabClick={(tab: any) => {
                setSelectedTab(tab);
                localService.setItem('selectedDataQualityTab', String(tab));
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataQuality;
