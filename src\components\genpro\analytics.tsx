'use client';

import {
  Bar<PERSON>hart3,
  DollarSign,
  Pie<PERSON><PERSON>,
  TrendingUp,
  Users,
} from 'lucide-react';
import dynamic from 'next/dynamic';

// Dynamically import ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const GenProAnalytics = () => {
  // Brokerage Fee Allocation Analytics
  const bfMetrics = {
    totalBFAllocated: 125000, // Total BF allocated this month
    monthlyGrowth: 8.4,
    avgBFPerVessel: 4167, // 125000 / 30 vessels
    totalVessels: 30,
    seachefPercentage: 26.7, // % of BF allocated to SEACHEF
    seachefAmount: 33375,
  };

  // Top 3 SMCs by BF Allocation
  const topSMCs = [
    { smc: 'BSM CYP', allocation: 50000, percentage: 40.0, vessels: 12 },
    { smc: 'SEACHEF', allocation: 33375, percentage: 26.7, vessels: 8 },
    { smc: 'BSM HEL', allocation: 25000, percentage: 20.0, vessels: 6 },
  ];

  // All SMCs for distribution chart
  const allSMCs = [
    { smc: 'BSM CYP', allocation: 50000, percentage: 40.0 },
    { smc: 'SEACHEF', allocation: 33375, percentage: 26.7 },
    { smc: 'BSM HEL', allocation: 25000, percentage: 20.0 },
    { smc: 'BSM SG', allocation: 12500, percentage: 10.0 },
    { smc: 'Frontline', allocation: 4125, percentage: 3.3 },
  ];

  // Monthly BF allocation trends (6 months)
  const monthlyTrends = [
    {
      month: 'Jan',
      totalBF: 98000,
      bsmCyp: 39200,
      seachef: 26166,
      bsmHel: 19600,
      bsmSG: 9800,
      frontline: 3234,
      seachefPercent: 26.7,
    },
    {
      month: 'Feb',
      totalBF: 112000,
      bsmCyp: 44800,
      seachef: 29904,
      bsmHel: 22400,
      bsmSG: 11200,
      frontline: 3696,
      seachefPercent: 26.7,
    },
    {
      month: 'Mar',
      totalBF: 125000,
      bsmCyp: 50000,
      seachef: 33375,
      bsmHel: 25000,
      bsmSG: 12500,
      frontline: 4125,
      seachefPercent: 26.7,
    },
    {
      month: 'Apr',
      totalBF: 118000,
      bsmCyp: 47200,
      seachef: 31506,
      bsmHel: 23600,
      bsmSG: 11800,
      frontline: 3894,
      seachefPercent: 26.7,
    },
    {
      month: 'May',
      totalBF: 135000,
      bsmCyp: 54000,
      seachef: 36045,
      bsmHel: 27000,
      bsmSG: 13500,
      frontline: 4455,
      seachefPercent: 26.7,
    },
    {
      month: 'Jun',
      totalBF: 142000,
      bsmCyp: 56800,
      seachef: 37914,
      bsmHel: 28400,
      bsmSG: 14200,
      frontline: 4686,
      seachefPercent: 26.7,
    },
  ];

  // SMC performance details
  const smcDetails = [
    {
      smc: 'BSM CYP',
      currentMonth: 50000,
      lastMonth: 54000,
      change: -7.4,
      vessels: 12,
      avgPerVessel: 4167,
    },
    {
      smc: 'SEACHEF',
      currentMonth: 33375,
      lastMonth: 36045,
      change: -7.4,
      vessels: 8,
      avgPerVessel: 4172,
    },
    {
      smc: 'BSM HEL',
      currentMonth: 25000,
      lastMonth: 27000,
      change: -7.4,
      vessels: 6,
      avgPerVessel: 4167,
    },
    {
      smc: 'BSM SG',
      currentMonth: 12500,
      lastMonth: 13500,
      change: -7.4,
      vessels: 3,
      avgPerVessel: 4167,
    },
    {
      smc: 'Frontline',
      currentMonth: 4125,
      lastMonth: 4455,
      change: -7.4,
      vessels: 1,
      avgPerVessel: 4125,
    },
  ];

  return (
    <div>
      <div className="mb-6">
        <h1 className="mb-2 flex items-center text-xl font-bold text-gray-800">
          <BarChart3 className="mr-2 size-4" />
          Analytics Overview (Concept)
        </h1>
      </div>

      {/* Brokerage Fee Overview Cards - 3 Cards */}
      <div className="mb-4 grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
        {/* Total BF Allocated This Month */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Total BF Allocated</p>
              <p className="text-lg font-bold text-gray-800">
                ${bfMetrics.totalBFAllocated.toLocaleString()}
              </p>
              <div className="mt-1 flex items-center">
                <TrendingUp className="mr-1 size-3 text-blue-500" />
                <span className="text-xs text-blue-600">
                  +{bfMetrics.monthlyGrowth}%
                </span>
              </div>
            </div>
            <DollarSign className="size-6 text-gray-500" />
          </div>
        </div>

        {/* Top SMC (BSM CYP) */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Top SMC - BSM CYP</p>
              <p className="text-lg font-bold text-gray-800">
                ${topSMCs[0]?.allocation?.toLocaleString?.() ?? '-'}
              </p>
              <div className="mt-1 flex items-center">
                <span className="text-xs text-blue-600">
                  {topSMCs[0]?.percentage ?? '-'}% of total
                </span>
              </div>
            </div>
            <Users className="size-5 text-gray-500" />
          </div>
        </div>

        {/* SEACHEF Allocation % */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">SEACHEF Allocation</p>
              <p className="text-lg font-bold text-gray-800">
                {bfMetrics.seachefPercentage}%
              </p>
              <div className="mt-1 flex items-center">
                <span className="text-xs text-gray-600">
                  ${bfMetrics.seachefAmount.toLocaleString()}
                </span>
              </div>
            </div>
            <PieChart className="size-5 text-gray-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="mb-4 grid grid-cols-1 gap-4 lg:grid-cols-2">
        {/* Monthly Total BF Allocated Trend */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-4">
          <h3 className="mb-3 text-base font-semibold text-gray-800">
            Monthly Total BF Allocated
          </h3>
          <div className="space-y-3">
            {/* Professional Bar Chart */}
            <div className="h-48">
              <Chart
                options={{
                  chart: {
                    type: 'bar',
                    height: 180,
                    toolbar: {
                      show: false,
                    },
                    background: 'transparent',
                  },
                  plotOptions: {
                    bar: {
                      borderRadius: 4,
                      columnWidth: '60%',
                      dataLabels: {
                        position: 'top',
                      },
                    },
                  },
                  dataLabels: {
                    enabled: true,
                    formatter(val: number) {
                      return `$${(val / 1000).toFixed(0)}K`;
                    },
                    offsetY: -20,
                    style: {
                      fontSize: '10px',
                      colors: ['#374151'],
                    },
                  },
                  xaxis: {
                    categories: monthlyTrends.map((trend) => trend.month),
                    axisBorder: {
                      show: false,
                    },
                    axisTicks: {
                      show: false,
                    },
                    labels: {
                      style: {
                        colors: '#6B7280',
                        fontSize: '12px',
                      },
                    },
                  },
                  yaxis: {
                    labels: {
                      formatter(val: number) {
                        return `$${(val / 1000).toFixed(0)}K`;
                      },
                      style: {
                        colors: '#6B7280',
                        fontSize: '12px',
                      },
                    },
                  },
                  grid: {
                    borderColor: '#E5E7EB',
                    strokeDashArray: 3,
                    xaxis: {
                      lines: {
                        show: false,
                      },
                    },
                  },
                  colors: ['#3B82F6'],
                  tooltip: {
                    y: {
                      formatter(val: number) {
                        return `$${val.toLocaleString()}`;
                      },
                    },
                  },
                }}
                series={[
                  {
                    name: 'BF Allocated',
                    data: monthlyTrends.map((trend) => trend.totalBF),
                  },
                ]}
                type="bar"
                height={180}
              />
            </div>
          </div>
        </div>

        {/* SMC-wise BF Allocation Trends */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-4">
          <h3 className="mb-3 text-base font-semibold text-gray-800">
            SMC-wise Allocation Trends
          </h3>
          <div className="space-y-3">
            {/* Professional Stacked Bar Chart */}
            <div className="h-48">
              <Chart
                options={{
                  chart: {
                    type: 'bar',
                    height: 180,
                    stacked: true,
                    toolbar: {
                      show: false,
                    },
                    background: 'transparent',
                  },
                  plotOptions: {
                    bar: {
                      horizontal: false,
                      borderRadius: 4,
                      columnWidth: '60%',
                    },
                  },
                  dataLabels: {
                    enabled: false,
                  },
                  xaxis: {
                    categories: monthlyTrends.map((trend) => trend.month),
                    axisBorder: {
                      show: false,
                    },
                    axisTicks: {
                      show: false,
                    },
                    labels: {
                      style: {
                        colors: '#6B7280',
                        fontSize: '12px',
                      },
                    },
                  },
                  yaxis: {
                    labels: {
                      formatter(val: number) {
                        return `$${(val / 1000).toFixed(0)}K`;
                      },
                      style: {
                        colors: '#6B7280',
                        fontSize: '12px',
                      },
                    },
                  },
                  grid: {
                    borderColor: '#E5E7EB',
                    strokeDashArray: 3,
                    xaxis: {
                      lines: {
                        show: false,
                      },
                    },
                  },
                  colors: [
                    '#3B82F6',
                    '#10B981',
                    '#F59E0B',
                    '#EF4444',
                    '#8B5CF6',
                  ],
                  legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    fontSize: '12px',
                    markers: {
                      width: 8,
                      height: 8,
                      radius: 2,
                    },
                  },
                  tooltip: {
                    y: {
                      formatter(val: number) {
                        return `$${val.toLocaleString()}`;
                      },
                    },
                  },
                }}
                series={[
                  {
                    name: 'BSM CYP',
                    data: monthlyTrends.map((trend) => trend.bsmCyp),
                  },
                  {
                    name: 'SEACHEF',
                    data: monthlyTrends.map((trend) => trend.seachef),
                  },
                  {
                    name: 'BSM HEL',
                    data: monthlyTrends.map((trend) => trend.bsmHel),
                  },
                  {
                    name: 'BSM SG',
                    data: monthlyTrends.map((trend) => trend.bsmSG),
                  },
                  {
                    name: 'Frontline',
                    data: monthlyTrends.map((trend) => trend.frontline),
                  },
                ]}
                type="bar"
                height={180}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Top 3 SMCs Detail */}
      <div className="mb-4 rounded-lg border border-lightgray-100 bg-white-200 p-4">
        <h3 className="mb-3 text-base font-semibold text-gray-800">
          Top SMCs by BF Allocation
        </h3>
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {/* SMC Cards */}
          <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-1">
            {topSMCs.map((smc, index) => (
              <div key={index} className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                <div className="mb-2 flex items-center justify-between">
                  <span className="text-sm font-semibold text-gray-800">
                    #{index + 1} {smc.smc}
                  </span>
                  <span className="text-xs text-gray-600">
                    {smc.vessels} vessels
                  </span>
                </div>
                <div className="mb-2">
                  <span className="text-lg font-bold text-gray-800">
                    ${smc.allocation.toLocaleString()}
                  </span>
                  <span className="ml-2 text-sm text-blue-600">
                    {smc.percentage}%
                  </span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-100">
                  <div
                    className={`h-2 rounded-full ${
                      index === 0 ? 'bg-blue-500' :
                      index === 1 ? 'bg-green-500' :
                      'bg-amber-500'
                    }`}
                    style={{ width: `${smc.percentage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* SMC Distribution Donut Chart */}
          <div className="flex flex-col items-center justify-center">
            <h4 className="mb-3 text-sm font-medium text-gray-800">
              SMC Distribution
            </h4>
            <div className="h-80 w-full">
              <Chart
                options={{
                  chart: {
                    type: 'donut',
                    height: 300,
                    toolbar: {
                      show: false,
                    },
                    background: 'transparent',
                  },
                  labels: allSMCs.map((smc) => smc.smc),
                  colors: [
                    '#3B82F6',
                    '#10B981',
                    '#F59E0B',
                    '#EF4444',
                    '#8B5CF6',
                  ],
                  dataLabels: {
                    enabled: false,
                  },
                  plotOptions: {
                    pie: {
                      donut: {
                        size: '65%',
                        labels: {
                          show: true,
                          total: {
                            show: true,
                            label: 'Total BF',
                            formatter() {
                              return `$${bfMetrics.totalBFAllocated.toLocaleString()}`;
                            },
                            fontSize: '13px',
                            fontWeight: 'bold',
                            color: '#374151',
                          },
                        },
                      },
                      expandOnClick: false,
                    },
                  },
                  legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    fontSize: '12px',
                    markers: {
                      width: 10,
                      height: 10,
                      radius: 3,
                    },
                    formatter: function(seriesName: string, opts: any) {
                      const percentage = opts.w.globals.series[opts.seriesIndex];
                      return `${seriesName}: ${percentage.toFixed(1)}%`;
                    },
                  },
                  tooltip: {
                    y: {
                      formatter(val: number, opts: any) {
                        const allocation =
                          allSMCs[opts.seriesIndex]?.allocation || 0;
                        return `$${allocation.toLocaleString()} (${val.toFixed(
                          1,
                        )}%)`;
                      },
                    },
                  },
                }}
                series={allSMCs.map((smc) => smc.percentage)}
                type="donut"
                height={300}
              />
            </div>
          </div>
        </div>
      </div>

      {/* SMC Performance Table & SEACHEF Analysis */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        {/* SMC Performance Details */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-4">
          <h3 className="mb-3 text-base font-semibold text-gray-800">
            SMC Performance Details
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-lightgray-100">
                  <th className="px-2 py-3 text-left font-medium text-gray-700">
                    SMC
                  </th>
                  <th className="px-2 py-3 text-left font-medium text-gray-700">
                    Current
                  </th>
                  <th className="px-2 py-3 text-left font-medium text-gray-700">
                    Vessels
                  </th>
                  <th className="px-2 py-3 text-left font-medium text-gray-700">
                    Avg/Vessel
                  </th>
                </tr>
              </thead>
              <tbody>
                {smcDetails.map((smc, index) => (
                  <tr key={index} className="border-b border-lightgray-100/50">
                    <td className="px-2 py-3 font-medium text-gray-800">
                      {smc.smc}
                    </td>
                    <td className="px-2 py-3 text-gray-600">
                      ${(smc.currentMonth / 1000).toFixed(0)}K
                    </td>
                    <td className="px-2 py-3 text-gray-600">{smc.vessels}</td>
                    <td className="px-2 py-3 text-gray-600">
                      ${(smc.avgPerVessel / 1000).toFixed(1)}K
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* SEACHEF vs Others Analysis */}
        <div className="rounded-lg border border-lightgray-100 bg-white-200 p-4">
          <h3 className="mb-3 text-base font-semibold text-gray-800">
            SEACHEF vs Others (Monthly %)
          </h3>
          <div className="space-y-3">
            {/* Professional Pie Chart */}
            <div className="h-48">
              <Chart
                options={{
                  chart: {
                    type: 'pie',
                    height: 180,
                    toolbar: {
                      show: false,
                    },
                    background: 'transparent',
                  },
                  labels: ['SEACHEF', 'Others'],
                  colors: ['#10B981', '#E5E7EB'],
                  dataLabels: {
                    enabled: true,
                    formatter(val: number) {
                      return `${val.toFixed(1)}%`;
                    },
                    style: {
                      fontSize: '12px',
                      fontWeight: 'bold',
                      colors: ['#FFFFFF', '#374151'],
                    },
                  },
                  plotOptions: {
                    pie: {
                      donut: {
                        size: '45%',
                      },
                      expandOnClick: false,
                    },
                  },
                  legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    fontSize: '12px',
                    markers: {
                      width: 8,
                      height: 8,
                      radius: 2,
                    },
                  },
                  tooltip: {
                    y: {
                      formatter(val: number) {
                        return `${val.toFixed(1)}%`;
                      },
                    },
                  },
                }}
                series={[
                  bfMetrics.seachefPercentage,
                  100 - bfMetrics.seachefPercentage,
                ]}
                type="pie"
                height={180}
              />
            </div>

            {/* SEACHEF Percentage Trend Line Chart */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-800">
                SEACHEF Allocation Trend
              </h4>
              <div className="h-32">
                <Chart
                  options={{
                    chart: {
                      type: 'line',
                      height: 120,
                      toolbar: {
                        show: false,
                      },
                      background: 'transparent',
                      sparkline: {
                        enabled: true,
                      },
                    },
                    stroke: {
                      curve: 'smooth',
                      width: 3,
                    },
                    colors: ['#10B981'],
                    dataLabels: {
                      enabled: true,
                      formatter(val: number) {
                        return `${val.toFixed(1)}%`;
                      },
                      style: {
                        fontSize: '10px',
                        colors: ['#374151'],
                      },
                      offsetY: -10,
                    },
                    xaxis: {
                      categories: monthlyTrends.map((trend) => trend.month),
                      labels: {
                        show: true,
                        style: {
                          fontSize: '10px',
                          colors: '#6B7280',
                        },
                      },
                    },
                    yaxis: {
                      show: false,
                    },
                    grid: {
                      show: false,
                    },
                    tooltip: {
                      enabled: true,
                      y: {
                        formatter(val: number) {
                          return `${val.toFixed(1)}%`;
                        },
                      },
                    },
                  }}
                  series={[
                    {
                      name: 'SEACHEF %',
                      data: monthlyTrends.map((trend) => trend.seachefPercent),
                    },
                  ]}
                  type="line"
                  height={120}
                />
              </div>
            </div>

            {/* Summary */}
            
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenProAnalytics;
