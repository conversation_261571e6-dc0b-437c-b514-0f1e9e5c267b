/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

import 'reactflow/dist/style.css';

import { useRouter } from 'next/navigation';
import React from 'react';
import ReactFlow, { Background, Controls, MiniMap } from 'reactflow';

import CustomNode from '@/components/nodes/customNode/customNode';
import GroupNode from '@/components/nodes/groupNode/groupNode';
import NoHanldeNode from '@/components/nodes/noHandleNode/noHandleNode';

const nodeTypes = {
  customNode: CustomNode,
  groupNode: GroupNode,
  nohandleNode: NoHanldeNode,
};
const ModelFlow: React.FC = () => {
  const router = useRouter();
  const initialNodes: any = [
    {
      id: '1',
      type: 'input',
      data: { label: 'Consolidated Actuals(BGC)' },
      position: { x: 0, y: 0 },
      sourcePosition: 'right',
    },
    {
      id: '2',
      sourcePosition: 'right',
      targetPosition: 'left',
      data: { label: 'Revaluation report' },
      position: { x: 300, y: 0 },
    },
    {
      id: '3',
      type: 'customNode',
      data: { label: 'Consolidated Actuals' },
      position: { x: 600, y: 0 },
    },
    {
      id: 'feesGroup',
      type: 'group',
      position: {
        x: 900,
        y: 0,
      },
      style: {
        width: 180,
        height: 180,
        border: 'none',
        backgroundColor: 'rgba(208, 192, 247, 0.2)',
      },
    },
    {
      id: 'feesGroup-nd1',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 20,
      },
      data: { label: 'Management Fees' },
      parentId: 'feesGroup',
    },
    {
      id: 'feesGroup-nd2',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 70,
      },
      data: { label: 'Termination Fees' },
      parentId: 'feesGroup',
    },
    {
      id: 'feesGroup-nd3',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 120,
      },
      data: { label: 'Crew Lumpsum fees' },
      parentId: 'feesGroup',
    },
    {
      id: 'transactionalGroup',
      type: 'groupNode',
      data: {
        label: 'Transactional Data',
      },
      position: {
        x: 500,
        y: 300,
      },
      style: {
        width: 300,
        height: 400,
        backgroundColor: 'rgba(208, 192, 247, 0.2)',
      },
    },
    {
      id: 'transGroup-nd1',
      position: {
        x: 40,
        y: 40,
      },
      style: {
        height: 40,
        width: 200,
      },
      type: 'output',
      targetPosition: 'top',
      data: { label: 'Consolidated Actuals (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd2',
      targetPosition: 'left',
      sourcePosition: 'right',
      style: {
        height: 40,
        width: 200,
      },
      position: {
        x: 40,
        y: 80,
      },
      data: { label: 'Consolidated Actuals (AGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd3',
      position: {
        x: 40,
        y: 120,
      },
      type: 'nohandleNode',
      data: { label: 'Revaluation Report' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd4',
      position: {
        x: 40,
        y: 160,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecast (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd5',
      position: {
        x: 40,
        y: 200,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecase (AGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd6',
      position: {
        x: 40,
        y: 240,
      },
      type: 'nohandleNode',
      data: { label: 'Management Fees*' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd7',
      position: {
        x: 40,
        y: 280,
      },
      type: 'nohandleNode',
      data: { label: 'Termination Fees*' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd8',
      position: {
        x: 40,
        y: 320,
      },
      type: 'nohandleNode',
      data: { label: 'Crew Lumpsum Fees*' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'chartAccount-nd2',
      type: 'input',
      sourcePosition: 'bottom',
      position: {
        x: 400,
        y: 125,
      },
      data: { label: 'Chart of Accounts' },
    },
    {
      id: 'orgHeirarchy-nd2',
      sourcePosition: 'bottom',
      targetPosition: 'top',
      position: {
        x: 600,
        y: 125,
      },
      data: { label: 'Organisation Heirarchy' },
    },
    {
      id: 'calendar',
      type: 'input',
      sourcePosition: 'right',
      position: {
        x: 150,
        y: 250,
      },
      data: { label: 'Calendar' },
    },
    {
      id: 'chartAccount-nd3',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 850,
        y: 250,
      },
      data: { label: 'Chart of account (Additional Info)' },
    },
  ];

  const initialEdges = [
    { id: 'e1', source: '1', target: '2' },
    { id: 'e2', source: '2', target: '3' },
    { id: 'e3', source: '3', sourceHandle: 'right', target: 'feesGroup-nd1' },
    { id: 'e4', source: '3', sourceHandle: 'right', target: 'feesGroup-nd2' },
    { id: 'e5', source: '3', sourceHandle: 'right', target: 'feesGroup-nd3' },
    {
      id: 'e6',
      source: '3',
      sourceHandle: 'bottom',
      target: 'orgHeirarchy-nd2',
    },
    { id: 'e7', source: 'chartAccount-nd2', target: 'transGroup-nd1' },
    { id: 'e8', source: 'orgHeirarchy-nd2', target: 'transGroup-nd1' },
    { id: 'e9', source: 'calendar', target: 'transGroup-nd2' },
    { id: 'e10', source: 'transGroup-nd2', target: 'chartAccount-nd3' },
  ];

  const proOptions = { hideAttribution: true };

  const goBack = () => {
    router.push('/home');
  };

  return (
    <div className="h-[calc(100vh-70px)]  w-[90vw]">
      <div className="flex w-fit flex-row items-center space-x-2">
        <img
          className="size-[24px] cursor-pointer"
          src="/assets/images/arrow-left.svg"
          onClick={goBack}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Model - Transactional Data
        </span>
      </div>
      <div className="h-[calc(100vh-110px)] ">
        <ReactFlow
          nodes={initialNodes}
          edges={initialEdges}
          nodeTypes={nodeTypes}
          proOptions={proOptions}
          nodesDraggable={false}
          deleteKeyCode={null}
          nodesConnectable={false}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background />
        </ReactFlow>
      </div>
    </div>
  );
};

export default ModelFlow;
