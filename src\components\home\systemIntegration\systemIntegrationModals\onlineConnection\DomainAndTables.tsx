/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setErpDomainTable } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import {
  setSelectedDomains,
  setSelectedTables,
} from '../../../../../slices/sourceSystemCrudSlice';
import DomainTable from '../../../../tables/systemIntegration/domainTable';
import ListBox from '../../../../ui/ListBox';
import { Paragraph } from '../../../../ui/Paragraph';

export function DomainAndTables() {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [formattedDomains, setFormattedDomains] = useState([]);
  const [availableTables, setAvailableTables] = useState<any>([]);

  // Selectors
  const domains = useSelector((state: RootState) => state.metadata.domains);
  const selectedSourceSystem = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedSourceSystem,
  );
  const selectedDomains = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedDomains,
  );
  const selectedTables = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedTables,
  );
  const erpDomainTables = useSelector(
    (state: RootState) => state.metadata.erpDomainTables,
  );
  const mappedDomain = useSelector(
    (state: RootState) => state.metadata.mappedDomain,
  );

  // Methods
  const formatDomains = (domains: any) => {
    const fd: any = [];
    domains.forEach((domain: any) => {
      fd.push({
        name: domain.analytics_name,
        id: domain.analytics_id,
        selected:
          selectedDomains.filter((sd: any) => sd.id === domain.analytics_id)
            .length > 0,
      });
    });
    return fd;
  };

  const formatTables = (result: any) => {
    const data: any = [];
    result.forEach((table: any) => {
      const row: any = {
        id: table.id,
        name: table.table_name,
        analytics_name: mappedDomain[table.analytics_id].analytics_name,
        selected:
          selectedTables.filter((st: any) => st.id === table.id).length > 0,
        components: [],
      };
      row.components.push({
        value: table.table_name,
        type: 'checkbox',
        selected: false,
        enabled: null,
        userImageLink: null,
        inputType: null,
      });
      row.components.push({
        value: mappedDomain[table.analytics_id].analytics_name,
        type: 'string',
        selected: false,
        enabled: null,
        userImageLink: null,
        inputType: null,
      });

      data.push(row);
    });
    return data;
  };

  const getTablesForDomains = (domains: any) => {
    // create string of comma separated domain ids from domains array of object which has name and id.
    const domainIds = domains
      .sort((a: any, b: any) => a.id > b.id)
      .map((domain: any) => domain.id)
      .join(',');
    dispatch(setIsLoading(true));
    if (domainIds === '') {
      dispatch(setIsLoading(false));
      setAvailableTables([]);
    } else if (erpDomainTables[`${selectedSourceSystem}-${domainIds}`]) {
      dispatch(setIsLoading(false));
      setAvailableTables(
        formatTables(erpDomainTables[`${selectedSourceSystem}-${domainIds}`]),
      );
    } else {
      const url = `${
        ApiUtilities.getApiServerUrl +
        ApiUtilities.apiPath.getTablesForDomainsOfSourceSystem.url
      }?source_system_id=${selectedSourceSystem}&analytics_id=${domainIds}`;
      apiService.getRequest(url).then((res) => {
        dispatch(setIsLoading(false));
        const edt: any = {};
        edt[`${selectedSourceSystem}-${domainIds}`] = res.data;
        const eedt = structuredClone(erpDomainTables);
        const edtObj = { ...eedt, ...edt };
        dispatch(setErpDomainTable(edtObj));
        const ft = formatTables(res.data);
        setAvailableTables(ft);
      });
    }
    // make api call to get the tables for the default domains.
  };

  const onDomainTableChange = (selectedRows: any) => {
    const tableIds: any = [];
    console.log(selectedRows);
    selectedRows.forEach((row: any) => {
      tableIds.push({
        id: row.id,
        table_name: row.name,
        analytics_name: row.analytics_name,
      });
    });
    dispatch(setSelectedTables(tableIds));
  };

  // Effects
  useEffect(() => {
    setFormattedDomains(formatDomains(domains));
  }, [domains]);

  return (
    <div className="my-2 flex h-[calc(100vh-320px)] flex-row overflow-auto lg:h-full">
      <div className="w-2/6">
        <Paragraph
          // content="Select Domain to run this connection in the context to"
          content="Domain"
          intent="p300"
        />
        <div className="w-48">
          {formattedDomains.length > 0 ? (
            <ListBox
              items={formattedDomains}
              multiselect
              selectedItems={selectedDomains}
              onSelectionChange={(selectedValue) => {
                dispatch(setSelectedDomains(selectedValue));
                getTablesForDomains(structuredClone(selectedValue));
              }}
              name="select"
            />
          ) : (
            ''
          )}
        </div>
      </div>
      <div className="w-4/6">
        <DomainTable
          mode="EDIT"
          tableData={availableTables}
          onRowSelection={onDomainTableChange}
        />
      </div>
    </div>
  );
}
