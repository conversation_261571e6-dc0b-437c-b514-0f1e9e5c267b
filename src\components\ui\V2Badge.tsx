import React from "react";

interface V2BadgeProps {
  intent: "success" | "error" | "warning" | "info" | "neutral" | "counter";
  content: string;
  className?: string;
}

const V2Badge: React.FC<V2BadgeProps> = ({ intent, content, className }) => {
  const badgeColor: any = {
    success: "bg-green-100/10 text-green-100",
    error: "bg-red-100/10 text-red-100",
    warning: "bg-yellow-100/10 text-yellow-100",
    info: "bg-blue-100/10 text-blue-100",
    neutral: "bg-gray-500/10 text-gray-500",
    counter: "bg-blue-200/10 text-blue-200",
  };

  return (
    <div
      className={`w-fit rounded px-4 py-1 text-[10px] font-semibold ${
        badgeColor[intent]
      } ${className || ""}`}
    >
      {content}
    </div>
  );
};

export default V2Badge;
