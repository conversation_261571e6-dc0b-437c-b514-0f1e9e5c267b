import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface sourceSystemState {
  value: [];
}

const initialState: sourceSystemState = {
  value: [],
};

export const sourceSystemSlice = createSlice({
  name: 'sourceSystems',
  initialState,
  reducers: {
    setSourceSystems: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.value = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { setSourceSystems } = sourceSystemSlice.actions;

export default sourceSystemSlice.reducer;
