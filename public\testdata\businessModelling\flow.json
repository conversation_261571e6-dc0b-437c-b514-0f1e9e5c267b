{"raw": {"RAW_SAP_APAC": {"tables": {"T001": {"LAND1": {"col_name": "LAND1"}, "KTOPL": {"col_name": "KTOPL"}, "RCOMP": {"col_name": "RCOMP"}, "BUTXT": {"col_name": "BUTXT"}, "ORT01": {"col_name": "ORT01"}}, "CSKT": {"KTEXT": {"col_name": "KTEXT"}, "LTEXT": {"col_name": "LTEXT"}}, "T005T": {"LANDX": {"col_name": "LANDX"}}, "KNA1": {"KTOKD": {"col_name": "KTOKD"}, "ORT01": {"col_name": "ORT01"}, "NAME1": {"col_name": "NAME1"}, "NAME2": {"col_name": "NAME2"}}, "SCMGATTR_REASONT": {"DESCRIPTION": {"col_name": "DESCRIPTION"}}, "T003T": {"LTEXT": {"col_name": "LTEXT"}}, "SKAT": {"TXT20": {"col_name": "TXT20"}, "TXT50": {"col_name": "TXT50"}}, "SKB1": {"BUKRS": {"col_name": "BUKRS"}}, "MAKT": {"MAKTX": {"col_name": "MAKTX"}}, "CEPC": {"NAME1": {"col_name": "NAME1"}}, "CEPCT": {"LTEXT": {"col_name": "LTEXT"}}, "LFA1": {"KTOKK": {"col_name": "KTOKK"}, "ORT01": {"col_name": "ORT01"}, "NAME1": {"col_name": "NAME1"}, "NAME2": {"col_name": "NAME2"}}, "BKPF": {"BLART": {"col_name": "BLART"}, "BLDAT": {"col_name": "BLDAT"}, "BUDAT": {"col_name": "BUDAT"}, "MONAT": {"col_name": "MONAT"}, "BVORG": {"col_name": "BVORG"}, "XBLNR": {"col_name": "XBLNR"}, "BKTXT": {"col_name": "BKTXT"}, "WAERS": {"col_name": "WAERS"}, "KURSF": {"col_name": "KURSF"}, "KZWRS": {"col_name": "KZWRS"}, "KZKRS": {"col_name": "KZKRS"}, "AWKEY": {"col_name": "AWKEY"}, "HWAER": {"col_name": "HWAER"}, "XSTOV": {"col_name": "XSTOV"}, "STODT": {"col_name": "STODT"}, "KUTY2": {"col_name": "KUTY2"}, "XREF1_HD": {"col_name": "XREF1_HD"}, "XREF2_HD": {"col_name": "XREF2_HD"}, "XREVERSAL": {"col_name": "XREVERSAL"}}, "BSEG": {"ZUONR": {"col_name": "ZUONR"}, "BUKRS": {"col_name": "BUKRS"}, "BELNR": {"col_name": "BELNR"}, "GJAHR": {"col_name": "GJAHR"}, "BUZEI": {"col_name": "BUZEI"}, "AUGDT": {"col_name": "AUGDT"}, "AUGBL": {"col_name": "AUGBL"}, "UMSKZ": {"col_name": "UMSKZ"}, "SHKZG": {"col_name": "SHKZG"}, "MWSK2": {"col_name": "MWSK2"}, "QSSKZ": {"col_name": "QSSKZ"}, "DMBTR": {"col_name": "DMBTR"}, "WRBTR": {"col_name": "WRBTR"}, "PSWBT": {"col_name": "PSWBT"}, "MWSTS": {"col_name": "MWSTS"}, "VBUND": {"col_name": "VBUND"}, "KOSTL": {"col_name": "KOSTL"}, "AUFNR": {"col_name": "AUFNR"}, "VBELN": {"col_name": "VBELN"}, "VBEL2": {"col_name": "VBEL2"}, "POSN2": {"col_name": "POSN2"}, "ANLN2": {"col_name": "ANLN2"}, "SAKNR": {"col_name": "SAKNR"}, "KUNNR": {"col_name": "KUNNR"}, "LIFNR": {"col_name": "LIFNR"}, "ZFBDT": {"col_name": "ZFBDT"}, "ZTERM": {"col_name": "ZTERM"}, "SKNTO": {"col_name": "SKNTO"}, "ZLSCH": {"col_name": "ZLSCH"}, "NEBTR": {"col_name": "NEBTR"}, "MATNR": {"col_name": "MATNR"}, "STCEG": {"col_name": "STCEG"}, "PRCTR": {"col_name": "PRCTR"}, "XREF1": {"col_name": "XREF1"}, "XREF2": {"col_name": "XREF2"}}, "FDM_DCOBJ": {"IS_CONFIRMED": {"col_name": "IS_CONFIRMED"}, "IS_VOIDED": {"col_name": "IS_VOIDED"}}, "SCMG_T_CASE_ATTR": {"CASE_TITLE": {"col_name": "CASE_TITLE"}}}}, "RAW_SAP_Financials": {"tables": {"T001": {"LAND1": {"col_name": "LAND1"}, "KTOPL": {"col_name": "KTOPL"}, "RCOMP": {"col_name": "RCOMP"}, "BUTXT": {"col_name": "BUTXT"}, "ORT01": {"col_name": "ORT01"}}, "CSKT": {"KTEXT": {"col_name": "KTEXT"}, "LTEXT": {"col_name": "LTEXT"}}, "T005T": {"LANDX": {"col_name": "LANDX"}}, "KNA1": {"KTOKD": {"col_name": "KTOKD"}, "ORT01": {"col_name": "ORT01"}, "NAME1": {"col_name": "NAME1"}, "NAME2": {"col_name": "NAME2"}}, "SCMGATTR_REASONT": {"DESCRIPTION": {"col_name": "DESCRIPTION"}}, "T003T": {"LTEXT": {"col_name": "LTEXT"}}, "SKAT": {"TXT20": {"col_name": "TXT20"}, "TXT50": {"col_name": "TXT50"}}, "SKB1": {"BUKRS": {"col_name": "BUKRS"}}, "MAKT": {"MAKTX": {"col_name": "MAKTX"}}, "CEPC": {"NAME1": {"col_name": "NAME1"}}, "CEPCT": {"LTEXT": {"col_name": "LTEXT"}}, "LFA1": {"KTOKK": {"col_name": "KTOKK"}, "ORT01": {"col_name": "ORT01"}, "NAME1": {"col_name": "NAME1"}, "NAME2": {"col_name": "NAME2"}}, "BKPF": {"BLART": {"col_name": "BLART"}, "BLDAT": {"col_name": "BLDAT"}, "BUDAT": {"col_name": "BUDAT"}, "MONAT": {"col_name": "MONAT"}, "BVORG": {"col_name": "BVORG"}, "XBLNR": {"col_name": "XBLNR"}, "BKTXT": {"col_name": "BKTXT"}, "WAERS": {"col_name": "WAERS"}, "KURSF": {"col_name": "KURSF"}, "KZWRS": {"col_name": "KZWRS"}, "KZKRS": {"col_name": "KZKRS"}, "AWKEY": {"col_name": "AWKEY"}, "HWAER": {"col_name": "HWAER"}, "XSTOV": {"col_name": "XSTOV"}, "STODT": {"col_name": "STODT"}, "KUTY2": {"col_name": "KUTY2"}, "XREF1_HD": {"col_name": "XREF1_HD"}, "XREF2_HD": {"col_name": "XREF2_HD"}, "XREVERSAL": {"col_name": "XREVERSAL"}}, "BSEG": {"ZUONR": {"col_name": "ZUONR"}, "BUKRS": {"col_name": "BUKRS"}, "BELNR": {"col_name": "BELNR"}, "GJAHR": {"col_name": "GJAHR"}, "BUZEI": {"col_name": "BUZEI"}, "AUGDT": {"col_name": "AUGDT"}, "AUGBL": {"col_name": "AUGBL"}, "UMSKZ": {"col_name": "UMSKZ"}, "SHKZG": {"col_name": "SHKZG"}, "MWSK2": {"col_name": "MWSK2"}, "QSSKZ": {"col_name": "QSSKZ"}, "DMBTR": {"col_name": "DMBTR"}, "WRBTR": {"col_name": "WRBTR"}, "PSWBT": {"col_name": "PSWBT"}, "MWSTS": {"col_name": "MWSTS"}, "VBUND": {"col_name": "VBUND"}, "KOSTL": {"col_name": "KOSTL"}, "AUFNR": {"col_name": "AUFNR"}, "VBELN": {"col_name": "VBELN"}, "VBEL2": {"col_name": "VBEL2"}, "POSN2": {"col_name": "POSN2"}, "ANLN2": {"col_name": "ANLN2"}, "SAKNR": {"col_name": "SAKNR"}, "KUNNR": {"col_name": "KUNNR"}, "LIFNR": {"col_name": "LIFNR"}, "ZFBDT": {"col_name": "ZFBDT"}, "ZTERM": {"col_name": "ZTERM"}, "SKNTO": {"col_name": "SKNTO"}, "ZLSCH": {"col_name": "ZLSCH"}, "NEBTR": {"col_name": "NEBTR"}, "MATNR": {"col_name": "MATNR"}, "STCEG": {"col_name": "STCEG"}, "PRCTR": {"col_name": "PRCTR"}, "XREF1": {"col_name": "XREF1"}, "XREF2": {"col_name": "XREF2"}}, "FDM_DCOBJ": {"IS_CONFIRMED": {"col_name": "IS_CONFIRMED"}, "IS_VOIDED": {"col_name": "IS_VOIDED"}}, "SCMG_T_CASE_ATTR": {"CASE_TITLE": {"col_name": "CASE_TITLE"}}}}, "RAW_Oracle_Financials": {"tables": {"HR_LOCATIONS_ALL": {"COUNTRY": {"col_name": "COUNTRY"}, "LOC_INFORMATION15": {"col_name": "LOC_INFORMATION15"}}, "GL_LEDGERS": {"CHART_OF_ACCOUNTS_ID": {"col_name": "CHART_OF_ACCOUNTS_ID"}, "CURRENCY_CODE": {"col_name": "CURRENCY_CODE"}}, "FND_FLEX_VALUES_TL": {"DESCRIPTION": {"col_name": "DESCRIPTION"}}, "FND_TERRITORIES_TL": {"TERRITORY_SHORT_NAME": {"col_name": "TERRITORY_SHORT_NAME"}}, "HZ_PARTIES": {"PARTY_TYPE": {"col_name": "PARTY_TYPE"}, "PARTY_NAME": {"col_name": "PARTY_NAME"}, "TAX_REFERENCE": {"col_name": "TAX_REFERENCE"}}, "HZ_LOCATIONS": {"CITY": {"col_name": "CITY"}}, "RA_CUST_TRX_TYPES_ALL": {"DESCRIPTION": {"col_name": "DESCRIPTION"}, "NAME": {"col_name": "NAME"}}, "FND_LOOKUP_VALUES": {"MEANING": {"col_name": "MEANING"}}, "GL_CODE_COMBINATIONS": {"SEGMENT1": {"col_name": "SEGMENT1"}, "SEGMENT2": {"col_name": "SEGMENT2"}, "SEGMENT3": {"col_name": "SEGMENT3"}}, "MTL_SYSTEM_ITEMS_TL": {"LONG_DESCRIPTION": {"col_name": "LONG_DESCRIPTION"}}, "AP_SUPPLIERS": {"VENDOR_TYPE_LOOKUP_CODE": {"col_name": "VENDOR_TYPE_LOOKUP_CODE"}, "VENDOR_NAME": {"col_name": "VENDOR_NAME"}, "VENDOR_NAME_ALT": {"col_name": "VENDOR_NAME_ALT"}, "CUSTOMER_NUM": {"col_name": "CUSTOMER_NUM"}, "SEGMENT1": {"col_name": "SEGMENT1"}}, "AP_SUPPLIER_SITES_ALL": {"CITY": {"col_name": "CITY"}, "VAT_REGISTRATION_NUM": {"col_name": "VAT_REGISTRATION_NUM"}}, "AP_INVOICES_ALL": {"SOURCE": {"col_name": "SOURCE"}, "INVOICE_DATE": {"col_name": "INVOICE_DATE"}, "GL_DATE": {"col_name": "GL_DATE"}, "INVOICE_NUM": {"col_name": "INVOICE_NUM"}, "DESCRIPTION": {"col_name": "DESCRIPTION"}, "CANCELED_DATE": {"col_name": "CANCELED_DATE"}, "INVOICE_TYPE_LOOKUP_CODE": {"col_name": "INVOICE_TYPE_LOOKUP_CODE"}, "TERMS_DATE": {"col_name": "TERMS_DATE"}}, "RA_CUSTOMER_TRX_ALL": {"TRX_DATE": {"col_name": "TRX_DATE"}, "TRX_NUMBER": {"col_name": "TRX_NUMBER"}, "INVOICE_CURRENCY_CODE": {"col_name": "INVOICE_CURRENCY_CODE"}, "EXCHANGE_RATE": {"col_name": "EXCHANGE_RATE"}, "CUSTOMER_TRX_ID": {"col_name": "CUSTOMER_TRX_ID"}, "TERM_DUE_DATE": {"col_name": "TERM_DUE_DATE"}}, "XLA_AE_LINES": {"ACCOUNTING_DATE": {"col_name": "ACCOUNTING_DATE"}, "YEAR": {"col_name": "YEAR"}, "AE_LINE_NUM": {"col_name": "AE_LINE_NUM"}, "ACCOUNTED_AMOUNT": {"col_name": "ACCOUNTED_AMOUNT"}, "ENTERED_AMOUNT": {"col_name": "ENTERED_AMOUNT"}}, "GL_JE_LINES": {"SUBLEDGER_DOC_SEQUENCE_VALUE": {"col_name": "SUBLEDGER_DOC_SEQUENCE_VALUE"}}, "AP_INVOICE_PAYMENTS_ALL": {"ACCOUNTING_DATE": {"col_name": "ACCOUNTING_DATE"}}, "AP_INVOICE_LINES_ALL": {"TAX_RATE_CODE": {"col_name": "TAX_RATE_CODE"}}, "AP_TAX_CODES_ALL": {"TAX_RATE": {"col_name": "TAX_RATE"}}, "RA_CUSTOMER_TRX_LINES_ALL": {"SALES_ORDER": {"col_name": "SALES_ORDER"}, "CUSTOMER_TRX_LINE_ID": {"col_name": "CUSTOMER_TRX_LINE_ID"}}, "HZ_CUST_ACCOUNTS_ALL": {"ACCOUNT_NUMBER": {"col_name": "ACCOUNT_NUMBER"}}, "AP_TERMS_TL": {"TYPE": {"col_name": "TYPE"}}, "AR_RECEIPT_CLASSES": {"NAME": {"col_name": "NAME"}}, "AR_PAYMENT_SCHEDULES_ALL": {"AMOUNT_DUE_ORIGINAL": {"col_name": "AMOUNT_DUE_ORIGINAL"}}, "MTL_SYSTEM_ITEMS_B": {"SEGMENT1": {"col_name": "SEGMENT1"}}, "HR_ALL_ORGANIZATION_UNITS_TL": {"NAME": {"col_name": "NAME"}}}}}, "enrich": {"tables": {"DIM_COMPANY": {"COMPANY_COUNTRY_KEY": {"col_name": "COMPANY_COUNTRY_KEY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T001", "col_name": "LAND1"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T001", "col_name": "LAND1"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HR_LOCATIONS_ALL", "col_name": "COUNTRY"}]}, "COMPANY_CHART_OF_ACCTS": {"col_name": "COMPANY_CHART_OF_ACCTS", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T001", "col_name": "KTOPL"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T001", "col_name": "KTOPL"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_LEDGERS", "col_name": "CHART_OF_ACCOUNTS_ID"}]}, "COMPANY_ID": {"col_name": "COMPANY_ID", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T001", "col_name": "RCOMP"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T001", "col_name": "RCOMP"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_LEDGERS", "col_name": "CHART_OF_ACCOUNTS_ID"}]}, "COMPANY_NAME": {"col_name": "COMPANY_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T001", "col_name": "BUTXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T001", "col_name": "BUTXT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HR_ALL_ORGANIZATION_UNITS_TL", "col_name": "NAME"}]}, "COMPANY_CITY": {"col_name": "COMPANY_CITY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T001", "col_name": "ORT01"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T001", "col_name": "ORT01"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HR_LOCATIONS_ALL", "col_name": "LOC_INFORMATION15"}]}}, "DIM_COST_CENTER": {"COST_CENTER_GENERAL_NAME": {"col_name": "COST_CENTER_GENERAL_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "CSKT", "col_name": "KTEXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "CSKT", "col_name": "KTEXT"}]}, "COST_CENTER_DESCRIPTION": {"col_name": "COST_CENTER_DESCRIPTION", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "CSKT", "col_name": "LTEXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "CSKT", "col_name": "LTEXT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_FLEX_VALUES_TL", "col_name": "DESCRIPTION"}]}}, "DIM_COUNTRY_CODES": {"COUNTRY_NAME": {"col_name": "COUNTRY_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T005T", "col_name": "LANDX"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T005T", "col_name": "LANDX"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_TERRITORIES_TL", "col_name": "TERRITORY_SHORT_NAME"}]}}, "DIM_CUSTOMER_MASTER": {"CUSTOMER_ACCOUNT_GROUP": {"col_name": "CUSTOMER_ACCOUNT_GROUP", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "KNA1", "col_name": "KTOKD"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "KNA1", "col_name": "KTOKD"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HZ_PARTIES", "col_name": "PARTY_TYPE"}]}, "CUSTOMER_CITY": {"col_name": "CUSTOMER_CITY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "KNA1", "col_name": "ORT01"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "KNA1", "col_name": "ORT01"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HZ_LOCATIONS", "col_name": "CITY"}]}, "CUSTOMER_NAME": {"col_name": "CUSTOMER_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "KNA1", "col_name": "NAME1"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "KNA1", "col_name": "NAME1"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HZ_PARTIES", "col_name": "PARTY_NAME"}]}, "CUSTOMER_NAME_2": {"col_name": "CUSTOMER_NAME_2", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "KNA1", "col_name": "NAME2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "KNA1", "col_name": "NAME2"}]}}, "DIM_DISPUTE_REASON": {"DISPUE_REASON_CODE_DESCRIPTION": {"col_name": "DISPUE_REASON_CODE_DESCRIPTION", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "SCMGATTR_REASONT", "col_name": "DESCRIPTION"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "SCMGATTR_REASONT", "col_name": "DESCRIPTION"}]}}, "DIM_DOCUMENT_TYPE": {"DOCUMENT_TYPE_DESC": {"col_name": "DOCUMENT_TYPE_DESC", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "T003T", "col_name": "LTEXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "T003T", "col_name": "LTEXT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUST_TRX_TYPES_ALL", "col_name": "DESCRIPTION"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_LOOKUP_VALUES", "col_name": "MEANING"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_LOOKUP_VALUES", "col_name": "MEANING"}]}}, "DIM_GL_MASTER": {"GL_SHORT_TEXT": {"col_name": "GL_SHORT_TEXT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "SKAT", "col_name": "TXT20"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "SKAT", "col_name": "TXT20"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_FLEX_VALUES_TL", "col_name": "DESCRIPTION"}]}, "GL_COMPANY_CODE": {"col_name": "GL_COMPANY_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "SKB1", "col_name": "BUKRS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "SKB1", "col_name": "BUKRS"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_CODE_COMBINATIONS", "col_name": "SEGMENT1"}]}, "GL_LONG_TEXT": {"col_name": "GL_LONG_TEXT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "SKAT", "col_name": "TXT50"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "SKAT", "col_name": "TXT50"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_FLEX_VALUES_TL", "col_name": "DESCRIPTION"}]}}, "DIM_MATERIAL_MASTER": {"MATERIAL_DESCRIPTION": {"col_name": "MATERIAL_DESCRIPTION", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "MAKT", "col_name": "MAKTX"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "MAKT", "col_name": "MAKTX"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "MTL_SYSTEM_ITEMS_TL", "col_name": "LONG_DESCRIPTION"}]}}, "DIM_PROFIT_CENTER_MASTER": {"PROFIT_CENTER_NAME": {"col_name": "PROFIT_CENTER_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "CEPC", "col_name": "NAME1"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "CEPC", "col_name": "NAME1"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_FLEX_VALUES_TL", "col_name": "DESCRIPTION"}]}, "PROFIT_CENTER_LONG_TEXT": {"col_name": "PROFIT_CENTER_LONG_TEXT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "CEPCT", "col_name": "LTEXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "CEPCT", "col_name": "LTEXT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "FND_FLEX_VALUES_TL", "col_name": "DESCRIPTION"}]}}, "DIM_VENDOR_MASTER": {"VENDOR_ACC_GROUP": {"col_name": "VENDOR_ACC_GROUP", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "LFA1", "col_name": "KTOKK"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "LFA1", "col_name": "KTOKK"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIERS", "col_name": "VENDOR_TYPE_LOOKUP_CODE"}]}, "VENDOR_CITY": {"col_name": "VENDOR_CITY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "LFA1", "col_name": "ORT01"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "LFA1", "col_name": "ORT01"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIER_SITES_ALL", "col_name": "CITY"}]}, "VENDOR_NAME": {"col_name": "VENDOR_NAME", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "LFA1", "col_name": "NAME1"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "LFA1", "col_name": "NAME1"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIERS", "col_name": "VENDOR_NAME"}]}, "VENDOR_NAME_2": {"col_name": "VENDOR_NAME_2", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "LFA1", "col_name": "NAME2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "LFA1", "col_name": "NAME2"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIERS", "col_name": "VENDOR_NAME_ALT"}]}}, "FACT_ACCOUNTING_DOCUMENT_HEADER": {"DOCUMENT_TYPE": {"col_name": "DOCUMENT_TYPE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "BLART"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "BLART"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUST_TRX_TYPES_ALL", "col_name": "NAME"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "SOURCE"}]}, "DOCUMENT_DATE": {"col_name": "DOCUMENT_DATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "BLDAT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "BLDAT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "TRX_DATE"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "INVOICE_DATE"}]}, "POSTING_DATE": {"col_name": "POSTING_DATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "BUDAT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "BUDAT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ACCOUNTING_DATE"}]}, "POSTING_PERIOD": {"col_name": "POSTING_PERIOD", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "MONAT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "MONAT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ACCOUNTING_DATE"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "GL_DATE"}]}, "CROSS_CC_DOC_NO": {"col_name": "CROSS_CC_DOC_NO", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "BVORG"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "BVORG"}]}, "REFERENCE_TEXT": {"col_name": "REFERENCE_TEXT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "XBLNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "XBLNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "TRX_NUMBER"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "INVOICE_NUM"}]}, "DOC_HEADER_TEXT": {"col_name": "DOC_HEADER_TEXT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "BKTXT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "BKTXT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "DESCRIPTION"}]}, "CURRENCY_KEY": {"col_name": "CURRENCY_KEY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "WAERS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "WAERS"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "INVOICE_CURRENCY_CODE"}]}, "EXCHANGE_RATE": {"col_name": "EXCHANGE_RATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "KURSF"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "KURSF"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "EXCHANGE_RATE"}]}, "GROUP_CURRENCY_KEY": {"col_name": "GROUP_CURRENCY_KEY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "KZWRS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "KZWRS"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_LEDGERS", "col_name": "CURRENCY_CODE"}]}, "GROUP_EXCH_RATE": {"col_name": "GROUP_EXCH_RATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "KZKRS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "KZKRS"}]}, "REFERENCE_KEY": {"col_name": "REFERENCE_KEY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "AWKEY"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "AWKEY"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "CUSTOMER_TRX_ID"}]}, "LOCAL_CURRENCY_KEY": {"col_name": "LOCAL_CURRENCY_KEY", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "HWAER"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "HWAER"}]}, "REVERSAL_FLAG": {"col_name": "REVERSAL_FLAG", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "XSTOV"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "XSTOV"}]}, "REVERSAL_DATE": {"col_name": "REVERSAL_DATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "STODT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "STODT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "CANCELED_DATE"}]}, "EXCH_RATE_TYPE": {"col_name": "EXCH_RATE_TYPE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "KUTY2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "KUTY2"}]}, "REF_KEY_1": {"col_name": "REF_KEY_1", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "XREF1_HD"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "XREF1_HD"}]}, "REF_KEY_2": {"col_name": "REF_KEY_2", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "XREF2_HD"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "XREF2_HD"}]}, "REVERSAL_IND": {"col_name": "REVERSAL_IND", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BKPF", "col_name": "XREVERSAL"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BKPF", "col_name": "XREVERSAL"}]}}, "FACT_ACCOUNTING_DOCUMENT_LINES": {"SRC_SYSTEM_NAME": {"col_name": "SRC_SYSTEM_NAME", "connection_info": []}, "GL_FLAG": {"col_name": "GL_FLAG", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "ZUONR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "ZUONR"}]}, "COMPANY_CODE": {"col_name": "COMPANY_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "BUKRS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "BUKRS"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_CODE_COMBINATIONS", "col_name": "SEGMENT1"}]}, "DOCUMENT_NUMBER": {"col_name": "DOCUMENT_NUMBER", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "BELNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "BELNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "TRX_NUMBER"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "INVOICE_NUM"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_JE_LINES", "col_name": "SUBLEDGER_DOC_SEQUENCE_VALUE"}]}, "FISCAL_YEAR": {"col_name": "FISCAL_YEAR", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "GJAHR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "GJAHR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "YEAR"}]}, "DOC_LINE_ITEM": {"col_name": "DOC_LINE_ITEM", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "BUZEI"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "BUZEI"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "AE_LINE_NUM"}]}, "CLEARING_DATE": {"col_name": "CLEARING_DATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "AUGDT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "AUGDT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICE_PAYMENTS_ALL", "col_name": "ACCOUNTING_DATE"}]}, "CLEARING_DOC": {"col_name": "CLEARING_DOC", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "AUGBL"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "AUGBL"}]}, "IND_SPGL": {"col_name": "IND_SPGL", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "UMSKZ"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "UMSKZ"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "INVOICE_TYPE_LOOKUP_CODE"}]}, "IND_DRCR": {"col_name": "IND_DRCR", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "SHKZG"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "SHKZG"}]}, "TAX_CODE": {"col_name": "TAX_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "MWSK2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "MWSK2"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICE_LINES_ALL", "col_name": "TAX_RATE_CODE"}]}, "WHT_TAX_CODE": {"col_name": "WHT_TAX_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "QSSKZ"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "QSSKZ"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_TAX_CODES_ALL", "col_name": "TAX_RATE"}]}, "AMOUNT_IN_LC": {"col_name": "AMOUNT_IN_LC", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "DMBTR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "DMBTR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ACCOUNTED_AMOUNT"}]}, "AMOUNT_IN_DC": {"col_name": "AMOUNT_IN_DC", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "WRBTR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "WRBTR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ENTERED_AMOUNT"}]}, "GL_AMOUNT": {"col_name": "GL_AMOUNT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "PSWBT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "PSWBT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ACCOUNTED_AMOUNT"}]}, "TAX_AMOUNT": {"col_name": "TAX_AMOUNT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "MWSTS"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "MWSTS"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ENTERED_AMOUNT"}]}, "TRADING_PARTNER_CODE": {"col_name": "TRADING_PARTNER_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "VBUND"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "VBUND"}]}, "COST_CENTER_CODE": {"col_name": "COST_CENTER_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "KOSTL"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "KOSTL"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_CODE_COMBINATIONS", "col_name": "SEGMENT2"}]}, "INTERNAL_ORDER_CODE": {"col_name": "INTERNAL_ORDER_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "AUFNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "AUFNR"}]}, "BILLING_DOC": {"col_name": "BILLING_DOC", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "VBELN"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "VBELN"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "TRX_NUMBER"}]}, "SALES_DOCUMENT_NUMBER": {"col_name": "SALES_DOCUMENT_NUMBER", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "VBEL2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "VBEL2"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_LINES_ALL", "col_name": "SALES_ORDER"}]}, "SALES_DOCUMENT_ITEM": {"col_name": "SALES_DOCUMENT_ITEM", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "POSN2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "POSN2"}]}, "ASSET_SUBNUMBER": {"col_name": "ASSET_SUBNUMBER", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "ANLN2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "ANLN2"}]}, "GL_ACCOUNT_NUMBER": {"col_name": "GL_ACCOUNT_NUMBER", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "SAKNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "SAKNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_CODE_COMBINATIONS", "col_name": "SEGMENT3"}]}, "CUSTOMER_CODE": {"col_name": "CUSTOMER_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "KUNNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "KUNNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HZ_CUST_ACCOUNTS_ALL", "col_name": "ACCOUNT_NUMBER"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIERS", "col_name": "CUSTOMER_NUM"}]}, "VENDOR_CODE": {"col_name": "VENDOR_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "LIFNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "LIFNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIERS", "col_name": "SEGMENT1"}]}, "INVOICE_BASELINE_DATE": {"col_name": "INVOICE_BASELINE_DATE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "ZFBDT"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "ZFBDT"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_ALL", "col_name": "TERM_DUE_DATE"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_INVOICES_ALL", "col_name": "TERMS_DATE"}]}, "PAYMENT_TERM": {"col_name": "PAYMENT_TERM", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "ZTERM"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "ZTERM"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_TERMS_TL", "col_name": "TYPE"}]}, "DISCOUNT_AMOUNT": {"col_name": "DISCOUNT_AMOUNT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "SKNTO"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "SKNTO"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "XLA_AE_LINES", "col_name": "ACCOUNTED_AMOUNT"}]}, "PAYMENT_METHOD": {"col_name": "PAYMENT_METHOD", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "ZLSCH"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "ZLSCH"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AR_RECEIPT_CLASSES", "col_name": "NAME"}]}, "PAYMENT_AMOUNT": {"col_name": "PAYMENT_AMOUNT", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "NEBTR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "NEBTR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AR_PAYMENT_SCHEDULES_ALL", "col_name": "AMOUNT_DUE_ORIGINAL"}]}, "NET_DUE_DATE": {"col_name": "NET_DUE_DATE", "connection_info": []}, "MATERIAL_NUMBER": {"col_name": "MATERIAL_NUMBER", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "MATNR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "MATNR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "MTL_SYSTEM_ITEMS_B", "col_name": "SEGMENT1"}]}, "VAT_REG_NO": {"col_name": "VAT_REG_NO", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "STCEG"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "STCEG"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "HZ_PARTIES", "col_name": "TAX_REFERENCE"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "AP_SUPPLIER_SITES_ALL", "col_name": "VAT_REGISTRATION_NUM"}]}, "PROFIT_CENTER_CODE": {"col_name": "PROFIT_CENTER_CODE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "PRCTR"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "PRCTR"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "GL_CODE_COMBINATIONS", "col_name": "SEGMENT1"}]}, "REFERENCE_KEY1": {"col_name": "REFERENCE_KEY1", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "XREF1"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "XREF1"}, {"dataset_name": "RAW_Oracle_Financials", "table_name": "RA_CUSTOMER_TRX_LINES_ALL", "col_name": "CUSTOMER_TRX_LINE_ID"}]}, "REFERENCE_KEY2": {"col_name": "REFERENCE_KEY2", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "BSEG", "col_name": "XREF2"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "BSEG", "col_name": "XREF2"}]}}, "FACT_DISPUTE_CASE_DETAILS": {"DISPUTE_IS_CONFIRMED": {"col_name": "DISPUTE_IS_CONFIRMED", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "FDM_DCOBJ", "col_name": "IS_CONFIRMED"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "FDM_DCOBJ", "col_name": "IS_CONFIRMED"}]}, "DISPUTE_IS_VOIDED": {"col_name": "DISPUTE_IS_VOIDED", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "FDM_DCOBJ", "col_name": "IS_VOIDED"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "FDM_DCOBJ", "col_name": "IS_VOIDED"}]}, "DISPUTE_CASE_TITLE": {"col_name": "DISPUTE_CASE_TITLE", "connection_info": [{"dataset_name": "RAW_SAP_APAC", "table_name": "SCMG_T_CASE_ATTR", "col_name": "CASE_TITLE"}, {"dataset_name": "RAW_SAP_Financials", "table_name": "SCMG_T_CASE_ATTR", "col_name": "CASE_TITLE"}]}}}}, "consume": {"tables": {"GL_ACCOUNTING_VIEW": {"COMPANY_COUNTRY_KEY": {"col_name": "COMPANY_COUNTRY_KEY", "connection_info": [{"logical_entity_name": "DIM_COMPANY", "table_name": "DIM_COMPANY", "table_id": 315, "col_id": 4562, "col_name": "COMPANY_COUNTRY_KEY"}]}, "COMPANY_CHART_OF_ACCTS": {"col_name": "COMPANY_CHART_OF_ACCTS", "connection_info": [{"logical_entity_name": "DIM_COMPANY", "table_name": "DIM_COMPANY", "table_id": 315, "col_id": 4556, "col_name": "COMPANY_CHART_OF_ACCTS"}]}, "COMPANY_ID": {"col_name": "COMPANY_ID", "connection_info": [{"logical_entity_name": "DIM_COMPANY", "table_name": "DIM_COMPANY", "table_id": 315, "col_id": 4565, "col_name": "COMPANY_ID"}]}, "COST_CENTER_GENERAL_NAME": {"col_name": "COST_CENTER_GENERAL_NAME", "connection_info": [{"logical_entity_name": "DIM_COST_CENTER", "table_name": "DIM_COST_CENTER", "table_id": 318, "col_id": 4599, "col_name": "COST_CENTER_GENERAL_NAME"}]}, "COST_CENTER_DESCRIPTION": {"col_name": "COST_CENTER_DESCRIPTION", "connection_info": [{"logical_entity_name": "DIM_COST_CENTER", "table_name": "DIM_COST_CENTER", "table_id": 318, "col_id": 4598, "col_name": "COST_CENTER_DESCRIPTION"}]}, "COUNTRY_NAME": {"col_name": "COUNTRY_NAME", "connection_info": [{"logical_entity_name": "DIM_COUNTRY_CODES", "table_name": "DIM_COUNTRY_CODES", "table_id": 320, "col_id": 4617, "col_name": "COUNTRY_NAME"}]}, "CUSTOMER_ACCOUNT_GROUP": {"col_name": "CUSTOMER_ACCOUNT_GROUP", "connection_info": [{"logical_entity_name": "DIM_CUSTOMER_MASTER", "table_name": "DIM_CUSTOMER_MASTER", "table_id": 321, "col_id": 4621, "col_name": "CUSTOMER_ACCOUNT_GROUP"}]}, "CUSTOMER_CITY": {"col_name": "CUSTOMER_CITY", "connection_info": [{"logical_entity_name": "DIM_CUSTOMER_MASTER", "table_name": "DIM_CUSTOMER_MASTER", "table_id": 321, "col_id": 4627, "col_name": "CUSTOMER_CITY"}]}, "CUSTOMER_NAME": {"col_name": "CUSTOMER_NAME", "connection_info": [{"logical_entity_name": "DIM_CUSTOMER_MASTER", "table_name": "DIM_CUSTOMER_MASTER", "table_id": 321, "col_id": 4631, "col_name": "CUSTOMER_NAME"}]}, "CUSTOMER_NAME_2": {"col_name": "CUSTOMER_NAME_2", "connection_info": [{"logical_entity_name": "DIM_CUSTOMER_MASTER", "table_name": "DIM_CUSTOMER_MASTER", "table_id": 321, "col_id": 4632, "col_name": "CUSTOMER_NAME_2"}]}, "DISPUE_REASON_CODE_DESCRIPTION": {"col_name": "DISPUE_REASON_CODE_DESCRIPTION", "connection_info": [{"logical_entity_name": "DIM_DISPUTE_REASON", "table_name": "DIM_DISPUTE_REASON", "table_id": 322, "col_id": 4652, "col_name": "DISPUE_REASON_CODE_DESCRIPTION"}]}, "DOCUMENT_TYPE_DESC": {"col_name": "DOCUMENT_TYPE_DESC", "connection_info": [{"logical_entity_name": "DIM_DOCUMENT_TYPE", "table_name": "DIM_DOCUMENT_TYPE", "table_id": 323, "col_id": 4656, "col_name": "DOCUMENT_TYPE_DESC"}]}, "GL_SHORT_TEXT": {"col_name": "GL_SHORT_TEXT", "connection_info": [{"logical_entity_name": "DIM_GL_MASTER", "table_name": "DIM_GL_MASTER", "table_id": 325, "col_id": 4683, "col_name": "GL_SHORT_TEXT"}]}, "GL_COMPANY_CODE": {"col_name": "GL_COMPANY_CODE", "connection_info": [{"logical_entity_name": "DIM_GL_MASTER", "table_name": "DIM_GL_MASTER", "table_id": 325, "col_id": 4681, "col_name": "GL_COMPANY_CODE"}]}, "GL_LONG_TEXT": {"col_name": "GL_LONG_TEXT", "connection_info": [{"logical_entity_name": "DIM_GL_MASTER", "table_name": "DIM_GL_MASTER", "table_id": 325, "col_id": 4682, "col_name": "GL_LONG_TEXT"}]}, "MATERIAL_DESCRIPTION": {"col_name": "MATERIAL_DESCRIPTION", "connection_info": [{"logical_entity_name": "DIM_MATERIAL_MASTER", "table_name": "DIM_MATERIAL_MASTER", "table_id": 326, "col_id": 4706, "col_name": "MATERIAL_DESCRIPTION"}]}, "PROFIT_CENTER_NAME": {"col_name": "PROFIT_CENTER_NAME", "connection_info": [{"logical_entity_name": "DIM_PROFIT_CENTER_MASTER", "table_name": "DIM_PROFIT_CENTER_MASTER", "table_id": 331, "col_id": 4775, "col_name": "PROFIT_CENTER_NAME"}]}, "PROFIT_CENTER_LONG_TEXT": {"col_name": "PROFIT_CENTER_LONG_TEXT", "connection_info": [{"logical_entity_name": "DIM_PROFIT_CENTER_MASTER", "table_name": "DIM_PROFIT_CENTER_MASTER", "table_id": 331, "col_id": 4774, "col_name": "PROFIT_CENTER_LONG_TEXT"}]}, "VENDOR_ACC_GROUP": {"col_name": "VENDOR_ACC_GROUP", "connection_info": [{"logical_entity_name": "DIM_VENDOR_MASTER", "table_name": "DIM_VENDOR_MASTER", "table_id": 335, "col_id": 4822, "col_name": "VENDOR_ACC_GROUP"}]}, "VENDOR_CITY": {"col_name": "VENDOR_CITY", "connection_info": [{"logical_entity_name": "DIM_VENDOR_MASTER", "table_name": "DIM_VENDOR_MASTER", "table_id": 335, "col_id": 4829, "col_name": "VENDOR_CITY"}]}, "VENDOR_NAME": {"col_name": "VENDOR_NAME", "connection_info": [{"logical_entity_name": "DIM_VENDOR_MASTER", "table_name": "DIM_VENDOR_MASTER", "table_id": 335, "col_id": 4837, "col_name": "VENDOR_NAME"}]}, "VENDOR_NAME_2": {"col_name": "VENDOR_NAME_2", "connection_info": [{"logical_entity_name": "DIM_VENDOR_MASTER", "table_name": "DIM_VENDOR_MASTER", "table_id": 335, "col_id": 4838, "col_name": "VENDOR_NAME_2"}]}, "DOCUMENT_TYPE": {"col_name": "DOCUMENT_TYPE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4881, "col_name": "DOCUMENT_TYPE"}]}, "DOCUMENT_DATE": {"col_name": "DOCUMENT_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4879, "col_name": "DOCUMENT_DATE"}]}, "POSTING_DATE": {"col_name": "POSTING_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4896, "col_name": "POSTING_DATE"}]}, "POSTING_PERIOD": {"col_name": "POSTING_PERIOD", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4897, "col_name": "POSTING_PERIOD"}]}, "CROSS_CC_DOC_NO": {"col_name": "CROSS_CC_DOC_NO", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4875, "col_name": "CROSS_CC_DOC_NO"}]}, "REFERENCE_TEXT": {"col_name": "REFERENCE_TEXT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4901, "col_name": "REFERENCE_TEXT"}]}, "DOC_HEADER_TEXT": {"col_name": "DOC_HEADER_TEXT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4877, "col_name": "DOC_HEADER_TEXT"}]}, "CURRENCY_KEY": {"col_name": "CURRENCY_KEY", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4876, "col_name": "CURRENCY_KEY"}]}, "EXCHANGE_RATE": {"col_name": "EXCHANGE_RATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4884, "col_name": "EXCHANGE_RATE"}]}, "GROUP_CURRENCY_KEY": {"col_name": "GROUP_CURRENCY_KEY", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4887, "col_name": "GROUP_CURRENCY_KEY"}]}, "GROUP_EXCH_RATE": {"col_name": "GROUP_EXCH_RATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4888, "col_name": "GROUP_EXCH_RATE"}]}, "REFERENCE_KEY": {"col_name": "REFERENCE_KEY", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4900, "col_name": "REFERENCE_KEY"}]}, "LOCAL_CURRENCY_KEY": {"col_name": "LOCAL_CURRENCY_KEY", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4895, "col_name": "LOCAL_CURRENCY_KEY"}]}, "REVERSAL_FLAG": {"col_name": "REVERSAL_FLAG", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4903, "col_name": "REVERSAL_FLAG"}]}, "REVERSAL_DATE": {"col_name": "REVERSAL_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4902, "col_name": "REVERSAL_DATE"}]}, "EXCH_RATE_TYPE": {"col_name": "EXCH_RATE_TYPE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4883, "col_name": "EXCH_RATE_TYPE"}]}, "REF_KEY_1": {"col_name": "REF_KEY_1", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4898, "col_name": "REF_KEY_1"}]}, "REF_KEY_2": {"col_name": "REF_KEY_2", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4899, "col_name": "REF_KEY_2"}]}, "REVERSAL_IND": {"col_name": "REVERSAL_IND", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "table_id": 337, "col_id": 4904, "col_name": "REVERSAL_IND"}]}, "SRC_SYSTEM_NAME": {"col_name": "SRC_SYSTEM_NAME", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4996, "col_name": "SRC_SYSTEM_NAME"}]}, "GL_FLAG": {"col_name": "GL_FLAG", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4910, "col_name": "GL_FLAG"}]}, "COMPANY_CODE": {"col_name": "COMPANY_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4933, "col_name": "COMPANY_CODE"}]}, "DOCUMENT_NUMBER": {"col_name": "DOCUMENT_NUMBER", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4947, "col_name": "DOCUMENT_NUMBER"}]}, "FISCAL_YEAR": {"col_name": "FISCAL_YEAR", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4950, "col_name": "FISCAL_YEAR"}]}, "DOC_LINE_ITEM": {"col_name": "DOC_LINE_ITEM", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4944, "col_name": "DOC_LINE_ITEM"}]}, "CLEARING_DATE": {"col_name": "CLEARING_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4930, "col_name": "CLEARING_DATE"}]}, "CLEARING_DOC": {"col_name": "CLEARING_DOC", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4931, "col_name": "CLEARING_DOC"}]}, "IND_SPGL": {"col_name": "IND_SPGL", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4957, "col_name": "IND_SPGL"}]}, "IND_DRCR": {"col_name": "IND_DRCR", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4956, "col_name": "IND_DRCR"}]}, "TAX_CODE": {"col_name": "TAX_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4998, "col_name": "TAX_CODE"}]}, "WHT_TAX_CODE": {"col_name": "WHT_TAX_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 5005, "col_name": "WHT_TAX_CODE"}]}, "AMOUNT_IN_LC": {"col_name": "AMOUNT_IN_LC", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4919, "col_name": "AMOUNT_IN_LC"}]}, "AMOUNT_IN_DC": {"col_name": "AMOUNT_IN_DC", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4917, "col_name": "AMOUNT_IN_DC"}]}, "GL_AMOUNT": {"col_name": "GL_AMOUNT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4954, "col_name": "GL_AMOUNT"}]}, "TAX_AMOUNT": {"col_name": "TAX_AMOUNT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4997, "col_name": "TAX_AMOUNT"}]}, "TRADING_PARTNER_CODE": {"col_name": "TRADING_PARTNER_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4999, "col_name": "TRADING_PARTNER_CODE"}]}, "COST_CENTER_CODE": {"col_name": "COST_CENTER_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4937, "col_name": "COST_CENTER_CODE"}]}, "INTERNAL_ORDER_CODE": {"col_name": "INTERNAL_ORDER_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4958, "col_name": "INTERNAL_ORDER_CODE"}]}, "BILLING_DOC": {"col_name": "BILLING_DOC", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4927, "col_name": "BILLING_DOC"}]}, "SALES_DOCUMENT_NUMBER": {"col_name": "SALES_DOCUMENT_NUMBER", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4995, "col_name": "SALES_DOCUMENT_NUMBER"}]}, "SALES_DOCUMENT_ITEM": {"col_name": "SALES_DOCUMENT_ITEM", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4993, "col_name": "SALES_DOCUMENT_ITEM"}]}, "ASSET_SUBNUMBER": {"col_name": "ASSET_SUBNUMBER", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4925, "col_name": "ASSET_SUBNUMBER"}]}, "GL_ACCOUNT_NUMBER": {"col_name": "GL_ACCOUNT_NUMBER", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4952, "col_name": "GL_ACCOUNT_NUMBER"}]}, "CUSTOMER_CODE": {"col_name": "CUSTOMER_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4939, "col_name": "CUSTOMER_CODE"}]}, "VENDOR_CODE": {"col_name": "VENDOR_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 5003, "col_name": "VENDOR_CODE"}]}, "INVOICE_BASELINE_DATE": {"col_name": "INVOICE_BASELINE_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4960, "col_name": "INVOICE_BASELINE_DATE"}]}, "PAYMENT_TERM": {"col_name": "PAYMENT_TERM", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4977, "col_name": "PAYMENT_TERM"}]}, "DISCOUNT_AMOUNT": {"col_name": "DISCOUNT_AMOUNT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4940, "col_name": "DISCOUNT_AMOUNT"}]}, "PAYMENT_METHOD": {"col_name": "PAYMENT_METHOD", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4975, "col_name": "PAYMENT_METHOD"}]}, "PAYMENT_AMOUNT": {"col_name": "PAYMENT_AMOUNT", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4973, "col_name": "PAYMENT_AMOUNT"}]}, "NET_DUE_DATE": {"col_name": "NET_DUE_DATE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4969, "col_name": "NET_DUE_DATE"}]}, "MATERIAL_NUMBER": {"col_name": "MATERIAL_NUMBER", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4968, "col_name": "MATERIAL_NUMBER"}]}, "VAT_REG_NO": {"col_name": "VAT_REG_NO", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 5002, "col_name": "VAT_REG_NO"}]}, "PROFIT_CENTER_CODE": {"col_name": "PROFIT_CENTER_CODE", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4986, "col_name": "PROFIT_CENTER_CODE"}]}, "REFERENCE_KEY1": {"col_name": "REFERENCE_KEY1", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4991, "col_name": "REFERENCE_KEY1"}]}, "REFERENCE_KEY2": {"col_name": "REFERENCE_KEY2", "connection_info": [{"logical_entity_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_name": "FACT_ACCOUNTING_DOCUMENT_LINES", "table_id": 338, "col_id": 4992, "col_name": "REFERENCE_KEY2"}]}, "DISPUTE_IS_CONFIRMED": {"col_name": "DISPUTE_IS_CONFIRMED", "connection_info": [{"logical_entity_name": "FACT_DISPUTE_CASE_DETAILS", "table_name": "FACT_DISPUTE_CASE_DETAILS", "table_id": 354, "col_id": 5460, "col_name": "DISPUTE_IS_CONFIRMED"}]}, "DISPUTE_IS_VOIDED": {"col_name": "DISPUTE_IS_VOIDED", "connection_info": [{"logical_entity_name": "FACT_DISPUTE_CASE_DETAILS", "table_name": "FACT_DISPUTE_CASE_DETAILS", "table_id": 354, "col_id": 5461, "col_name": "DISPUTE_IS_VOIDED"}]}, "DISPUTE_CASE_TITLE": {"col_name": "DISPUTE_CASE_TITLE", "connection_info": [{"logical_entity_name": "FACT_DISPUTE_CASE_DETAILS", "table_name": "FACT_DISPUTE_CASE_DETAILS", "table_id": 354, "col_id": 5458, "col_name": "DISPUTE_CASE_TITLE"}]}, "COMPANY_NAME": {"col_name": "COMPANY_NAME", "connection_info": [{"logical_entity_name": "DIM_COMPANY", "table_name": "DIM_COMPANY", "table_id": 315, "col_id": 4567, "col_name": "COMPANY_NAME"}]}, "COMPANY_CITY": {"col_name": "COMPANY_CITY", "connection_info": [{"logical_entity_name": "DIM_COMPANY", "table_name": "DIM_COMPANY", "table_id": 315, "col_id": 4557, "col_name": "COMPANY_CITY"}]}}}}}