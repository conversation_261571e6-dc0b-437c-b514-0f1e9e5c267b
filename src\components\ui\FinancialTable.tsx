'use client';

import React, { useState, useMemo } from 'react';
import {
  ChevronUp,
  ChevronDown,
  Filter,
  Edit3,
  Check,
  X,
  Search,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

import { Input } from './Input';
import { Button } from './Button';

export interface FinancialTableColumn {
  key: string;
  title: string;
  type?: 'text' | 'number' | 'currency' | 'percentage' | 'date';
  align?: 'left' | 'center' | 'right';
  editable?: boolean;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  formatter?: (value: any) => string;
  validator?: (value: any) => boolean;
}

export interface FinancialTableRow {
  id: string | number;
  [key: string]: any;
}

export interface FinancialTableProps {
  columns: FinancialTableColumn[];
  data: FinancialTableRow[];
  onCellEdit?: (rowId: string | number, columnKey: string, newValue: any) => void;
  onRowSelect?: (selectedRows: (string | number)[]) => void;
  selectable?: boolean;
  striped?: boolean;
  bordered?: boolean;
  compact?: boolean;
  showSearch?: boolean;
  showFilters?: boolean;
  className?: string;
  emptyMessage?: string;
  loading?: boolean;
}

type SortDirection = 'asc' | 'desc' | null;

interface SortState {
  column: string | null;
  direction: SortDirection;
}

const FinancialTable: React.FC<FinancialTableProps> = ({
  columns,
  data,
  onCellEdit,
  onRowSelect,
  selectable = false,
  striped = true,
  bordered = true,
  compact = false,
  showSearch = true,
  showFilters = false,
  className = '',
  emptyMessage = 'No data available',
  loading = false,
}) => {
  const [sortState, setSortState] = useState<SortState>({ column: null, direction: null });
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [editingCell, setEditingCell] = useState<{ rowId: string | number; columnKey: string } | null>(null);
  const [editValue, setEditValue] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<string | number>>(new Set());

  // Format currency values
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Format percentage values
  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // Format number values
  const formatNumber = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // Format cell value based on column type
  const formatCellValue = (value: any, column: FinancialTableColumn): string => {
    if (value === null || value === undefined) return '';
    
    if (column.formatter) {
      return column.formatter(value);
    }

    switch (column.type) {
      case 'currency':
        return formatCurrency(Number(value));
      case 'percentage':
        return formatPercentage(Number(value));
      case 'number':
        return formatNumber(Number(value));
      case 'date':
        return new Date(value).toLocaleDateString();
      default:
        return String(value);
    }
  };

  // Handle sorting
  const handleSort = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey);
    if (!column?.sortable) return;

    setSortState(prev => {
      if (prev.column === columnKey) {
        if (prev.direction === 'asc') return { column: columnKey, direction: 'desc' };
        if (prev.direction === 'desc') return { column: null, direction: null };
      }
      return { column: columnKey, direction: 'asc' };
    });
  };

  // Handle filtering
  const handleFilterChange = (columnKey: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [columnKey]: value,
    }));
  };

  // Handle cell editing
  const startEdit = (rowId: string | number, columnKey: string, currentValue: any) => {
    setEditingCell({ rowId, columnKey });
    setEditValue(String(currentValue || ''));
  };

  const saveEdit = () => {
    if (editingCell && onCellEdit) {
      const column = columns.find(col => col.key === editingCell.columnKey);
      let processedValue = editValue;

      // Process value based on column type
      if (column?.type === 'number' || column?.type === 'currency' || column?.type === 'percentage') {
        processedValue = (parseFloat(editValue) || 0).toString();
      }

      // Validate if validator exists
      if (column?.validator && !column.validator(processedValue)) {
        alert('Invalid value entered');
        return;
      }

      onCellEdit(editingCell.rowId, editingCell.columnKey, processedValue);
    }
    setEditingCell(null);
    setEditValue('');
  };

  const cancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Handle row selection
  const handleRowSelect = (rowId: string | number) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(rowId)) {
      newSelectedRows.delete(rowId);
    } else {
      newSelectedRows.add(rowId);
    }
    setSelectedRows(newSelectedRows);
    onRowSelect?.(Array.from(newSelectedRows));
  };

  const handleSelectAll = () => {
    if (selectedRows.size === filteredAndSortedData.length) {
      setSelectedRows(new Set());
      onRowSelect?.([]);
    } else {
      const allIds = new Set(filteredAndSortedData.map(row => row.id));
      setSelectedRows(allIds);
      onRowSelect?.(Array.from(allIds));
    }
  };

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    let result = [...data];

    // Apply search filter
    if (searchTerm) {
      result = result.filter(row =>
        columns.some(column =>
          String(row[column.key] || '').toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([columnKey, filterValue]) => {
      if (filterValue) {
        result = result.filter(row =>
          String(row[columnKey] || '').toLowerCase().includes(filterValue.toLowerCase())
        );
      }
    });

    // Apply sorting
    if (sortState.column && sortState.direction) {
      result.sort((a, b) => {
        const aValue = a[sortState.column!];
        const bValue = b[sortState.column!];
        
        if (aValue === bValue) return 0;
        
        const comparison = aValue < bValue ? -1 : 1;
        return sortState.direction === 'asc' ? comparison : -comparison;
      });
    }

    return result;
  }, [data, searchTerm, filters, sortState, columns]);

  return (
    <div className={`financial-table-container ${className}`}>
      {/* Search and Controls */}
      {showSearch && (
        <div className="mb-4 flex items-center justify-between">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search table..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          {selectable && selectedRows.size > 0 && (
            <div className="text-sm text-gray-600">
              {selectedRows.size} row{selectedRows.size !== 1 ? 's' : ''} selected
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto bg-white-200 rounded-lg border border-lightgray-100 shadow-sm">
        <table className={`w-full ${compact ? 'table-compact' : ''}`}>
          <thead className="bg-gray-50 border-b border-lightgray-100">
            <tr>
              {selectable && (
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedRows.size === filteredAndSortedData.length && filteredAndSortedData.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-4 py-3 text-${column.align || 'left'} font-semibold text-gray-700 ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  } ${column.width ? `w-${column.width}` : ''}`}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <ChevronUp
                          className={`w-3 h-3 ${
                            sortState.column === column.key && sortState.direction === 'asc'
                              ? 'text-blue-600'
                              : 'text-gray-400'
                          }`}
                        />
                        <ChevronDown
                          className={`w-3 h-3 -mt-1 ${
                            sortState.column === column.key && sortState.direction === 'desc'
                              ? 'text-blue-600'
                              : 'text-gray-400'
                          }`}
                        />
                      </div>
                    )}
                    {column.filterable && showFilters && (
                      <Filter className="w-3 h-3 text-gray-400" />
                    )}
                  </div>
                </th>
              ))}
            </tr>
            {/* Filter Row */}
            {showFilters && (
              <tr className="bg-gray-25">
                {selectable && <th className="px-4 py-2"></th>}
                {columns.map((column) => (
                  <th key={`filter-${column.key}`} className="px-4 py-2">
                    {column.filterable && (
                      <Input
                        type="text"
                        placeholder={`Filter ${column.title}...`}
                        value={filters[column.key] || ''}
                        onChange={(e) => handleFilterChange(column.key, e.target.value)}
                        className="text-xs"
                      />
                    )}
                  </th>
                ))}
              </tr>
            )}
          </thead>
          <tbody className="divide-y divide-lightgray-100">
            {loading ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0)} className="px-4 py-8 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : filteredAndSortedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length + (selectable ? 1 : 0)} className="px-4 py-8 text-center text-gray-500">
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              filteredAndSortedData.map((row, rowIndex) => (
                <tr
                  key={row.id}
                  className={`
                    ${striped && rowIndex % 2 === 1 ? 'bg-gray-25' : 'bg-white-200'}
                    hover:bg-blue-25 transition-colors duration-150
                    ${selectedRows.has(row.id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
                  `}
                >
                  {selectable && (
                    <td className="px-4 py-3">
                      <input
                        type="checkbox"
                        checked={selectedRows.has(row.id)}
                        onChange={() => handleRowSelect(row.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                  )}
                  {columns.map((column) => (
                    <td
                      key={`${row.id}-${column.key}`}
                      className={`px-4 py-3 text-${column.align || 'left'} ${
                        compact ? 'py-2' : ''
                      } ${bordered ? 'border-r border-lightgray-100 last:border-r-0' : ''}`}
                    >
                      {editingCell?.rowId === row.id && editingCell?.columnKey === column.key ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type={column.type === 'number' || column.type === 'currency' || column.type === 'percentage' ? 'number' : 'text'}
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            className="text-xs"
                            autoFocus
                          />
                          <Button
                            onClick={saveEdit}
                            intent="primary"
                            className="p-1 text-xs"
                          >
                            <Check className="w-3 h-3" />
                          </Button>
                          <Button
                            onClick={cancelEdit}
                            intent="secondary"
                            className="p-1 text-xs"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between group">
                          <span className={`
                            ${column.type === 'currency' ? 'font-mono font-semibold' : ''}
                            ${column.type === 'number' ? 'font-mono' : ''}
                            ${column.type === 'percentage' ? 'font-medium' : ''}
                          `}>
                            {formatCellValue(row[column.key], column)}
                          </span>
                          {column.editable && onCellEdit && (
                            <Edit3
                              className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100 cursor-pointer ml-2"
                              onClick={() => startEdit(row.id, column.key, row[column.key])}
                            />
                          )}
                          {column.type === 'currency' && typeof row[column.key] === 'number' && (
                            <span className="ml-1">
                              {row[column.key] > 0 ? (
                                <TrendingUp className="w-3 h-3 text-green-500" />
                              ) : row[column.key] < 0 ? (
                                <TrendingDown className="w-3 h-3 text-red-500" />
                              ) : null}
                            </span>
                          )}
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Summary Row */}
      {filteredAndSortedData.length > 0 && (
        <div className="mt-2 text-sm text-gray-600 flex justify-between items-center">
          <span>
            Showing {filteredAndSortedData.length} of {data.length} rows
          </span>
          {columns.some(col => col.type === 'currency') && (
            <div className="flex space-x-4">
              {columns
                .filter(col => col.type === 'currency')
                .map(col => {
                  const total = filteredAndSortedData.reduce((sum, row) => sum + (Number(row[col.key]) || 0), 0);
                  return (
                    <span key={col.key} className="font-semibold">
                      {col.title} Total: {formatCurrency(total)}
                    </span>
                  );
                })}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FinancialTable;
