/* eslint-disable no-param-reassign */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable import/no-named-as-default */
/* eslint-disable react/no-array-index-key */

'use client';

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/dataCollaboration/dataCollaboration.json';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Avatar from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { WarningDialog } from '@/components/ui/Dialog';
import { Dropdown } from '@/components/ui/Dropdown';
import { Searchbar } from '@/components/ui/Searchbar';
import { createMenuName } from '@/constants/menuSvg';
import { setIsWarningAlertOpen, setWarningAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

import { Link } from '../../ui/Link';

const DataCollaboration: React.FC = () => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();

  // Selectors
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Constants
  const data: any = eData;

  // States
  const [searchItem, setSearchItem] = useState('');
  const [RowID, setRowID] = useState(null);
  const [filteredData, setFilteredData] = useState(data);

  // Methods
  const search = (query: any) => {
    if (!query.trim()) {
      return [];
    }
    const results = data.filter((item: any) =>
      item.name.toLowerCase().includes(query.toLowerCase()),
    );

    return results;
  };
  const handleSearch = (value: string) => {
    setSearchItem(value);
    if (value) {
      const filteredValues = search(value);
      setFilteredData(filteredValues);
    } else {
      const filteredValues = data;
      setFilteredData(filteredValues);
    }
  };
  const goToCollaborate = (Mode: any) => {
    const parameter: any = {
      mode: Mode,
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/dataCollaboration/addNewCollab?${queryString}`);
  };
  const goToDataLog = (view: any) => {
    const parameter: any = {
      view,
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/dataCollaboration/dataLog?${queryString}`);
  };
  const deleteItem = () => {
    dispatch(setIsWarningAlertOpen(false));
  };
  const menuData: any = [
    {
      option: 'View Analytics Dashboard',
      clickFn: (key: any) => {
        router.push('/dataCollaboration/viewCollab');
        setRowID(key);
      },
    },
    {
      option: 'View Data Log',
      clickFn: () => {
        goToDataLog('normal');
      },
    },
    {
      option: 'View Suite Details',
      clickFn: (key: any) => {
        setRowID(key);
        console.log(key, RowID);
        goToCollaborate('View');
      },
    },
    {
      option: 'Edit Suite',
      clickFn: () => {
        goToCollaborate('Edit');
      },
    },
    {
      option: 'Delete Suite',
      clickFn: () => {
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: 'Delete Entry 01',
            message:
              'Are you sure? Do you want to delete the Entity 01 Please note this action cannot be reverted ',
            actionbuttonText: 'Yes, Delete ',
            cancelButtonText: ' No, Discard',
          }),
        );
      },
    },
  ];

  const markFav = (cardId: any, isFav: boolean) => {
    const newCards = data?.map((card: any) => {
      if (card.id === cardId) {
        card.isFavourite = !card.isFavourite;
      }
      return card;
    });
    setFilteredData(newCards);

    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Collaboration Suite
        </span>
        <div className="create-data-collaboration">
          <Link
            content={createMenuName('Collaborate')}
            onClick={() => goToCollaborate('Create')}
          />
        </div>
      </div>
      <div className="flex h-[88vh] w-full flex-col border-lightgray-100 text-blueGray-300">
        <div className="flex h-full w-full flex-col space-y-6">
          <div className="w-[40vw]">
            {' '}
            <Searchbar
              value={searchItem}
              placeholder="Search"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          <div className="flex h-full max-h-[calc(100vh-100px)] w-full flex-wrap overflow-auto">
            {filteredData.map((cardData: any, index: any) => (
              <div
                className="data-collaboration-card mb-4 mr-4 h-[354px] w-[305px]  rounded bg-white-200 p-4"
                key={index}
              >
                <div className="flex items-center justify-between">
                  {' '}
                  <Avatar name={cardData.name} color={cardData.avatarColor} />
                  <div className="relative right-14 z-10 rounded-full border-2 border-blue-500 bg-white-100">
                    <Avatar name={cardData.name} color={cardData.avatarColor} />
                  </div>
                  <div className="flex flex-row items-center space-x-2">
                    {' '}
                    <Badge
                      intent="success"
                      content="ACTIVE"
                      className="mt-1 rounded px-1 py-2 text-sm"
                    />
                    <div className="view-data-collaboration mt-[4px] flex flex-row items-center space-x-1">
                      <button
                        type="button"
                        className="h-[24px] w-[24px] cursor-pointer"
                        onClick={() => {
                          markFav(cardData.id, !cardData?.isFavourite);
                        }}
                      >
                        {cardData?.isFavourite ? (
                          <img
                            src="/assets/images/star-active.svg"
                            alt="clear"
                          />
                        ) : (
                          <img src="/assets/images/star.svg" alt="clear" />
                        )}
                      </button>
                      <Dropdown
                        menuTitle={`<img src='/assets/images/menu.svg'>`}
                        data={menuData}
                        showMenu={isTourOpen && index === 0 ? true : undefined}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex flex-col space-y-1 border-b-[0.5px] border-lightgray-100  bg-white-200 pb-3 pt-2">
                  <span className="font-sans text-base font-semibold text-blueGray-300">
                    {cardData.header}
                  </span>
                  <span className="font-sans text-sm font-normal text-gray-500">
                    {cardData.email}
                  </span>
                  <span className="font-sans text-xs font-normal text-gray-400">
                    {cardData.createdOn}
                  </span>
                </div>
                <div className="flex h-full w-full flex-col space-y-2 pt-3">
                  <div className=" flex flex-row items-center justify-between">
                    <span className="font-sans text-xs font-normal text-gray-500">
                      Dashboards
                    </span>
                    <div className=" flex flex-row items-center space-x-2 ">
                      <Link
                        className="px-1 text-xs text-blue-200"
                        href={cardData.view}
                        content="View All"
                      />
                      <img src="/assets/images/verticalLine.svg" alt="line" />
                      <Link
                        className="px-1 text-xs text-blue-200"
                        href={cardData.create}
                        content="Create New"
                      />
                    </div>
                  </div>

                  <div className="flex flex-col space-y-1">
                    {cardData.dashboard.map(
                      (dashboardName: any, rowIndex: any) => (
                        <div
                          key={rowIndex}
                          className="flex h-[44px] w-full cursor-pointer items-center rounded-sm bg-slate-100 px-2 font-sans text-sm font-semibold  text-blue-200"
                        >
                          {dashboardName}
                        </div>
                      ),
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <WarningDialog onConfirm={deleteItem} />
        </div>
      </div>
    </div>
  );
};

export default DataCollaboration;
