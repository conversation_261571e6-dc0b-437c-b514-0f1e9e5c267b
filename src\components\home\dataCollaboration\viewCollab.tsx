'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

import { Dropdown } from '@/components/ui/Dropdown';

// import SelectData from './addLogicalEntitiesTabs/selectData';

const ViewCollab: React.FC = () => {
  // Essentials
  const router = useRouter();

  // Constants
  const menuData: any = [
    {
      option: 'Mark as Favourite',
    },
    {
      option: 'Edit',
    },
    {
      option: 'Delete',
    },
  ];

  // Methods
  const goBack = () => {
    router.push('/dataCollaboration');
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Data Collaboration Insights
        </span>
      </div>
      <div className="flex h-[84vh] w-[260px] flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <div className="relative flex w-full flex-col overflow-auto">
          <div className="pb-4 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
            Dashboards
          </div>
          <div
            className="absolute right-0 top-0 flex items-center pt-2"
            dangerouslySetInnerHTML={{
              __html: `<img src='/assets/images/search.svg'>`,
            }}
          />
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Supply Chain Insights
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Payable Insights
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Receivable Insights
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Invoice Details
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Purchase Details
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
        <div className="relative flex w-full justify-between">
          <div className="pb-4 font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50">
            Payment Analysis
          </div>
          <div className="pt-1">
            <Dropdown
              menuTitle={`<img src='/assets/images/menu.svg'>`}
              data={menuData}
              // refId={rowData.id}
              // scrollPosition={scrollPosition}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewCollab;
