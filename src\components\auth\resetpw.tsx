'use client';

/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */

import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React, { useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setEmailValue } from '@/slices/resetPasswordSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import PageLoader from '../loader';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Link } from '../ui/Link';

const ResetPassword: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // State
  const isLoading = false;

  // Methods
  const forgotPassword = async (email: string) => {
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.forgotPassword.url;
    const params = {
      username: email,
    };
    apiService
      .unauthorizedPostRequest(url, params)
      .then(() => {
        dispatch(setEmailValue(email));
        router.push('/create-new-password');
      })
      .catch(() => {
        // setIsAlertOpen(true);
      });
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      {!isLoading && (
        <div className="flex min-h-[30vh] w-[50vw] flex-col lg:w-[30vw]">
          <div className=" mb-10 flex flex-col space-y-1">
            <span className="font-sans text-5xl font-normal leading-[70.81px] text-gray-800">
              Forgot Password?
            </span>
            <span className="font-sans text-base font-normal leading-5 text-gray-900">
              No worries, we&rsquo;ll send you reset instructions.
            </span>
          </div>
          <Formik
            initialValues={{ email: '', password: '', confirmPassword: '' }}
            validate={(values) => {
              const errors: any = {};
              if (!values.email) {
                errors.email = 'Required.';
              } else if (
                !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
              ) {
                errors.email = 'Invalid email address.';
              }
              return errors;
            }}
            onSubmit={(values, { setSubmitting }) => {
              setSubmitting(true);
              forgotPassword(values.email);
              setSubmitting(false);
            }}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              isSubmitting,
              handleSubmit,
            }) => (
              <form className="flex flex-col" onSubmit={handleSubmit}>
                <div className="mb-4 w-full">
                  <Input
                    label="Email ID"
                    name="email"
                    className="w-full"
                    placeholder="<EMAIL>"
                    type="email"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.email}
                    intent={
                      errors.email && touched.email ? 'hasError' : 'enabled'
                    }
                    error={errors.email && touched.email && errors.email}
                  />
                </div>
                <Button
                  intent="primary"
                  disabled={
                    isSubmitting ||
                    Object.keys(errors).length > 0 ||
                    !values.email
                  }
                >
                  Reset Password
                </Button>
              </form>
            )}
          </Formik>
          <div className="flex w-full items-center justify-center py-6">
            <span className="font-sans text-base font-normal leading-5 text-gray-800">
              Know your password?
              <Link className="px-1" href="login" content="Back to log in" />
            </span>
          </div>
        </div>
      )}

      {isLoading && (
        <PageLoader
          isLoading={isLoading}
          message="Creating Account and setting up AstRai"
        />
      )}
    </div>
  );
};
export default ResetPassword;
