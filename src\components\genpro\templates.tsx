'use client';

import React from 'react';
import { 
  FileSpreadsheet, 
  Download, 
  FileText, 
  Info,
  Calendar,
  Database
} from 'lucide-react';

import { Button } from '@/components/ui/Button';

const GenProTemplates = () => {
  const templates = [
    {
      name: 'Base Data (POs) Template',
      description: 'Monthly Excel template for purchase orders received by BSM',
      type: 'Excel (.xlsx)',
      size: '45 KB',
      version: '2.1',
      lastUpdated: '2024-01-15',
      required: true,
      fields: ['PO Number', 'SMC', 'Vessel Name', 'Owner Name', 'PO Category', 'Item Price', 'GENPRO - BSM/BOTH'],
      icon: FileSpreadsheet,
      color: 'bg-green-500'
    },
    {
      name: 'BF Overview Values Template',
      description: 'Template for monthly brokerage fee reporting values',
      type: 'Excel (.xlsx)',
      size: '32 KB',
      version: '1.8',
      lastUpdated: '2024-01-12',
      required: true,
      fields: ['Line Item', 'Add/Less Indicator', 'Amount', 'Description', 'Month', 'Year'],
      icon: FileText,
      color: 'bg-blue-500'
    },
    {
      name: 'GenPro Expenses Template',
      description: 'Template for GenPro expense data (YTD to MTD conversion)',
      type: 'Excel (.xlsx)',
      size: '28 KB',
      version: '1.5',
      lastUpdated: '2024-01-10',
      required: true,
      fields: ['Expense Category', 'YTD Amount', 'MTD Amount', 'Description', 'Cost Center'],
      icon: Database,
      color: 'bg-purple-500'
    },
    {
      name: 'Vessel Entity Mapping Template',
      description: 'Reference template for vessel-to-entity code mappings',
      type: 'Excel (.xlsx)',
      size: '15 KB',
      version: '3.0',
      lastUpdated: '2024-01-08',
      required: false,
      fields: ['Entity Code', 'Vessel Name', 'Description', 'Status', 'Owner Name'],
      icon: FileSpreadsheet,
      color: 'bg-orange-500'
    },
    {
      name: 'Import Transaction Template',
      description: 'Template for final system import with Genpro as default entity',
      type: 'Excel (.xlsx)',
      size: '22 KB',
      version: '1.2',
      lastUpdated: '2024-01-05',
      required: false,
      fields: ['Date', 'Entity Code', 'Narration', 'Amount', 'Final Vessel'],
      icon: FileText,
      color: 'bg-indigo-500'
    }
  ];

  const handleDownload = (templateName: string) => {
    alert(`Downloading ${templateName}...`);
  };

  const handlePreview = (templateName: string) => {
    alert(`Opening preview for ${templateName}...`);
  };

  return (
    <div className="p-8">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          <FileSpreadsheet className="w-6 h-6 mr-2" />
          Data Templates
        </h1>
        <p className="text-gray-600">
          Download and manage data import templates for GenPro workflow processing
        </p>
      </div>

      {/* Info Banner */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <Info className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-semibold text-blue-800">Template Guidelines</h3>
            <p className="text-sm text-blue-700 mt-1">
              All templates follow the naming convention: genpro/&lt;year&gt;/&lt;month&gt;/&lt;file_type&gt;.xlsx. 
              Required templates must be uploaded for workflow processing. Ensure data integrity and follow field specifications.
            </p>
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {templates.map((template, index) => {
          const IconComponent = template.icon;
          return (
            <div
              key={index}
              className="bg-white-200 rounded-lg border border-lightgray-100 p-6 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-3">
                  <div className={`${template.color} p-3 rounded-lg`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">
                      {template.name}
                      {template.required && (
                        <span className="text-red-500 text-sm ml-1">*</span>
                      )}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {template.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>{template.type}</span>
                      <span>{template.size}</span>
                      <span>v{template.version}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Template Details */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Required Fields:</h4>
                <div className="flex flex-wrap gap-1">
                  {template.fields.slice(0, 4).map((field, fieldIndex) => (
                    <span
                      key={fieldIndex}
                      className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                    >
                      {field}
                    </span>
                  ))}
                  {template.fields.length > 4 && (
                    <span className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                      +{template.fields.length - 4} more
                    </span>
                  )}
                </div>
              </div>

              {/* Last Updated */}
              <div className="flex items-center text-xs text-gray-500 mb-4">
                <Calendar className="w-3 h-3 mr-1" />
                Last updated: {template.lastUpdated}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  intent="primary"
                  className="flex-1"
                  onClick={() => handleDownload(template.name)}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
                <Button
                  intent="secondary"
                  onClick={() => handlePreview(template.name)}
                >
                  Preview
                </Button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Usage Instructions */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Usage Instructions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">File Naming Convention</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Use format: genpro/&lt;year&gt;/&lt;month&gt;/&lt;file_type&gt;.xlsx</li>
              <li>• Example: genpro/2025/01/base_data.xlsx</li>
              <li>• Ensure consistent naming for automated processing</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-700 mb-2">Data Requirements</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• All required fields must be populated</li>
              <li>• Follow data type specifications exactly</li>
              <li>• Validate entity codes against mapping table</li>
              <li>• Ensure date formats are consistent (YYYY-MM-DD)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenProTemplates;
