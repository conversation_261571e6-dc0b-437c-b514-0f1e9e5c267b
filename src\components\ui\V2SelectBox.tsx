"use client";

/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-unstable-nested-components */
import DeleteIcon from "@mui/icons-material/DeleteOutlined";
import EditIcon from "@mui/icons-material/Edit";
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import * as React from "react";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

interface V2SelectboxProps {
  options?: any[];
  placeholder?: any;
  value?: any;
  onChange?: (event: any) => void;
  onDelete?: (item: string) => void;
  onUpdate?: (item: string) => void;
  disabled?: boolean;
  noBorder?: boolean;
  noSelect?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  open?: boolean;
  name?: string;
  boxStyle?: string;
  required?: boolean;
}

const V2Selectbox: React.FC<V2SelectboxProps> = ({
  options,
  placeholder,
  value,
  onChange,
  onDelete,
  onUpdate,
  disabled = false,
  noBorder = false,
  noSelect = false,
  onOpen,
  boxStyle,
  required = false,
  name = "selectbox",
}) => {
  const handleDelete = (item: string) => {
    if (onDelete) onDelete(item);
  };

  const handleUpdate = (item: string) => {
    if (onUpdate) onUpdate(item);
  };

  return (
    <Box sx={{ minWidth: 0, width: "100%" }}>
      <FormControl className="select-core w-full">
        <Select
          name={name}
          IconComponent={(props) => (
            <div {...props} style={{ ...props.style, paddingRight: 8 }}>
              <ArrowDropDownIcon 
                style={{ 
                  fontSize: "20px", 
                  color: "#6B7280", 
                  cursor: "pointer" 
                }} 
              />
            </div>
          )}
          variant={noBorder ? "standard" : "outlined"}
          id="demo-simple-select"
          className={boxStyle}
          value={value}
          onChange={onChange}
          disabled={disabled}
          onOpen={onOpen}
          required={required}
          displayEmpty
          MenuProps={{
            MenuListProps: { disablePadding: true },
            PaperProps: {
              style: {
                maxHeight: 300,
                width: "auto",
              },
            },
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "left",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "left",
            },
          }}
          inputProps={{ "aria-label": "Without label" }}
          renderValue={(selected: string) => {
            if (!selected || noSelect) {
              return (
                <div className="text-xs font-normal leading-4 text-gray-400">
                  {placeholder}
                </div>
              );
            }
            return selected;
          }}
        >
          {options?.map((item: string, index: number) => (
            <MenuItem
              key={`${item}_${index}`}
              sx={{
                color: "#7B7C7D",
                "&:hover": {
                  backgroundColor: "rgba(47, 128, 237, 0.1)",
                  color: "#2F80ED",
                },
                "&:focus": {
                  backgroundColor: "rgba(47, 128, 237, 0.1)",
                  color: "#2F80ED",
                },
                fontSize: 12,
                fontWeight: 400,
                lineHeight: "1rem",
                padding: "8px 16px",
                width: "100%",
                position: "relative",
              }}
              divider
              value={item}
            >
              <div className="flex w-full flex-1 items-center justify-between">
                <span className="flex-1">{item}</span>
                {onDelete && onUpdate && (
                  <div className="ml-4 flex items-center gap-2">
                    <button
                      onClick={() => handleUpdate(item)}
                      className="flex h-4 w-4 items-center justify-center rounded-full"
                      title="Update"
                    >
                      <EditIcon style={{ fontSize: 20 }} />
                    </button>
                    <button
                      onClick={() => handleDelete(item)}
                      className="flex h-6 w-6 items-center justify-center rounded-full"
                      title="Delete"
                    >
                      <DeleteIcon style={{ fontSize: 20 }} />
                    </button>
                  </div>
                )}
              </div>
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};

export default V2Selectbox;
