/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

'use client';

import dynamic from 'next/dynamic';
import React, { useEffect, useState } from 'react';

import ListBoxTable from '../ui/ListBoxTable';

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });
const ChatGraph: React.FC<{ data?: any }> = (data) => {
  const [selectedGraphType, setSelectedGraphType] = useState<any>('bar');
  const [typeList] = useState<any>([
    { name: 'bar' },
    { name: 'line' },
    { name: 'area' },
  ]);
  const renderGraph = (graphType: any) => {
    return (
      <Chart
        height="auto"
        key={graphType}
        options={data.data.options}
        series={data.data.series}
        type={graphType}
      />
    );
  };
  useEffect(() => {
    setSelectedGraphType(selectedGraphType);
  }, [selectedGraphType]);

  return (
    <div className="flex w-full flex-col space-y-2 rounded bg-white-200 p-2">
      <div className="flex w-full flex-col space-y-1">
        <span className="font-sans text-sm font-semibold text-gray-500">
          Select Graph Type
        </span>
        <div className="mt-3 w-[15vw] rounded border-[1px] border-lightgray-100 py-1">
          <ListBoxTable
            isInTable
            hasValue
            items={typeList}
            placeholder="Select Type"
            selectedValue={selectedGraphType}
            onSelectionChange={(selectedValue) => {
              setSelectedGraphType(selectedValue.name);
            }}
            name="type"
          />
        </div>
      </div>
      {data && data.data && (
        <div className="h-full w-full pb-2 ">
          {renderGraph(selectedGraphType)}
        </div>
      )}
    </div>
  );
};

export default ChatGraph;
