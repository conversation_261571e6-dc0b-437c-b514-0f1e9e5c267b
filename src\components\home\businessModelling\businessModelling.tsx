/* eslint-disable tailwindcss/no-custom-classname */

'use client';

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useRouter } from 'next/navigation';
import React from 'react';

import BusinessModellingTable from '@/components/tables/businessModelling/businessModellingTable';
import { createMenuName } from '@/constants/menuSvg';

import { Link } from '../../ui/Link';

const BusinessModelling: React.FC = () => {
  // essentials
  const router = useRouter();

  // Methods
  const goToAddBusinessModel = () => {
    const parameter: any = {
      mode: 'Create',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/businessModelling/addNewBusinessModel?${queryString}`);
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Business Modelling Studio
        </span>
        <div className="create-business-modelling">
          <Link
            content={createMenuName('Create Business Schema')}
            onClick={goToAddBusinessModel}
          />
        </div>
      </div>
      <div className="flex h-[88vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <BusinessModellingTable />
      </div>
    </div>
  );
};

export default BusinessModelling;
