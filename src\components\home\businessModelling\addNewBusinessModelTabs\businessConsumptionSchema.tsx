import React from 'react';

import CustomAttribute from '@/components/tables/businessModelling/businessConsumption/customAttributesTable';
import SelectExistingAttribute from '@/components/tables/businessModelling/businessConsumption/existingAttributesTable';

const BusinessConsumptionSchema: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  return (
    <div className="flex h-[72vh] w-full flex-col space-y-4 px-6 pt-2">
      <div className="h-[49%] w-full rounded bg-gray-300 px-4 py-2">
        <SelectExistingAttribute mode={props.mode} />
      </div>{' '}
      <div className="h-[49%] w-full rounded bg-gray-300 px-4 py-2">
        <CustomAttribute mode={props.mode} />
      </div>
    </div>
  );
};

export default BusinessConsumptionSchema;
