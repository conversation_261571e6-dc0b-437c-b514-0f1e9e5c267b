/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-alert */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable jsx-a11y/anchor-is-valid */

'use client';

import 'survey-core/defaultV2.min.css';

import { surveyJson } from 'public/testdata/configurationCockpit/configurationCockpit.js';
import { useEffect, useState } from 'react';
import { Model } from 'survey-core';
import { Survey } from 'survey-react-ui';

import { surveyCompletedPage } from '@/constants/menuSvg';
import { PlainLightPanelless } from '@/theme/plain-light';

const ConfigurationCockpitForm: React.FC = () => {
  // Create a new instance of the Model class with survey data from surveyJson
  const survey = new Model(surveyJson);
  const [readyFlag, setReadyFlag] = useState(false);
  // Applying theme
  survey.applyTheme(PlainLightPanelless);
  survey.completeText = 'Submit';
  // Adding Styling for Complete Page
  survey.completedHtml = surveyCompletedPage;
  console.log('survey', survey);
  // Method
  // survey.onUploadFiles.add((_, options: any) => {
  //   const formData = new FormData();
  //   options.files.forEach((file: any) => {
  //     formData.append(file.name, file);
  //   });

  //   fetch('https://api.surveyjs.io/private/Surveys/uploadTempFiles', {
  //     method: 'POST',
  //     body: formData,
  //   })
  //     .then((response) => response.json())
  //     .then((data) => {
  //       options.callback(
  //         options.files.map((file: any) => {
  //           return {
  //             file,
  //             content: `https://api.surveyjs.io/private/Surveys/getTempFile?name=${
  //               data[file.name]
  //             }`,
  //           };
  //         }),
  //       );
  //     })
  //     .catch((error) => {
  //       console.error('Error: ', error);
  //       options.callback([], ['An error occurred during file upload.']);
  //     });
  // });

  // async function deleteFile(fileURL: any, options: any) {
  //   try {
  //     const name = fileURL.split('=')[1];
  //     const apiUrl = `https://api.surveyjs.io/private/Surveys/deleteTempFile?name=${name}`;
  //     const response = await fetch(apiUrl, {
  //       method: 'DELETE',
  //     });

  //     if (response.status === 200) {
  //       console.log(`File ${name} was deleted successfully`);
  //       options.callback('success');
  //     } else {
  //       console.error(`Failed to delete file: ${name}`);
  //       options.callback('error');
  //     }
  //   } catch (error) {
  //     console.error('Error while deleting file: ', error);
  //     options.callback('error');
  //   }
  // }

  // survey.onClearFiles.add(async (_, options: any) => {
  //   if (!options.value || options.value.length === 0)
  //     return options.callback('success');
  //   if (!options.fileName && !!options.value) {
  //     for (const item of options.value) {
  //       await deleteFile(item.content, options);
  //     }
  //   } else {
  //     const fileToRemove = options.value.find(
  //       (item: any) => item.name === options.fileName,
  //     );
  //     if (fileToRemove) {
  //       await deleteFile(fileToRemove.content, options);
  //     } else {
  //       console.error(`File with name ${options.fileName} is not found`);
  //     }
  //   }
  // });

  survey.onComplete.add((sender) => {
    console.log(JSON.stringify(sender.data, null, 3));
  });

  useEffect(() => {
    setTimeout(() => {
      setReadyFlag(true);
    }, 400);
  });

  return (
    <div>
      {readyFlag && (
        <div className="my-[20px] flex h-[calc(100vh-240px)] overflow-auto lg:justify-center xl:px-3">
          <Survey className="survey-form" model={survey} />
        </div>
      )}
    </div>
  );
};

export default ConfigurationCockpitForm;
