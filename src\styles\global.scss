@tailwind base;

a {
  @apply text-blue-700;
}

a:hover {
  @apply border-b-2 border-blue-700;
}

@tailwind components;

@tailwind utilities;

.content p {
  @apply my-6;
}

.content ul {
  @apply my-6;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

* {
  scrollbar-width: thin;
}

*::-webkit-scrollbar-track {
  background-color: transparent !important;
}

*::-webkit-scrollbar-thumb {
  background-color: #D0D0D0;
  border-radius: 8px;
  border: 1px solid #D0D0D0;
}

.page-loader {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .loader {
    border: 8px solid #2F80ED;
    border-top: 4px solid transparent;
    border-radius: 50%;
    width: 90px;
    height: 90px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}


.datePicker {
  border: 2px solid green
}

.dateRange {
  .react-daterange-picker__wrapper {
    height: 48px;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 4px;
    border: 1px solid #D0D0D0;
    background-color: transparent;
    font-family: 'OpenSans';
    font-weight: 600;
    font-size: 14px;
    color: #474D67;
    padding-left: 12px;
    padding-right: 16px;
    // padding: 16px 16px 16px 12px;

  }

  .react-daterange-picker__clear-button {
    display: none;
  }

  .react-daterange-picker__range-divider {
    margin-left: 3px;
    margin-right: 3px;
  }

  .react-daterange-picker__calendar-button {
    height: 24px;
    width: 24px;
  }

  .react-daterange-picker__calendar .react-calendar {
    border-radius: 4px;
    border: 1px solid #D0D0D0;
    font-family: 'OpenSans';
    font-weight: 600;
    font-size: 14px;
    color: #474D67;
    width: 313px;
  }
}

.logs {
  p {
    margin-top: 0px;
    margin-bottom: 0px;
  }
}

.truncateData {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.custom-tooltip {
  max-width: 60vw;
  overflow: hidden;
  background-color: #2F80ED !important;
  color: #ffffff;
  z-index: 5000;
}

.pp {
  border: 2px solid red
}

.survey-form {
  .sd-page {
    height: 100% !important;
    padding: 0px;

  }

  .sd-question__header {
    padding-left: 22px;
    padding-right: 22px;
  }

  .sd-question__content {
    padding-left: 22px;
    padding-right: 22px;
  }

  .sd-body.sd-body--static {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .sd-body__navigation.sd-action-bar {
    padding: 0px 0px 15px 0px;
    justify-content: end !important;

  }

  .sd-body__navigation .sd-btn {
    padding: 12px 26px 12px 26px;
  }
}

.sd-root--compact .sd-body.sd-body--responsive .sd-body__navigation,
.sd-root--compact .sd-body.sd-body--static .sd-body__navigation {
  padding-top: 15px !important;
  padding-left: 0px !important;
  padding-right: 15px !important;
}

.sd-element__title .sd-element__num {
  font-size: 16px !important;

}

.truncateTablename {
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.cron-container-select .ant-select-selector {
  border: none !important;
}

.shadowAllCorners {
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

// .insight-card {
//   box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
// }

.sd-page .sd-page__title {
  border-bottom: 1px solid #D6D6DC;
  color: #69696b !important;
  width: 100%;
  padding: 6px;
  font-size: 14px !important;
  height: 40px;
}

.sd-progress {
  background: transparent !important;
  background-color: #FFFFFF !important;
}

.sd-progress__bar {
  background-color: #FFFFFF !important;
}

@keyframes moveRight {
  0% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(8px);
  }

  100% {
    transform: translateX(0);
  }
}

.loading-span {
  opacity: 0;
  animation: moveRight 1s infinite;
  transition: opacity 0.5s ease-in-out;
}

.loading-span.show {
  opacity: 1;
}

.text-pretty {
  text-wrap: pretty;
}

.chat-table {
  .dataframe {
    border-collapse: collapse;
    width: 100%;
  }

  .dataframe th,
  .dataframe td {
    border: 1px solid #dddddd;
    padding: 8px;
    text-align: left;
    font-size: 12px;
  }

  .dataframe th {
    background-color: #f2f2f2;
  }

  .dataframe tbody tr:nth-child(even) {
    background-color: #f9f9f9;
  }
}

.file-drop-area {
  border: 2px dashed #2F80ED;
  padding: 20px;
  text-align: center;
  cursor: pointer;
}

.preview-class-tooltip {
  // border-radius: 50%;
  // border:1px solid #D0D0D0;
  // overflow: auto;
  background-color: white !important;
  color: #4F5152 !important;
  z-index: 5000;
  box-shadow: 0px 4px 8px rgba(192, 192, 192, 0.1);
}

.info-tooltip {
  z-index: 1000;
  max-width: 30vw;
  background-color: #2F80ED !important;
  color: #ffffff;
}

.has-table-border {

  th:first-child,
  td:first-child {
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: white;

  }
}

.svYMe,.bxWxwx {
  svg {
    display: none !important;
  }
  .eVrcSZ,.eo0bXZ {
    width: 20px;
  }
}
