[{"id": "R009", "selected": false, "components": [{"value": "R009", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "success", "content": "COMPLETED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}, {"id": "R008", "selected": false, "components": [{"value": "R008", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "error", "content": "FAILED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}, {"id": "R004", "selected": false, "components": [{"value": "R004", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "success", "content": "COMPLETED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}, {"id": "R005", "selected": false, "components": [{"value": "R005", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "success", "content": "COMPLETED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}, {"id": "R002", "selected": false, "components": [{"value": "R003", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "error", "content": "FAILED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}, {"id": "R006", "selected": false, "components": [{"value": "R006", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "2023-08-10T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "2023-08-12T18:25:43.511Z", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "3hr 18min", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "198 MB", "userAvatarLink": null, "disabled": false, "type": "text"}, {"value": "", "badges": [{"intent": "success", "content": "COMPLETED", "id": 1}], "disabled": false, "userAvatarLink": null, "type": "badges"}, {"value": "View Log", "disabled": false, "userAvatarLink": null, "type": "modal", "route": "logModal"}]}]