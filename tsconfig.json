{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "removeComments": true, "preserveConstEnums": true, "strict": true, "alwaysStrict": true, "strictNullChecks": true, "noUncheckedIndexedAccess": false, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "allowUnreachableCode": false, "noFallthroughCasesInSwitch": true, "target": "es2017", "outDir": "out", "declaration": true, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "checkJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "noEmit": true, "isolatedModules": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/public/*": ["./public/*"]}, "plugins": [{"name": "next"}]}, "exclude": ["./out/**/*", "./node_modules/**/*", "cypress/**/*.ts"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".storybook/*.ts", ".next/types/**/*.ts", "src/libs/Env.mjs"]}