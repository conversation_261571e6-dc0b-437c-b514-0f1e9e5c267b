/* eslint-disable react/no-danger */
/* eslint-disable tailwindcss/classnames-order */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

/* eslint-disable react/no-unescaped-entities */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/anchor-is-valid */

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/landing/cardsAdmin.json';
import fData from 'public/testdata/landing/favourites.json';
import notificationData from 'public/testdata/landing/notification.json';
import overAllData from 'public/testdata/landing/overAll.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';

import { Link } from '@/components/ui/Link';
import { Modal } from '@/components/ui/Modal';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import {
  setIsLoading,
  setIsTourOpen,
  setNotificationPanelVisibility,
} from '@/slices/appSlice';
import { setNotification } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';
import {
  formatNotificationArray,
  resetAllFilters,
} from '@/utils/utilityHelper';

import ConfigurationModal from '../modal/configurationModal';
import InsightCards from './insightCards';
import NotificationBox from './notificationPanel';

const HomeComp: React.FC = () => {
  // Essentials
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const cardData: any = eData;
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const notification = useSelector(
    (state: RootState) => state.metadata.notification,
  );
  const notificationPanelVisible = useSelector(
    (state: RootState) => state.appConfig.notificationPanelVisible,
  );
  const [modalOpen, setModalOpen] = useState(false);
  const [userName, setUserName] = useState('');

  // Constants
  const allData: any = overAllData;
  const favData: any = fData;

  const closePopupModal = () => {
    setModalOpen(false);
  };

  const navigateToCardRoute = (name: string, route: any) => {
    if (name.includes('Create')) {
      const parameter: any = {
        mode: 'Create',
      };
      const queryString = new URLSearchParams(parameter).toString();
      router.push(`${route}?${queryString}`);
    } else {
      if (name === 'Data Reconcialiation') {
        localService.setItem('selectedDataQualityTab', String(0));
      } else if (name === 'Data Validation') {
        localService.setItem('selectedDataQualityTab', String(3));
      }
      router.push(route);
    }
  };

  const togglePinNotification = () => {
    dispatch(setNotificationPanelVisibility(!notificationPanelVisible));
  };

  useEffect(() => {
    const user =
      localStorage.getItem('name') || localStorage.getItem('userName') || '';
    setUserName(user);
  }, []);

  useEffect(() => {
    const fetchNotification = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsmNotification +
          ApiUtilities.apiPath.getNotifications.url
        }`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const notificationArray = formatNotificationArray(res.data);
              const finalNotificationArray = notificationArray.filter(
                (item) => item !== null,
              );
              dispatch(setNotification(finalNotificationArray));
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (!isTourOpen) {
      if (notification.length === 0) {
        fetchNotification();
      }
    } else {
      dispatch(setNotification(notificationData));
    }
  }, []);

  return (
    <div className="flex h-screen w-full flex-col">
      <div className="flex flex-row relative">
        <div className="w-full 2xl:col-span-10 md:col-span-4 col-span-3">
          <div className="w-full pl-8 pt-3 pb-0 justify-between flex flex-row items-center">
            <span className="font-sans text-xl lg:text-2xl font-semibold text-blueGray-200">
              Hello {userName.split(' ')[0]}, Welcome to astRai
            </span>
            <div className=" flex flex-row space-x-3">
              <Link
                content="Watch Tour"
                onClick={() => {
                  resetAllFilters();
                  dispatch(setIsTourOpen(true));
                }}
              />
              <div className=" flex flex-row space-x-1">
                {!notificationPanelVisible ? (
                  <img
                    // data-tooltip-id="table-badge"
                    // data-tooltip-content="Toggle Notification"
                    onClick={togglePinNotification}
                    src="/assets/images/notification.svg"
                    className="size-[25px] cursor-pointer mr-6 notification"
                    alt="pin"
                  />
                ) : (
                  <img
                    onClick={togglePinNotification}
                    src="/assets/images/right-arrow.svg"
                    className="size-[25px] cursor-pointer mr-3"
                    alt="pin"
                  />
                )}
              </div>
            </div>
          </div>
          <div className="flex size-full flex-col space-y-2 py-3 pl-8">
            <div className="max-h-[calc(100vh-60px)] w-full flex-wrap pb-6 overflow-auto">
              <div className="flex flex-col insight-cards">
                <span className=" pb-2 font-sans text-xl font-semibold leading-8 text-blueGray-300">
                  Insights
                </span>
                <InsightCards />
              </div>
              <div className="flex flex-col data-management">
                <span className=" pb-2 font-sans text-xl font-semibold leading-8 text-blueGray-300">
                  Data Management
                </span>
                {/* <DataManagement /> */}
                <div className="grid grid-cols-4 w-full">
                  {cardData.map((data: any, index: any) => (
                    <div
                      className={`${data.tour} ${
                        index === 0 ? 'data-management-dash' : ''
                      } mb-4 mr-4 h-full mt-0 col-span-1 rounded bg-white-200 p-4`}
                      key={index}
                    >
                      <div className="flex min-h-[40%] w-full flex-row">
                        <div className="flex w-[75%] flex-col space-y-1 overflow-hidden ">
                          <span className="font-sans text-base font-semibold text-gray-500">
                            {data.name}
                          </span>
                          <span
                            className="font-sans text-xs font-normal text-gray-400"
                            dangerouslySetInnerHTML={{
                              __html: data.text,
                            }}
                          />
                        </div>
                        <div className="flex w-[25%] items-center justify-center">
                          <div className="flex size-16 items-center justify-center rounded-full bg-slate-100">
                            <img
                              className="size-[30px]"
                              src={`/assets/images/sidebar/${data.icon}`}
                              alt={data.icon}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col space-y-1">
                        {data.dashboard.map((dashboard: any, rowIndex: any) => (
                          <button
                            type="button"
                            key={rowIndex}
                            onClick={() => {
                              navigateToCardRoute(
                                dashboard.name,
                                dashboard.href,
                              );
                            }}
                            className="flex h-[44px] w-full cursor-pointer items-center  text-left rounded-sm bg-slate-100 px-2 font-sans text-sm font-semibold  text-blue-200"
                          >
                            {dashboard.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        {notificationPanelVisible && (
          <div className="shadow-sidebar absolute md:relative right-0 notification min-w-[30vh] 2xl:col-span-2 md:col-span-1 col-span-2 favorites flex flex-col  h-screen rounded bg-white-200 px-4 pb-6 pt-2">
            <div className="mb-2 flex w-full space-y-2 flex-col h-screen">
              <img
                onClick={togglePinNotification}
                src="/assets/images/right-arrow.svg"
                className="size-[25px] cursor-pointer mr-3 block md:hidden"
                alt="pin"
              />
              <button
                type="button"
                className="flex h-[40px] bg-white-200 border-b border-lightgray-100 w-full cursor-pointer flex-row items-center justify-between px-[13px] py-3"
              >
                <span className="font-sans text-sm font-semibold text-blue-200">
                  Notification
                </span>
              </button>

              <div
                className={`px-1 pb-2 
              max-h-[calc(100vh-100px)]
                `}
              >
                <NotificationBox />
              </div>
            </div>
          </div>
        )}
      </div>
      <Modal
        isOpen={modalOpen}
        headerTitle="Configure Favourites"
        component={
          <ConfigurationModal overallData={allData} favItems={favData} />
        }
        removePannelBorder
        closeModal={closePopupModal}
        panelWidth="lg:w-[50vw] w-[80vw]"
        isActionButtonVisible
        actionbuttonText="Save"
      />
      <Tooltip
        className="custom-tooltip"
        id="table-badge"
        place="left"
        variant="info"
        positionStrategy="fixed"
      />
    </div>
  );
};

export default HomeComp;
