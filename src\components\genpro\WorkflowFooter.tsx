'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';

interface WorkflowFooterProps {
  // Status section props
  statusIcon?: React.ReactNode;
  statusText?: string;
  statusSubtext?: string;
  isLoading?: boolean;
  loadingText?: string;
  
  // Button props
  backButton?: {
    text: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  secondaryButton?: {
    text: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  primaryButton: {
    text: React.ReactNode;
    onClick: () => void;
    disabled?: boolean;
    loading?: boolean;
    ariaLabel?: string;
  };
  
  // Additional status items
  additionalStatus?: React.ReactNode[];
}

const WorkflowFooter: React.FC<WorkflowFooterProps> = ({
  statusIcon,
  statusText,
  statusSubtext,
  isLoading,
  loadingText,
  backButton,
  secondaryButton,
  primaryButton,
  additionalStatus = []
}) => {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40">
      <div className="max-w-full mx-auto px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            {isLoading ? (
              <>
                <div className="size-4 animate-spin rounded-full border-b-2 border-blue-600" />
                <span className="text-xs font-medium text-blue-600">
                  {loadingText}
                </span>
              </>
            ) : (
              <>
                {statusIcon}
                {statusText && (
                  <span className="text-xs font-medium text-gray-700">
                    {statusText}
                  </span>
                )}
                {statusSubtext && (
                  <>
                    <span className="text-xs text-gray-400">•</span>
                    <span className="text-xs text-gray-600">{statusSubtext}</span>
                  </>
                )}
                {additionalStatus.map((item, index) => (
                  <React.Fragment key={index}>
                    <span className="text-xs text-gray-400">•</span>
                    {item}
                  </React.Fragment>
                ))}
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {backButton && (
              <Button
                className="flex h-[32px] items-center px-3 text-xs"
                type="button"
                onClick={backButton.onClick}
                intent="secondary"
                disabled={backButton.disabled || isLoading}
              >
                {backButton.text}
              </Button>
            )}
            
            {secondaryButton && (
              <Button
                className="flex h-[32px] items-center px-3 text-xs"
                type="button"
                onClick={secondaryButton.onClick}
                intent="secondary"
                disabled={secondaryButton.disabled || isLoading}
              >
                {secondaryButton.text}
              </Button>
            )}
            
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              onClick={primaryButton.onClick}
              disabled={primaryButton.disabled || isLoading}
              aria-label={primaryButton.ariaLabel}
            >
              {primaryButton.text}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowFooter;