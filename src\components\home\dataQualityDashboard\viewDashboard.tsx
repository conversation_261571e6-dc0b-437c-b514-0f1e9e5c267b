/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

const DataQualityDashboardComp: React.FC<{
  screen?: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const router = useRouter();
  console.log(props);
  // Methods
  const goBack = () => {
    router.push('/systemAdmin');
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Quality Dashboard
        </span>
      </div>
      <div className="grid h-[calc(100vh-60px)]">
        <iframe
          title="Data Collaboration Insights_Buyer"
          width="100%"
          height="100%"
          src="https://app.powerbi.com/reportEmbed?reportId=738c3a84-d3e4-4b04-b7d7-342db84621bd&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"
          frameBorder="0"
          allowFullScreen
        />
      </div>
    </div>
  );
};

export default DataQualityDashboardComp;
