import type { Meta, StoryObj } from '@storybook/react';

import { Dropdown } from './Dropdown';

const meta: any = {
  title: 'UI/Dropdown',
  component: Dropdown,
  args: {
    menuTitle: 'Menu',
    data: [
      { option: 'Option 01' },
      { option: 'Option 02' },
      {
        subSection: {
          data: [{ option: 'Option 04' }, { option: 'Option 03' }],
          title: 'Section 01',
        },
      } as any,
    ],
  },
} satisfies Meta<typeof Dropdown>;

export default meta;
type Story = StoryObj<typeof meta>;

const Regular: Story = {
  args: {
    menuTitle: 'Menu',
    data: [
      { option: 'Option 01' },
      { option: 'Option 02' },
      { option: 'Option 03' },
      { option: 'Option 04' },
    ],
  },
};

const Sections: Story = {
  args: {
    menuTitle: 'Menu',
    data: [
      {
        subSection: {
          title: 'Section 01',
          data: [{ option: 'Option 01' }, { option: 'Option 02' }],
        },
      },
      {
        subSection: {
          title: 'Section 02',
          data: [{ option: 'Option 03' }, { option: 'Option 04' }],
        },
      },
    ],
  },
};

const Mixed: Story = {
  args: {
    data: [
      { option: 'Option 01' },
      { option: 'Option 02' },
      {
        subSection: {
          title: 'Section 01',
          data: [{ option: 'Option 03' }, { option: 'Option 04' }],
        },
      } as any,
    ],
  },
};

export { Mixed, Regular, Sections };
