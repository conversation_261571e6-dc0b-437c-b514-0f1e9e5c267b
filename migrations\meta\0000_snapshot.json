{"version": "5", "dialect": "sqlite", "id": "33ba2aeb-9902-4f71-9e40-f18012cd5d31", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"guestbook": {"name": "guestbook", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "body": {"name": "body", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(strftime('%s', 'now'))"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(strftime('%s', 'now'))"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}