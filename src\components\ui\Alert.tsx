import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import Image from 'next/image';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

const alert = cva(
  'flex w-[488px] flex-col rounded-lg border-[1px] pb-4 pl-4 pr-6  pt-3 font-sans',
  {
    variants: {
      intent: {
        success: ['border-green-100', 'bg-green-100', 'text-white-100'],
        error: ['border-red-100', 'bg-red-300', 'text-white-100'],
        warning: ['border-yellow-100', 'bg-yellow-100', 'text-white-100'],
        info: ['border-blue-100', 'bg-blue-100', 'text-white-100'],
      },
    },
    compoundVariants: [
      {
        intent: 'success',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'success',
    },
  },
);

export interface AlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alert> {}

export const Alert: React.FC<AlertProps> = ({ className, ...props }) => {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const alertState = useSelector(
    (state: RootState) => state.metadata.toastAlert,
  );

  // Constants
  const { intent } = alertState;

  // Methods
  const closeAlert = () => {
    if (alertState.isToastOpen) {
      dispatch(
        setToastAlert({
          isToastOpen: false,
          intent: '',
          title: '',
          content: '',
        }),
      );
    }
  };

  // Effects
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (alertState.isToastOpen) {
      timeoutId = setTimeout(() => {
        closeAlert();
      }, 3000);
    }
    return () => clearTimeout(timeoutId);
  }, [alertState.isToastOpen]);

  return (
    <div
      className={` z-50 ${
        alertState.isToastOpen ? 'fixed right-4 top-3 flex' : 'hidden'
      }`}
    >
      <div className={alert({ intent, className })} {...props}>
        <div className="flex flex-row items-center justify-between space-x-4 pb-[8px]">
          <span className="font-sans text-sm font-semibold leading-6 ">
            {alertState.title}
          </span>
          <Image
            onClick={closeAlert}
            className="ml-auto cursor-pointer"
            src="/assets/images/cross.svg"
            alt="close"
            width={24}
            height={24}
          />
        </div>
        <div className="font-sans text-sm font-normal leading-5 text-white-100">
          {alertState.content}
        </div>
      </div>
    </div>
  );
};
