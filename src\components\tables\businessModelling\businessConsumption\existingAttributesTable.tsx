/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable import/no-cycle */

import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Badge } from '@/components/ui/Badge';
import { Link } from '@/components/ui/Link';
import { Searchbar } from '@/components/ui/Searchbar';
import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import {
  setEntityAttributes,
  setExistingAttributes,
} from '@/slices/businessModelCrudSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const SelectExistingAttribute: React.FC<{
  mode: string | string[] | undefined;
  id?: string;
}> = (props) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [data, setData] = useState([]);
  const [header, setHeader] = useState([
    {
      title: '',
      optionsEnabled: false,
      options: [],
      disable: props.mode === 'View',
      inputType: 'checkbox',
    },
    {
      title: 'Entity Name',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Data Type',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Length',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'FormulA',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
  ]);
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState([]);

  // Selectors
  const joinConfig = useSelector(
    (state: any) => state.businessModelCrud.joinConfig,
  );
  const existingAttributes = useSelector(
    (state: any) => state.businessModelCrud.existingAttributes,
  );
  const entityAttributes = useSelector(
    (state: any) => state.businessModelCrud.entityAttributes,
  );

  // Methods
  const uniqueTables: any = Object.values(joinConfig).reduce(
    (tables: any, entry: any) => {
      if (entry.parent_table !== '') {
        tables.add(entry.parent_table);
      }
      if (entry.child_table !== '') {
        tables.add(entry.child_table);
      }
      return tables;
    },
    new Set(),
  );
  const uniqueTablesArray = Array.from(uniqueTables);
  // function extractAttributes(entities: any) {
  //   const resultArray: any = [];

  //   entities.forEach((entity: any) => {
  //     const entityName = entity.entity_name;
  //     entity.attributes.forEach((attribute: any) => {
  //       resultArray.push({
  //         entity_name: entityName,
  //         attribute_name: attribute.attribute_name,
  //       });
  //     });
  //   });

  //   return resultArray;
  // }
  // console.log(extractAttributes(data), 'data');
  // const [FormattedData, setFormattedData]: any = useState([]);

  const handleRowSelection = (rows: any) => {
    const newExisitingAttributes: any = {};
    rows?.forEach((row: any) => {
      newExisitingAttributes[row?.id] = {};
      newExisitingAttributes[row?.id].entity_name = row?.components[1]?.value;
      newExisitingAttributes[row?.id].attribute_name =
        row?.components[2]?.value;
      newExisitingAttributes[row?.id].entity_id = row?.entity_id;
      newExisitingAttributes[row?.id].attribute_id = row?.id;
    }, {});
    dispatch(setExistingAttributes(newExisitingAttributes));
  };

  const formatDatasetForTable = (result: any) => {
    const dataRows: any = [];
    result.forEach((entity: any) => {
      entity.attributes.forEach((attribute: any) => {
        const isSelected = attribute.id in existingAttributes;
        const row: any = {
          id: attribute.id,
          entity_id: entity.entity_id,
          selected: isSelected,
          components: [],
        };
        row.components.push({
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'checkbox',
        });
        row.components.push({
          value: entity.entity_name,
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        });
        row.components.push({
          value: attribute.attribute_name,
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        });
        row.components.push({
          value: attribute.attribute_datatype
            ? attribute.attribute_datatype
            : 'N/A',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        });
        row.components.push({
          value: attribute.length ? attribute.length : 'N/A',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        });
        row.components.push({
          value: attribute.formula ? attribute.formula : 'N/A',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        });
        dataRows.push(row);
      });
    });
    return dataRows;
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const filterComponentsInViewMode = (tableData: any) => {
    return tableData.map((item: any) => {
      if (item.selected) {
        const [, ...remainingComponents] = item.components;
        return {
          ...item,
          components: remainingComponents,
        };
      }
      return item;
    });
  };

  const removeFirstIndexFromHeader = () => {
    const updatedHeader = [
      {
        title: 'Entity Name',
        optionsEnabled: false,
        options: [],
        inputType: 'text',
      },
      {
        title: 'Attribute',
        optionsEnabled: false,
        options: [],
        inputType: 'text',
      },
      {
        title: 'Data Type',
        optionsEnabled: false,
        options: [],
        inputType: 'text',
      },
      {
        title: 'Length',
        optionsEnabled: false,
        options: [],
        inputType: 'text',
      },
      {
        title: 'FormulA',
        optionsEnabled: false,
        options: [],
        inputType: 'text',
      },
    ];
    setHeader(updatedHeader);
  };

  const goToLineageFlow = () => {
    const parameter: any = {
      id: props.id,
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/businessModelling/flow?${queryString}`);
  };

  // Effects
  useEffect(() => {
    const entityData = entityAttributes.filter((obj: any) =>
      uniqueTablesArray.includes(obj.entity_name),
    );
    const formattedData = formatDatasetForTable(entityData);
    setData(formattedData);
    setFilteredData(formattedData);
    if (props.mode === 'View') {
      const filterComponents = filterComponentsInViewMode(formattedData);
      removeFirstIndexFromHeader();
      const filterTableData = filterComponents.filter(
        (item: any) => item.selected === true,
      );
      setData(filterTableData);
      setFilteredData(filterTableData);
    }
  }, [entityAttributes, existingAttributes]);

  useEffect(() => {
    function getResolvedData() {
      if (entityAttributes.length === 0) {
        const url =
          ApiUtilities.getApiServerUrl +
          ApiUtilities.apiPath.getExistingAttributes.url;
        const apiData = apiService.getRequest(url);
        apiData.then((resp) => {
          dispatch(setEntityAttributes(resp.data));
        });
      }
    }
    if (props.mode === 'View') {
      getResolvedData();
    }
  }, []);
  return (
    <div className="flex h-full flex-col">
      <div className=" flex w-full flex-row items-center justify-between">
        <div className="flex flex-row space-x-2">
          {props.mode === 'View' ? (
            <span className="font-sans text-base font-semibold text-blueGray-300">
              Existing Attributes
            </span>
          ) : (
            <span className="font-sans text-base font-semibold text-blueGray-300">
              Select Existing Attributes
            </span>
          )}
          <Badge content={String(filteredData.length)} intent="success" />
        </div>

        <div className="flex flex-row items-center space-x-5">
          {props.mode === 'View' && (
            <Link content="View Data Lineage" onClick={goToLineageFlow} />
          )}

          <div className="w-[30vw]">
            {' '}
            <Searchbar
              value={searchItem}
              placeholder="Search"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div
        className={`mt-4  w-full overflow-auto bg-gray-300 ${
          props.mode === 'View' ? 'h-[100%]' : 'h-[85%]'
        }`}
      >
        <Table
          data={filteredData}
          header={header}
          isDashTable={false}
          isCondensedTable
          enableOptions={false}
          isView={props.mode === 'View'}
          onRowSelection={handleRowSelection}
        />
      </div>
    </div>
  );
};

export default SelectExistingAttribute;
