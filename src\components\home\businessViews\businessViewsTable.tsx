'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import eData from 'public/testdata/businessViews/businessViewsTable.json';
import React, { useEffect, useMemo, useState } from 'react';
import DataTable from 'react-data-table-component';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';

// import { Link } from '@/components/ui/Link';
import { Searchbar } from '@/components/ui/Searchbar';
// import { downloadMenuTitle } from '@/constants/menuSvg';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';
import { formatSimpleTableData } from '@/utils/utilityHelper';

const BusinessViewsTableComp: React.FC = () => {
  // essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const searchParams = useSearchParams();
  const name: any = searchParams?.get('name');
  const physicalName: any = searchParams?.get('physicalName');
  // States
  const [height, setHeight] = useState('0px');
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const [totalRows, setTotalRows] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const customStyles = {
    headCells: {
      style: {
        border: '.5px solid #D0D0D0',
        backgroundColor: '#F0F0F0',
      },
    },
    cells: {
      style: {
        border: '.5px solid #D0D0D0',
      },
    },
  };
  const [searchItem, setSearchItem] = useState('');
  const [allTableData, setAllTableData] = useState<any>([]);
  const [filteredData, setFilteredData] = useState<any>([]);
  const [filteredHeaders, setFilteredHeaders] = useState<any>([]);

  const handleSearch = (query: string) => {
    setSearchItem(query);
    const filteredValues: any = allTableData.filter((item: any) =>
      Object.values(item).some((val: any) => {
        const value = val;
        return (
          (typeof value === 'string' &&
            value.toLowerCase().includes(query.toLowerCase())) ||
          (typeof value === 'number' &&
            value.toString().toLowerCase().includes(query)) ||
          (typeof value === 'object' &&
            value instanceof Date &&
            value.toISOString().toLowerCase().includes(query))
        );
      }),
    );
    setFilteredData(filteredValues);
  };

  const formatHeader = (data: any) => {
    return data.map((item: any) => {
      const camelCaseKey = item
        .replace(/\s+\(.*?\)|[^a-zA-Z0-9]+(.)/g, (chr: any) =>
          chr ? chr.toUpperCase() : '',
        )
        .replace(/^[A-Z]/, (chr: any) => chr.toLowerCase());
      return {
        name: item,
        selector: (row: any) => row[camelCaseKey],
        sortable: true,
      };
    });
  };

  const formatRows = (headers: any, rows: any) => {
    return rows.map((row: any, index: any) => {
      const formattedRow: any = { id: index + 1 };
      headers.forEach((header: any, i: any) => {
        const camelCaseKey = header
          .replace(/\s+\(.*?\)|[^a-zA-Z0-9]+(.)/g, (chr: any) =>
            chr ? chr.toUpperCase() : '',
          )
          .replace(/^[A-Z]/, (chr: any) => chr.toLowerCase());
        formattedRow[camelCaseKey] = row[i];
      });
      return formattedRow;
    });
  };

  // const handleDownload = () => {
  //   const tableData: any = {
  //     headers: filteredHeaders,
  //     rows: filteredData,
  //   };
  //   const csv = jsonToCsv(tableData);
  //   const csvData = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  //   const link = document.createElement('a');
  //   if (link.download !== undefined) {
  //     const url = URL.createObjectURL(csvData);
  //     link.setAttribute('href', url);
  //     link.setAttribute('download', `${name}.csv`);
  //     link.style.visibility = 'hidden';
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //   }
  // };

  const fetchTableData = async (perPageData: any, pageNumber: any) => {
    setSearchItem('');
    try {
      const url = `${
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getBusinessViewsViewTableData.url
      }/${physicalName}/${pageNumber}/${perPageData}`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const tableData: any = {
              headers: formatHeader(res.data.columns),
              data: formatRows(res.data.columns, res.data.data),
            };
            setTotalRows(res.data.totalRecords);
            setFilteredHeaders(tableData.headers);
            setAllTableData(tableData.data);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };
  // Methods
  const goBack = () => {
    router.back();
  };

  const handlePageChange = (page: any) => {
    fetchTableData(perPage, page);
  };

  const handlePerRowsChange = async (newPerPage: any, page: any) => {
    setPerPage(newPerPage);
    fetchTableData(newPerPage, page);
  };

  useEffect(() => {
    if (filteredData.length > 0) {
      setFilteredData(filteredData);
    }
  }, [filteredData]);

  useEffect(() => {
    if (!isTourOpen) {
      fetchTableData(perPage, 1);
    } else {
      setAllTableData(formatSimpleTableData(eData));
    }
  }, []);
  useEffect(() => {
    if (allTableData) {
      setFilteredData(allTableData);
    }
  }, [allTableData]);

  useEffect(() => {
    const calculateHeight = () => {
      const newHeight = window.innerHeight - 260;
      setHeight(`${newHeight}px`);
    };

    calculateHeight();
    window.addEventListener('resize', calculateHeight);
    return () => window.removeEventListener('resize', calculateHeight);
  }, []);

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {name || 'View Business Views'}
        </span>
      </div>
      <div className="flex h-[calc(100vh-100px)] w-full flex-col rounded border border-lightgray-100 bg-white-200 p-4">
        <div className="flex w-full items-center justify-between px-2 py-3">
          <div className="w-[30vw]">
            {' '}
            <Searchbar
              value={searchItem}
              placeholder="Search"
              onChange={(e: any) => handleSearch(e.target.value)}
            />
          </div>
          {/* {filteredData && (
            <span className="pl-2">
              <Link content={downloadMenuTitle} onClick={handleDownload} />
            </span>
          )} */}
        </div>
        {filteredData && filteredHeaders && (
          <div className="relative h-[calc(100vh-200px)] w-full overflow-auto px-2">
            <DataTable
              className="border border-lightgray-100"
              columns={filteredHeaders}
              data={filteredData}
              customStyles={customStyles}
              pagination
              paginationServer
              paginationTotalRows={totalRows}
              onChangeRowsPerPage={handlePerRowsChange}
              onChangePage={handlePageChange}
              fixedHeader
              fixedHeaderScrollHeight={height}
            />
          </div>
        )}
      </div>
      <Tooltip
        className="info-tooltip"
        id="download-tooltip"
        place="left"
        variant="info"
        positionStrategy="fixed"
      />
    </div>
  );
};

export default BusinessViewsTableComp;
