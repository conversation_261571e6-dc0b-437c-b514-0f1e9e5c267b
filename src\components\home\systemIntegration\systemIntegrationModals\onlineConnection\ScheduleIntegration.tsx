/* eslint-disable import/no-extraneous-dependencies */
import 'react-js-cron/dist/styles.css';

import { Cron } from 'react-js-cron';
import { useDispatch, useSelector } from 'react-redux';

import { setSchedulingConfig } from '@/slices/sourceSystemCrudSlice';

import { Paragraph } from '../../../../ui/Paragraph';

export function ScheduleIntegration() {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const schedulingConfig = useSelector(
    (state: any) => state.sourceSystemsCrud.schedulingConfig,
  );

  return (
    <div className="my-3 lg:h-[calc(100vh-250px)] h-[calc(100vh-400px)] overflow-auto pt-3">
      <Paragraph
        content="Setup the schedule to initiate the integration"
        intent="p300"
      />
      <div>
        <div className="mt-5 flex flex-row items-center justify-start space-x-3">
          <span className="flex font-sans text-sm leading-4 tracking-[0.2px] text-gray-500">
            <div className="inline w-full">
              <Cron
                value={schedulingConfig || '* * * * *'}
                setValue={(v: any) => {
                  dispatch(setSchedulingConfig(v));
                }}
                humanizeLabels
                humanizeValue
                clearButton
              />
            </div>
          </span>
        </div>
      </div>
    </div>
  );
}
