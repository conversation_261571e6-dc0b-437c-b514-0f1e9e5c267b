/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { type RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import PageLoader from '../loader';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const CreateNewPassword: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const resetPasswordEmailValue = useSelector(
    (state: RootState) => state.resetPassword.value,
  );

  // States
  const isLoading = false;

  // Methods
  const resetNewPassword = async (code: string, password: string) => {
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.confirmPassword.url;
    const params = {
      username: resetPasswordEmailValue,
      code,
      password,
    };
    apiService
      .unauthorizedPostRequest(url, params)
      .then(() => {
        // TODO: set user details in redux
        router.push('/login');
      })
      .catch(() => {
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Something Went Wrong',
            content: 'Something Went Wrong',
          }),
        );
      });
  };

  return (
    <div className="flex h-full w-full items-center justify-center">
      {!isLoading && (
        <div className="flex min-h-[30vh] w-[30vw] flex-col">
          <div className=" mb-10 flex flex-col space-y-1">
            <span className="font-sans text-6xl font-normal leading-[70.81px] text-gray-800">
              Create New Password
            </span>
            <span className="font-sans text-base font-normal leading-5 text-gray-900">
              Create a strong password for your account
            </span>
          </div>
          <Formik
            initialValues={{ code: '', password: '', confirmPassword: '' }}
            validate={(values) => {
              const errors: any = {};
              if (!values.password) {
                errors.password = 'Required.';
              } else if (values.password.length < 8) {
                errors.password =
                  'Password must be at least 8 characters long.';
              } else if (
                !/(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,}/.test(
                  values.password,
                )
              ) {
                errors.password =
                  'Password must contain at least one uppercase letter, one lowercase letter, one special character, and one number, and be at least 8 characters long.';
              }

              if (!values.confirmPassword) {
                errors.confirmPassword = 'Required.';
              } else if (values.confirmPassword !== values.password) {
                errors.confirmPassword = 'Passwords do not match.';
              }
              return errors;
            }}
            onSubmit={(values, { setSubmitting }) => {
              setTimeout(() => {
                // setLoader();
                setSubmitting(false);
                // can show loader if wanted
                resetNewPassword(values.code, values.password);
                setSubmitting(false);
              }, 400);
            }}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              isSubmitting,
              handleSubmit,
            }) => (
              <form className="flex flex-col" onSubmit={handleSubmit}>
                <div className="mb-4 w-full">
                  <Input
                    label="Code"
                    name="code"
                    className="w-full"
                    placeholder="1234"
                    type="text"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.code}
                    intent={
                      errors.code && touched.code ? 'hasError' : 'enabled'
                    }
                    error={errors.code && touched.code && errors.code}
                  />
                </div>
                <div className="mb-4 w-full">
                  <Input
                    className="w-full"
                    label="Password"
                    name="password"
                    placeholder="Password"
                    onChange={handleChange}
                    type="password"
                    onBlur={handleBlur}
                    value={values.password}
                    error={
                      errors.password && touched.password && errors.password
                    }
                    intent={
                      errors.password && touched.password
                        ? 'hasError'
                        : 'enabled'
                    }
                  />
                </div>
                <div className="mb-6 w-full">
                  <Input
                    className="w-full"
                    label="Confirm Password"
                    name="confirmPassword"
                    placeholder="Confirm Password"
                    onChange={handleChange}
                    type="password"
                    onBlur={handleBlur}
                    value={values.confirmPassword}
                    error={
                      errors.confirmPassword &&
                      touched.confirmPassword &&
                      errors.confirmPassword
                    }
                    intent={
                      errors.confirmPassword && touched.confirmPassword
                        ? 'hasError'
                        : 'enabled'
                    }
                  />
                </div>
                <Button
                  intent="primary"
                  disabled={
                    isSubmitting ||
                    Object.keys(errors).length > 0 ||
                    !values.code ||
                    !values.password ||
                    values.password !== values.confirmPassword
                  }
                >
                  Get Started
                </Button>
              </form>
            )}
          </Formik>
        </div>
      )}

      {isLoading && (
        <PageLoader
          isLoading={isLoading}
          message="Creating Account and setting up AstRai"
        />
      )}
    </div>
  );
};
export default CreateNewPassword;
