/* eslint-disable no-empty-pattern */
/* eslint-disable import/no-extraneous-dependencies */
import React from 'react';
import LoadingOverlay from 'react-loading-overlay-ts';
import { useSelector } from 'react-redux';

// interface PageLoaderProps {
//   isLoading: boolean;
//   message: string;
// }

const AppLoader: React.FC<any> = () => {
  // Selectors
  const isLoading = useSelector((state: any) => state.appConfig.isLoading);

  return (
    isLoading && (
      <LoadingOverlay
        className="absolute"
        active={isLoading}
        styles={{
          overlay: (base) => ({
            ...base,
            background: 'rgba(0, 0, 0, 0.2)',
          }),
          spinner: (base) => ({
            ...base,
            width: '100px',
            '& svg circle': {
              stroke: 'rgba(0, 0, 0, 0.5)',
            },
          }),
          wrapper: {
            width: '100vw',
            height: '100vh',
            overflow: 'hidden',
          },
        }}
        spinner
      />
    )
  );
};

export default AppLoader;
