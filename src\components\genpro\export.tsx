'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  Download,
  Lock,
  Unlock,
  FileText,
  CheckCircle,
  Calendar,
  Eye,
  EyeOff,
  AlertCircle,
  Info,
  Loader2
} from 'lucide-react';

import { Button } from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import FinancialTable, { FinancialTableColumn, FinancialTableRow } from '@/components/ui/FinancialTable';
import StatusBanner from '@/components/ui/StatusBanner';
import ExportControls, { ExportFormat, ExportOption } from '@/components/ui/ExportControls';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface ExportProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onExportComplete?: (data: any) => void;
}

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const Export = ({ onNext, uploadedFiles, onExportComplete }: ExportProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);
  
  const [isLocked, setIsLocked] = useState(false);
  const [showInternalData, setShowInternalData] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('excel');
  const [includeAuditTrail, setIncludeAuditTrail] = useState(true);
  const [exportResult, setExportResult] = useState<any>(null);
  const [exportHistory, setExportHistory] = useState<any[]>([]);
  const [finalMappingData, setFinalMappingData] = useState<FinancialTableRow[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  const getExportHistory = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproExportHistory?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/export-history`}`;
    return await apiService.genproGetRequest(url);
  };

  const getFinalMapping = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproFinalMapping?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/final-mapping`}`;
    return await apiService.genproGetRequest(url);
  };

  const finalizeReport = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproReportFinalize?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/finalize`}`;
    return await apiService.genproPostRequest(url, {});
  };

  const unlockReport = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproReportUnlock?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/unlock`}`;
    return await apiService.genproPostRequest(url, {});
  };

  const exportReport = async (workflowId: string, options: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproExportReport?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/export`}`;
    return await apiService.genproPostRequest(url, options);
  };

  const sendReportEmail = async (workflowId: string, options: any) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproExportEmail?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/email`}`;
    return await apiService.genproPostRequest(url, options);
  };

  const previewReport = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproExportPreview?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/preview`}`;
    return await apiService.genproGetRequest(url);
  };

  const completeWorkflow = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproWorkflowComplete?.url.replace('{id}', workflowId) || `/workflow/${workflowId}/complete`}`;
    return await apiService.genproPostRequest(url, {});
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  const showInfo = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'info', title: 'Info', content: message }));
  };

  const exportFormats: ExportFormat[] = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: FileText },
    { value: 'csv', label: 'CSV (.csv)', icon: FileText },
    { value: 'pdf', label: 'PDF Report (.pdf)', icon: FileText }
  ];

  const exportOptions: ExportOption[] = [
    {
      key: 'includeAuditTrail',
      label: 'Include Audit Trail',
      description: 'Add audit trail information to the export',
      icon: includeAuditTrail ? CheckCircle : AlertCircle
    },
    {
      key: 'showInternalData',
      label: 'Show Internal Vessel Data',
      description: 'Include internal vessel mapping data for debugging',
      icon: showInternalData ? Eye : EyeOff
    }
  ];

  useEffect(() => {
    if (uploadedFiles) {
      loadDummyExportData();
      loadExportHistory();
      loadFinalMapping();
    }
  }, [uploadedFiles]);

  const loadDummyExportData = async () => {
    try {
      // Load dummy export history
      const dummyHistory = [
        {
          id: 1,
          version: '1.0',
          date: '2025-01-15 14:40:33',
          format: 'Excel',
          size: '1.8 MB',
          user: '<EMAIL>',
          status: 'completed'
        },
        {
          id: 2,
          version: '1.1',
          date: '2025-01-16 10:20:15',
          format: 'PDF',
          size: '2.1 MB',
          user: '<EMAIL>',
          status: 'completed'
        }
      ];
      setExportHistory(dummyHistory);
    } catch (error) {
      console.error('Failed to load dummy export data:', error);
    }
  };

  const loadExportHistory = async () => {
    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await getExportHistory(workflowId);
      setExportHistory(response.data.results || response.data || []);
    } catch (error) {
      console.error('Failed to load export history:', error);
      // Set default history if API fails
      setExportHistory([
        {
          id: 1,
          version: '1.0',
          date: '2025-01-15 14:40:33',
          format: 'Excel',
          size: '1.8 MB',
          user: '<EMAIL>',
          status: 'completed'
        }
      ]);
    }
  };

  const loadFinalMapping = async () => {
    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await getFinalMapping(workflowId);
      const mappingData = response.data.results || response.data || [];

      // Transform API data to FinancialTableRow format
      const transformedData: FinancialTableRow[] = mappingData.map((item: any, index: number) => ({
        id: index + 1,
        date: new Date().toISOString().split('T')[0],
        entityCode: item.entity_code || item.finalSMC || 'Unknown',
        narration: `GenPro BF Allocation - ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`,
        amount: item.amount || item.distributionAmount || 0,
        finalVessel: item.vessel_name || item.finalVessel || 'Unknown'
      }));

      setFinalMappingData(transformedData);
    } catch (error) {
      console.error('Failed to load final mapping:', error);
      // Set default data if API fails
      setFinalMappingData([
        {
          id: 1,
          date: '2025-01-15',
          entityCode: 'BSM HEL',
          narration: 'GenPro BF Allocation - January 2025',
          amount: 33375,
          finalVessel: 'Vessel X'
        },
        {
          id: 2,
          date: '2025-01-15',
          entityCode: 'BSM CYP',
          narration: 'GenPro BF Allocation - January 2025',
          amount: 50000,
          finalVessel: 'Vessel Y'
        }
      ]);
    }
  };

  const handleLock = async () => {
    setLoading(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      
      if (isLocked) {
        await unlockReport(workflowId);
        setIsLocked(false);
        showSuccess('Report has been unlocked and can be modified.');
      } else {
        await finalizeReport(workflowId);
        setIsLocked(true);
        showSuccess('Report has been finalized and locked for export.');
      }
    } catch (error: any) {
      console.error('Failed to toggle lock status:', error);
      showError('Failed to change report lock status', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    setIsExporting(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const exportOptions = {
        format: selectedFormat,
        include_audit_trail: includeAuditTrail,
        show_internal_data: showInternalData,
      };

      const response = await exportReport(workflowId, exportOptions);
      setExportResult(response.data);
      
      // If response includes download URL, trigger download
      if (response.data.download_url) {
        window.open(response.data.download_url, '_blank');
      }

      showSuccess(`Report exported successfully as ${selectedFormat.toUpperCase()}`);

      // Refresh export history
      await loadExportHistory();
    } catch (error: any) {
      console.error('Export failed:', error);
      showError('Failed to generate export', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleEmail = async (email: string) => {
    setIsExporting(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const emailOptions = {
        recipient_email: email,
        format: selectedFormat,
        include_audit_trail: includeAuditTrail,
        show_internal_data: showInternalData,
      };

      await sendReportEmail(workflowId, emailOptions);

      showSuccess(`Report has been sent to ${email}`);

      // Refresh export history
      await loadExportHistory();
    } catch (error: any) {
      console.error('Email sending failed:', error);
      showError('Failed to send email', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handlePreview = async () => {
    setLoading(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await previewReport(workflowId);
      
      if (response.data.preview_url) {
        window.open(response.data.preview_url, '_blank');
      }

      showSuccess('Report preview opened in new window');
    } catch (error: any) {
      console.error('Preview failed:', error);
      showError('Failed to generate preview', error);
    } finally {
      setLoading(false);
    }
  };


  const financialTableColumns: FinancialTableColumn[] = [
    { key: 'date', title: 'Date', type: 'date', align: 'left', sortable: true },
    { key: 'entityCode', title: 'Entity Code', type: 'text', align: 'left', sortable: true },
    { key: 'narration', title: 'Narration', type: 'text', align: 'left' },
    { key: 'amount', title: 'Amount', type: 'currency', align: 'right', sortable: true },
    ...(showInternalData ? [{
      key: 'finalVessel',
      title: 'Final Vessel (Internal)',
      type: 'text' as const,
      align: 'left' as const
    }] : []),
  ];

  const handleCellEdit = (rowId: string | number, columnKey: string, newValue: any) => {
    // Handle cell editing logic with proper API integration
    console.log('Cell edited:', { rowId, columnKey, newValue });
    
    // Update the local state immediately for responsiveness
    setFinalMappingData(prevData => 
      prevData.map(row => 
        row.id === rowId 
          ? { ...row, [columnKey]: newValue }
          : row
      )
    );

    // Optional: Save changes to API
    showInfo(`${columnKey} updated for record ${rowId}`);
  };

  const historyHeader = [
    { title: 'Version', align: 'left' },
    { title: 'Export Date', align: 'left' },
    { title: 'Format', align: 'left' },
    { title: 'Size', align: 'left' },
    { title: 'User', align: 'left' },
    { title: 'Status', align: 'left' },
    { title: 'Actions', align: 'left' },
  ];

  const historyData = exportHistory.map((item) => ({
    id: item.id,
    components: [
      {
        type: 'text',
        value: (
          <span className="font-medium">v{item.version}</span>
        )
      },
      { type: 'text', value: item.date },
      { type: 'text', value: item.format },
      { type: 'text', value: item.size },
      { type: 'text', value: item.user },
      {
        type: 'badges',
        badges: [{
          intent: item.status === 'completed' ? 'success' : 'warning',
          content: item.status.charAt(0).toUpperCase() + item.status.slice(1)
        }]
      },
      {
        type: 'text',
        value: (
          <Button intent="secondary" className="text-xs px-2 py-1">
            <Download className="w-3 h-3 mr-1" />
            Download
          </Button>
        )
      }
    ],
  }));

  return (
    <div className="flex flex-col h-full p-6">
      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto pr-2">

      {/* Always show content for demo */}
      <>
          {/* Export Result */}
          {exportResult && (
            <div className="bg-green-50 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-green-800 flex items-center mb-3">
                <CheckCircle className="w-5 h-5 mr-2" />
                Export Generated Successfully
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Export ID</p>
                  <p className="text-xl font-bold text-green-700">
                    {exportResult.export_id || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">File Size</p>
                  <p className="text-xl font-bold text-green-700">
                    {exportResult.file_size || 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Generated
                  </p>
                  <p className="text-xl font-bold text-green-700">
                    {new Date().toLocaleTimeString()}
                  </p>
                </div>
              </div>
              {exportResult.download_url && (
                <div className="mt-3 flex items-center">
                  <FileText className="w-4 h-4 mr-2 text-green-600" />
                  <a 
                    href={exportResult.download_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-green-600 hover:text-green-800 underline"
                  >
                    Download Export File
                  </a>
                </div>
              )}
            </div>
          )}

          {/* Status Banner with Alert Circle for warnings */}
          <StatusBanner
            status={isLocked ? 'locked' : 'draft'}
            title={`Report Status: ${isLocked ? 'Finalized & Locked' : 'Draft'}`}
            description={
              isLocked
                ? 'Report is locked and ready for export. No further modifications allowed.'
                : 'Report is in draft mode. Finalize to lock and enable export options.'
            }
            actionButton={{
              text: isLocked ? 'Unlock Report' : 'Finalize & Lock',
              onClick: handleLock,
              intent: isLocked ? 'secondary' : 'primary',
              icon: isLocked ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />
            }}
          />

          {/* Lock Status Warning */}
          {!isLocked && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-orange-500 mr-2" />
                <p className="text-sm text-orange-700">
                  <strong>Warning:</strong> Report must be finalized and locked before export options become available.
                </p>
              </div>
            </div>
          )}

          {/* Export Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Records to Export</p>
                  <p className="text-2xl font-bold text-blue-600">{finalMappingData.length}</p>
                </div>
                <FileText className="w-8 h-8 text-blue-500" />
              </div>
            </div>
            <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Export History</p>
                  <p className="text-2xl font-bold text-green-600">{exportHistory.length}</p>
                </div>
                <Calendar className="w-8 h-8 text-green-500" />
              </div>
            </div>
            <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Report Status</p>
                  <p className="text-xl font-bold text-gray-800">{isLocked ? 'Locked' : 'Draft'}</p>
                </div>
                {isLocked ? <Lock className="w-8 h-8 text-gray-500" /> : <Unlock className="w-8 h-8 text-orange-500" />}
              </div>
            </div>
            <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Data Visibility</p>
                  <p className="text-xl font-bold text-gray-800">{showInternalData ? 'Internal' : 'External'}</p>
                </div>
                {showInternalData ? <Eye className="w-8 h-8 text-blue-500" /> : <EyeOff className="w-8 h-8 text-gray-500" />}
              </div>
            </div>
          </div>

          {/* Export Controls */}
          <ExportControls
            formats={exportFormats}
            options={exportOptions}
            selectedFormat={selectedFormat}
            onFormatChange={setSelectedFormat}
            optionValues={{
              includeAuditTrail,
              showInternalData
            }}
            onOptionChange={(key, value) => {
              if (key === 'includeAuditTrail') setIncludeAuditTrail(value);
              if (key === 'showInternalData') setShowInternalData(value);
            }}
            onPreview={handlePreview}
            onDownload={handleDownload}
            onEmail={handleEmail}
            disabled={!isLocked || isExporting}
            className="mb-6"
          />

          {/* Final Mapping Preview */}
          <div className="bg-white-200 rounded-lg border border-lightgray-100 p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-gray-500" />
                <h3 className="text-lg font-semibold">Final Mapping to Source System</h3>
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Last Updated: {new Date().toLocaleDateString()}</span>
                </div>
                <div className="font-semibold">
                  Total Amount: ${finalMappingData.reduce((sum, item) => sum + item.amount, 0).toLocaleString()}
                </div>
              </div>
            </div>
            <div className="max-h-96 overflow-y-auto">
              <FinancialTable
                columns={financialTableColumns}
                data={finalMappingData}
                onCellEdit={handleCellEdit}
                striped={true}
                bordered={true}
                showSearch={false}
                className="financial-data-table"
              />
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
              <div className="flex items-start space-x-2">
                <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-700">
                  <p className="font-semibold mb-1">Export Information:</p>
                  <ul className="space-y-1">
                    <li>• The Final Vessel column is for internal processing only</li>
                    <li>• Use the {showInternalData ? <Eye className="w-3 h-3 inline mx-1" /> : <EyeOff className="w-3 h-3 inline mx-1" />} toggle above to control visibility</li>
                    <li>• Audit trail can be included for compliance tracking</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Export History */}
          <div className="bg-white-200 rounded-lg border border-lightgray-100 p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Export History
            </h3>
            <div className="max-h-96 overflow-y-auto">
              <Table header={historyHeader} data={historyData} isDashTable={false} enableOptions={false} />
            </div>
          </div>
        </>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">Ready to export</span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">Workflow complete</span>
          </div>
          <Button 
            onClick={async () => {
              setSaveLoading(true);
              try {
                const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
                await completeWorkflow(workflowId);
                onExportComplete?.(uploadedFiles);
                showSuccess('GenPro workflow completed successfully!');
                onNext?.();
              } catch (error: any) {
                console.error('Workflow completion failed:', error);
                showError('Failed to complete workflow', error);
              } finally {
                setSaveLoading(false);
              }
            }}
            disabled={saveLoading}
            className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
          >
            {saveLoading ? (
              <>
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                Completing...
              </>
            ) : (
              <>
                <CheckCircle className="w-3 h-3 mr-1" />
                Complete Workflow
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Export;
