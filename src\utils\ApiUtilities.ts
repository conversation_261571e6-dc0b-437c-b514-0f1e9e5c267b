/* eslint-disable class-methods-use-this */
import { AppConfig } from './AppConfig';

export const ApiUtilities: any = {
  apiPath: {
    // BSM API endpoints - DEACTIVATED
    // COMMENTED OUT as of now for Genpro dev
    /*
    login: { url: '/login', method: 'POST', testFile: '/auth/login.json' },
    logout: { url: '/logout', method: 'POST', testFile: '/auth/logout.json' },
    forgotPassword: {
      url: '/users/forgot-password',
      method: 'POST',
      testFile: '/auth/forgot-password.json',
    },
    confirmPassword: {
      url: '/users/confirm-password',
      method: 'POST',
      testFile: '/auth/confirm-password.json',
    },
    getAllDomains: {
      url: '/domains',
      method: 'GET',
      testFile: '',
    },
    getSourceSystems: {
      url: '/source_systems',
      method: 'GET',
      testFile: '/connections/source-systems.json',
    },
    getSourceSystemFormFields: {
      url: '/source_systems/',
      method: 'GET',
      testFile: '/connections/source-system-details.json',
    },
    getTablesForDomainsOfSourceSystem: {
      url: '/tables',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getOnlineConnections: {
      url: '/connections',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getOfflineConnections: {
      url: '/offline_connections',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getDataSets: {
      url: '/datasets',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getDataSetTables: {
      url: '/datasets',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getLogicalEntities: {
      url: '/logical_entities',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getExistingAttributes: {
      url: '/logical_entities/attributes',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getBusinessModels: {
      url: '/business_models',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getBusinessModelAtttributes: {
      url: '/business_models/attributes',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getDatasetTableList: {
      url: '/datasets/tablelist/',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    getUsers: {
      url: '/users',
      method: 'GET',
      testFile: '/auth/confirm-password.json',
    },
    craeteUser: {
      url: '/users',
      method: 'POST',
      testFile: '/auth/confirm-password.json',
    },
    getChatResponse: {
      url: '/chat',
      method: 'POST',
    },
    getBusinessFlow: {
      url: '/business_models',
      method: 'POST',
    },
    getBsmDataSets: {
      url: '/bsm-datasets',
      method: 'GET',
    },
    getFileHistory: {
      url: '/file-groups/file-upload-history',
      method: 'GET',
    },
    postUploadedFiles: {
      url: '/file-groups/upload',
      method: 'POST',
    },
    modifyUploadedFileDetails: {
      url: '/file-groups/modify',
      method: 'PUT',
    },
    submitUploadedFileDetails: {
      url: '/file-groups/submit',
      method: 'POST',
    },
    getNotifications: {
      url: '/file-groups',
      method: 'GET',
    },
    getFilePreviewUrl: {
      url: '/file-groups/get-presigned-url',
      method: 'GET',
    },
    getCountryList: {
      url: '/countries',
      method: 'GET',
    },
    getReports: {
      url: '/report-details',
      method: 'GET',
    },
    getBsmBusinessViews: {
      url: '/business-views',
      method: 'GET',
    },
    getBsmDataAvailabilityMonthly: {
      url: '/file-groups/dashboard/years/',
      method: 'GET',
    },
    getReconTableData: {
      url: '/recon/test-names/',
      method: 'GET',
    },
    getReconStatusTableData: {
      url: '/recon/test/',
      method: 'GET',
    },
    getDataValidationTableData: {
      url: '/file-validation/years/',
      method: 'GET',
    },
    getReportFreshnessData: {
      url: '/report-freshness/status ',
      method: 'GET',
    },
    getReportFreshnessPendingDatasets: {
      url: '/report-freshness',
      method: 'GET',
    },
    getDataQualityCardsData: {
      url: '/test-scenario-summary/',
      method: 'GET',
    },
    getBusinessViewsViewTableData: {
      url: '/business-views/view',
      method: 'GET',
    },
    refreshAllDashboard: {
      url: '/power-bi/refresh/all',
      method: 'POST',
    },
    refreshDashboard: {
      url: '/power-bi/refresh',
      method: 'POST',
    },
    getDashboards: {
      url: '/power-bi/dashboard-details',
      method: 'GET',
    },
    */
    // GenPro API endpoints
    genproLogin: {
      url: '/auth/login/',
      method: 'POST',
      server: 'genpro',
    },
    genproRegister: {
      url: '/auth/register/',
      method: 'POST',
      server: 'genpro',
    },
    genproRefreshToken: {
      url: '/auth/refresh/',
      method: 'POST',
      server: 'genpro',
    },
    genproLogout: {
      url: '/auth/logout/',
      method: 'POST',
      server: 'genpro',
    },
    genproProfile: {
      url: '/auth/profile/',
      method: 'GET',
      server: 'genpro',
    },
    genproFileUpload: {
      url: '/file-upload/',
      method: 'POST',
      server: 'genpro',
    },
    genproFileList: {
      url: '/file-upload/',
      method: 'GET',
      server: 'genpro',
    },
    genproFileValidate: {
      url: '/file-upload/{id}/validate_file/',
      method: 'POST',
      server: 'genpro',
    },
    genproFileDetails: {
      url: '/file-upload/{id}/',
      method: 'GET',
      server: 'genpro',
    },
    genproFileDelete: {
      url: '/file-upload/{id}/',
      method: 'DELETE',
      server: 'genpro',
    },
    genproUploadWorkflowFiles: {
      url: '/file-upload/upload_workflow_files/',
      method: 'POST',
      server: 'genpro',
    },
    genproWorkflowCreate: {
      url: '/workflow-run/',
      method: 'POST',
      server: 'genpro',
    },
    genproWorkflowList: {
      url: '/workflow-run/',
      method: 'GET',
      server: 'genpro',
    },
    genproWorkflowStart: {
      url: '/workflow-run/{id}/start/',
      method: 'POST',
      server: 'genpro',
    },
    genproWorkflowStatus: {
      url: '/workflow-run/{id}/status/',
      method: 'GET',
      server: 'genpro',
    },
    genproWorkflowAddFiles: {
      url: '/workflow-run/{id}/add_files/',
      method: 'POST',
      server: 'genpro',
    },
    genproWorkflowNextStage: {
      url: '/workflow-run/{id}/next_stage/',
      method: 'POST',
      server: 'genpro',
    },
    genproBfCalculate: {
      url: '/bf-overview/calculate_total_bf/',
      method: 'POST',
      server: 'genpro',
    },
    genproTransformation: {
      url: '/transformation-result/apply_transformation/',
      method: 'POST',
      server: 'genpro',
    },
    genproPivotGenerate: {
      url: '/pivot/generate/',
      method: 'POST',
      server: 'genpro',
    },
    genproDistributionCalculate: {
      url: '/distribution/calculate/',
      method: 'POST',
      server: 'genpro',
    },
    genproVesselEntity: {
      url: '/vessel-entity/',
      method: 'GET',
      server: 'genpro',
    },
    genproVesselEntityActive: {
      url: '/vessel-entity/active/',
      method: 'GET',
      server: 'genpro',
    },
    genproVesselEntityBulkCreate: {
      url: '/vessel-entity/bulk_create/',
      method: 'POST',
      server: 'genpro',
    },
    genproVesselEntityUpdate: {
      url: '/vessel-entity/{id}/',
      method: 'PUT',
      server: 'genpro',
    },
    genproVesselEntityDelete: {
      url: '/vessel-entity/{id}/',
      method: 'DELETE',
      server: 'genpro',
    },
    genproSmc: {
      url: '/smc/',
      method: 'GET',
      server: 'genpro',
    },
    genproSmcActive: {
      url: '/smc/active/',
      method: 'GET',
      server: 'genpro',
    },
    genproSmcCreate: {
      url: '/smc/',
      method: 'POST',
      server: 'genpro',
    },
    genproSmcUpdate: {
      url: '/smc/{id}/',
      method: 'PUT',
      server: 'genpro',
    },
    genproSmcDelete: {
      url: '/smc/{id}/',
      method: 'DELETE',
      server: 'genpro',
    },
    genproExport: {
      url: '/export/',
      method: 'POST',
      server: 'genpro',
    },
    genproExportList: {
      url: '/export/',
      method: 'GET',
      server: 'genpro',
    },
    genproExportEmail: {
      url: '/export/{id}/send_email/',
      method: 'POST',
      server: 'genpro',
    },
    genproSystemConfig: {
      url: '/system-config/',
      method: 'GET',
      server: 'genpro',
    },
    genproAnalyticsSummary: {
      url: '/analytics/summary/',
      method: 'GET',
      server: 'genpro',
    },
    genproAnalyticsTrends: {
      url: '/analytics/trends/',
      method: 'GET',
      server: 'genpro',
    },
    genproAuditLog: {
      url: '/audit-log/',
      method: 'GET',
      server: 'genpro',
    },
    genproAuditLogByWorkflow: {
      url: '/audit-log/by_workflow/',
      method: 'GET',
      server: 'genpro',
    },
    genproDashboardAnalytics: {
      url: '/analytics/dashboard/',
      method: 'GET',
      server: 'genpro',
    },
    genproRecentActivity: {
      url: '/analytics/recent-activity/',
      method: 'GET',
      server: 'genpro',
    },
    genproSystemStatus: {
      url: '/system/status/',
      method: 'GET',
      server: 'genpro',
    },
    genproExportHistory: {
      url: '/export/history/',
      method: 'GET',
      server: 'genpro',
    },
    genproFinalMapping: {
      url: '/export/final-mapping/',
      method: 'GET',
      server: 'genpro',
    },
    genproFinalizeReport: {
      url: '/export/finalize/',
      method: 'POST',
      server: 'genpro',
    },
    genproUnlockReport: {
      url: '/export/unlock/',
      method: 'POST',
      server: 'genpro',
    },
    genproExportReport: {
      url: '/export/generate/',
      method: 'POST',
      server: 'genpro',
    },
    genproSendReportEmail: {
      url: '/export/send-email/',
      method: 'POST',
      server: 'genpro',
    },
    genproPreviewReport: {
      url: '/export/preview/',
      method: 'POST',
      server: 'genpro',
    },
    genproCompleteWorkflow: {
      url: '/workflow-run/{id}/complete/',
      method: 'POST',
      server: 'genpro',
    },
  },
  apiKey: 'F3C3ZWb4iD8VKSL3zi5FR3He4CxQnJ7y2vAxnx73',
  getApiServerUrl:
    AppConfig.network.apiServer.protocol +
    AppConfig.network.apiServer.target +
    AppConfig.network.apiServer.portNo +
    AppConfig.network.apiServer.basePath,

  getApiServerUrlBsm:
    AppConfig.network.apiServerBsm.protocol +
    AppConfig.network.apiServerBsm.target +
    AppConfig.network.apiServerBsm.portNo +
    AppConfig.network.apiServerBsm.basePath,

  getApiServerUrlBsmNotification:
    AppConfig.network.apiServerBsmNotification.protocol +
    AppConfig.network.apiServerBsmNotification.target +
    AppConfig.network.apiServerBsmNotification.portNo +
    AppConfig.network.apiServerBsmNotification.basePath,

  getChatApiServerUrl:
    AppConfig.network.chatServer.protocol +
    AppConfig.network.chatServer.target +
    AppConfig.network.chatServer.portNo +
    AppConfig.network.chatServer.basePath,

  getApiServerUrlGenPro:
    AppConfig.network.apiServerGenPro.protocol +
    AppConfig.network.apiServerGenPro.target +
    ':' + AppConfig.network.apiServerGenPro.portNo +
    AppConfig.network.apiServerGenPro.basePath,
};
