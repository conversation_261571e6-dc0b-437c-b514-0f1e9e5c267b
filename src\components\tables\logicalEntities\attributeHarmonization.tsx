/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable jsx-a11y/anchor-is-valid */
import <PERSON> from 'papaparse';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Link } from '@/components/ui/Link';
import Table from '@/components/ui/Table';
import { createMenuName, downloadMenuTitle } from '@/constants/menuSvg';

import { Searchbar } from '../../ui/Searchbar';

const AttributeHarmonization: React.FC<{
  mode: string | string[] | undefined;
  onRowChange?: any;
}> = (props) => {
  // States
  const [searchItem, setSearchItem] = useState('');
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const activatedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.activatedAttributes,
  );
  const mappedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.mappedAttributes,
  );

  // Methods
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const resolveComponent = (
    isValueTypePresent: boolean,
    value: any,
    columnType: any,
    columnKey: any,
    selectList: any,
    placeHolder: any,
    selectedValue?: any,
  ) => {
    return {
      value,
      columnType,
      columnKey,
      disabled: false,
      userAvatarLink: null,
      valueType: isValueTypePresent ? 'multiple' : '',
      type: 'select',
      placeholder: placeHolder,
      selectList: [...selectList],
      selectedValue: selectedValue || {},
    };
  };

  const resolveHeader = () => {
    const newHeader:any = [];
    Object.keys(activatedAttributes)?.forEach((dataSet: any) => {
      newHeader.push({
        title: `${dataSet} Table`,
        optionsEnabled: false,
        options: [],
        inputType: 'select',
      });
      newHeader.push({
        title: `${dataSet} Attribute`,
        optionsEnabled: false,
        options: [],
        inputType: 'select',
      });
    });
    newHeader.push({
      title: 'Harmonized Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'input',
    });
    return newHeader;
  };

  const resolveMapper = (attrToMap: any) => {
    return attrToMap?.reduce((prevValue: any, currValue: any) => {
      prevValue.push({
        name: currValue,
      });
      return prevValue;
    }, []);
  };

  const getAttributeList = (table: any, dataSet: any) => {
    const selectedTableValue = table;
    const attrKeys = Object.keys(
      activatedAttributes?.[dataSet]?.tables?.[selectedTableValue] || {},
    );
    const attrOptions = attrKeys.reduce((prevValue: any, currValue: any) => {
      if (
        activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
          currValue
        ]?.alias
      ) {
        prevValue.push({
          name: activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
            currValue
          ]?.alias,
          datatype:
            activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
              currValue
            ]?.datatype,
          length:
            activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
              currValue
            ]?.length,
          col: currValue,
        });
      } else {
        prevValue.push({
          name: currValue,
          datatype:
            activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
              currValue
            ]?.datatype,
          length:
            activatedAttributes?.[dataSet]?.tables?.[selectedTableValue]?.[
              currValue
            ]?.length,
          col: currValue,
        });
      }
      return prevValue;
    }, []);
    attrOptions.push({ name: 'None' });
    return attrOptions;
  };

const arrangeDataObjects=(headers: any, data: any) => {
  if(data){
    const arrangedData:any=[]
  headers.forEach((header:any) => {
    const title  = header.title;
    const match = data.find((dataObj:any) => {
      const columnTypeFirstLetter = dataObj.columnType.charAt(0).toUpperCase();
      const columnTypeRest = dataObj.columnType.slice(1).toLowerCase();
      const formattedColumnType = columnTypeFirstLetter + columnTypeRest;
       if(title === `${dataObj.columnKey} ${formattedColumnType}`){
        return dataObj
       };
    });
    if (match) {
        arrangedData.push(match);
    }
});
   arrangedData.push(data[data.length-1])
   return arrangedData
  }
}

  const resolveData = () => {
    const data: any = [];
    Object.keys(mappedAttributes || {}).forEach((row, index) => {
      const dataSetsInMappedAttributes = Object.keys(
        mappedAttributes?.[row] || {},
      ).filter((key) => key !== 'harmonizedAttribute');
      dataSetsInMappedAttributes.forEach((dataSet) => {
        const tableKeys = Object.keys(
          activatedAttributes?.[dataSet]?.tables || {},
        );
        const tableOptions = resolveMapper(tableKeys);
        tableOptions.push({ name: 'None' });
        if (
          typeof mappedAttributes?.[row]?.[dataSet] === 'object' &&
          !Array.isArray(mappedAttributes?.[row]?.[dataSet])
        ) {
          const attributeOption = getAttributeList(
            mappedAttributes?.[row]?.[dataSet],
            dataSet,
          );
          if (!data[index]) {
            data[index] = {
              id: 'Vendor Master 1',
              selected: true,
              components: [],
            };
          }
          data[index]?.components.push(
            resolveComponent(
              false,
              mappedAttributes?.[row]?.[dataSet]?.table,
              'table',
              dataSet,
              tableOptions,
              'Select Table',
            ),
            resolveComponent(
              false,
              mappedAttributes?.[row]?.[dataSet]?.attribute,
              'attribute',
              dataSet,
              attributeOption,
              'Select Attribute',
              {
                datatype: mappedAttributes?.[row]?.[dataSet]?.datatype,
                length: mappedAttributes?.[row]?.[dataSet]?.length,
                col: mappedAttributes?.[row]?.[dataSet]?.col,
              },
            ),
          );
        } else if (Array.isArray(mappedAttributes?.[row]?.[dataSet])) {
          let count = 0;
          mappedAttributes?.[row]?.[dataSet].forEach((tableData: any) => {
            if (count > 0) {
              return;
            }
            count += 1;
            const attributeOption = getAttributeList(tableData.table, dataSet);
            if (!data[index]) {
              data[index] = {
                id: 'Vendor Master 1',
                selected: true,
                components: [],
              };
            }
            const tables = mappedAttributes?.[row]?.[dataSet].map(
              (item: any) => item.table,
            );
            const attributes = mappedAttributes?.[row]?.[dataSet].map(
              (item: any) => item.attribute,
            );
            data[index]?.components.push(
              resolveComponent(
                true,
                tables,
                'table',
                dataSet,
                tableOptions,
                'Select Table',
              ),
              resolveComponent(
                true,
                attributes,
                'attribute',
                dataSet,
                attributeOption,
                'Select Attribute',
                {
                  datatype: mappedAttributes?.[row]?.[dataSet]?.datatype,
                  length: mappedAttributes?.[row]?.[dataSet]?.length,
                  col: mappedAttributes?.[row]?.[dataSet]?.col,
                },
              ),
            );
          });
        }
      });
      const dataSetsLeftToMap = Object.keys(activatedAttributes || {}).filter(
        (activatedDataSet) =>
          !Object.keys(mappedAttributes[row] || {}).includes(activatedDataSet),
      );
      dataSetsLeftToMap.forEach((dataSet) => {
        const tableKeys = Object.keys(
          activatedAttributes?.[dataSet]?.tables || {},
        );
        const tableOptions = resolveMapper(tableKeys);
        data[index]?.components.push(
          resolveComponent(
            false,
            null,
            'table',
            dataSet,
            tableOptions,
            'Select Table',
          ),
          resolveComponent(
            false,
            null,
            'attribute',
            dataSet,
            [],
            'Select Attribute',
          ),
        );
      });
      data[index]?.components.push({
        value: mappedAttributes?.[row]?.harmonizedAttribute,
        columnType: 'harmonizedAttribute',
        disabled: false,
        userAvatarLink: null,
        type: 'input',
        placeholder: 'Harmonised Attribute Name',
      });
      const arrangedData = arrangeDataObjects(resolveHeader(), data[index]?.components);
      if(data[index]?.components){
        data[index].components=arrangedData
      }  
    });
    return data;


  };

  // States
  const [data, setData]: any = useState(resolveData());
  const [filteredData, setFilteredData] = useState(data);

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const downloadAttributesList = (filename: string) => {
    const csv = Papa.unparse(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };
  const goToAddNewRow = () => {
    const rowToAdd: any = { components: [] };
    Object.keys(activatedAttributes).forEach((dataSet: any) => {
      const tableKeys = Object.keys(
        activatedAttributes?.[dataSet]?.tables || {},
      );
      const tableOptions = tableKeys.reduce(
        (prevValue: any, currValue: any) => {
          prevValue.push({
            name: currValue,
          });
          return prevValue;
        },
        [],
      );
      rowToAdd.components.push({
        value: null,
        columnType: 'table',
        columnKey: dataSet,
        disabled: false,
        userAvatarLink: null,
        type: 'select',
        placeholder: 'Select Table',
        selectList: [...tableOptions],
      });
      rowToAdd.components.push({
        value: null,
        columnType: 'attribute',
        columnKey: dataSet,
        disabled: false,
        userAvatarLink: null,
        type: 'select',
        placeholder: 'Select Attribute',
        selectList: [],
      });
    });
    const updatedData = [...filteredData, rowToAdd];
    setData(updatedData);
    setFilteredData(updatedData);
  };

  // Effects
  useEffect(() => {
    if (Object.keys(activatedAttributes).length > 0 && Object.keys(mappedAttributes).length > 0) {
      setData(resolveData());
      setFilteredData(resolveData());
    }
  }, [activatedAttributes, mappedAttributes]);

  return (
    <div className={`flex  w-full flex-col space-y-6 pt-6 ${props.mode === 'View'?'h-[calc(100vh-120px)]':'h-[72vh]'}`}>
      <div className="flex w-full flex-row items-center justify-between">
        <div className="w-[40vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        {props.mode !== 'View' && (
          <div className="flex flex-row items-center space-x-4">
            <Link
              content={createMenuName('Add Row')}
              onClick={() => goToAddNewRow()}
            />
            <Link
              content={downloadMenuTitle}
              onClick={() => downloadAttributesList('AttributeList')}
            />
          </div>
        )}
      </div>

      <div className="w-full overflow-auto h-full" onScroll={handleScroll}>
        <Table
          isCondensedTable
          isDashTable
          header={resolveHeader()}
          data={filteredData}
          enableOptions={false}
          isView={props.mode === 'View'}
          enabledCross={props.mode !== 'View'}
          onRowChange={props.onRowChange}
          scrollPosition={scrollPosition}
        />
      </div>
    </div>
  );
};

export default AttributeHarmonization;
