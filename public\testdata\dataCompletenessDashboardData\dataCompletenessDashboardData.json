[{"id": 1, "name": "Monthly Deck", "tableData": {"headers": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "rows": [[{"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Not Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Not Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Not Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Not Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 3, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 1, "tableData": {"header": ["Expected DataSets"], "rows": [{"Expected DataSets": "YTD Consolidated Report additional information"}, {"Expected DataSets": "YTD Consolidated Report after group cost allocation"}, {"Expected DataSets": "YTD Consolidated Report before group cost allocation"}, {"Expected DataSets": "YTD Crew Lumpsum Fee"}, {"Expected DataSets": "YTD Management Fee"}, {"Expected DataSets": "YTD Net profitability report"}, {"Expected DataSets": "YTD Termination fees"}]}}, {"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Not Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Not Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 3, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Not Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Not Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 1, "tableData": {"header": ["Expected DataSets"], "rows": [{"Expected DataSets": "YTD Consolidated Report additional information"}, {"Expected DataSets": "YTD Consolidated Report after group cost allocation"}, {"Expected DataSets": "YTD Consolidated Report before group cost allocation"}, {"Expected DataSets": "YTD Crew Lumpsum Fee"}, {"Expected DataSets": "YTD Management Fee"}, {"Expected DataSets": "YTD Net profitability report"}, {"Expected DataSets": "YTD Termination fees"}]}}, {"completenessScore": 3, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 1, "tableData": {"header": ["Expected DataSets"], "rows": [{"Expected DataSets": "YTD Consolidated Report additional information"}, {"Expected DataSets": "YTD Consolidated Report after group cost allocation"}, {"Expected DataSets": "YTD Consolidated Report before group cost allocation"}, {"Expected DataSets": "YTD Crew Lumpsum Fee"}, {"Expected DataSets": "YTD Management Fee"}, {"Expected DataSets": "YTD Net profitability report"}, {"Expected DataSets": "YTD Termination fees"}]}}, {"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Not Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Not Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}, {"completenessScore": 3, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "YTD Consolidated Report additional information", "Status": "Received"}, {"DataSets": "YTD Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "YTD Consolidated Report before group cost allocation", "Status": "Received"}, {"DataSets": "YTD Crew Lumpsum Fee", "Status": "Received"}, {"DataSets": "YTD Management Fee", "Status": "Received"}, {"DataSets": "YTD Net profitability report", "Status": "Received"}, {"DataSets": "YTD Termination fees", "Status": "Received"}]}}]]}}, {"id": 2, "name": "Quarterly Deck", "tableData": {"headers": ["Q1", "Q2", "Q3", "Q4"], "rows": [[{"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "Forecast Consolidated Report additional information", "Status": "Received"}, {"DataSets": "Forecast Consolidated Report after group cost allocation", "Status": "Not Received"}, {"DataSets": "Forecast Consolidated Report before group cost allocation", "Status": "Received"}]}}, {"completenessScore": 2, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "Forecast Consolidated Report additional information", "Status": "Received"}, {"DataSets": "Forecast Consolidated Report after group cost allocation", "Status": "Not Received"}, {"DataSets": "Forecast Consolidated Report before group cost allocation", "Status": "Received"}]}}, {"completenessScore": 3, "tableData": {"header": ["DataSets", "Status"], "rows": [{"DataSets": "Forecast Consolidated Report additional information", "Status": "Received"}, {"DataSets": "Forecast Consolidated Report after group cost allocation", "Status": "Received"}, {"DataSets": "Forecast Consolidated Report before group cost allocation", "Status": "Received"}]}}, {"completenessScore": 1, "tableData": {"header": ["Expected DataSets"], "rows": [{"Expected DataSets": "Forecast Consolidated Report additional information"}, {"Expected DataSets": "Forecast Consolidated Report after group cost allocation"}, {"Expected DataSets": "Forecast Consolidated Report before group cost allocation"}]}}]]}}, {"id": 3, "name": "Annual and Ad hoc Deck", "tableData": {"headers": [["Budget Consolidated Report additional information", "Budget Consolidated Report after group cost allocation", "Budget Consolidated Report before group cost allocation"], ["Budget Crew Lumpsum Fees", "Budget Management Fees", "Budget Termination Fees"], ["Designation Mapping", "Group Cost Allocations Map"]], "rows": [[[{"completenessScore": 3, "tableData": {"header": [], "rows": []}}, {"completenessScore": 3, "tableData": {"header": [], "rows": []}}, {"completenessScore": 3, "tableData": {"header": [], "rows": []}}]], [[{"completenessScore": 3, "tableData": {"header": [], "rows": []}}, {"completenessScore": 3, "tableData": {"header": [], "rows": []}}, {"completenessScore": 3, "tableData": {"header": [], "rows": []}}]], [[{"completenessScore": 3, "tableData": {"header": [], "rows": []}}, {"completenessScore": 3, "tableData": {"header": [], "rows": []}}]]]}}]