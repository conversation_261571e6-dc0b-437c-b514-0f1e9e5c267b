/* eslint-disable import/no-cycle */
import React, { useEffect, useState } from 'react';

import Table from '@/components/ui/Table';

import { Searchbar } from '../../ui/Searchbar';

const DomainTable: React.FC<any> = ({ mode, tableData, onRowSelection }) => {
  // Constants
  const header: any = [
    {
      title: 'Table Name',
      optionsEnabled: false,
      inputType: mode === 'EDIT' ? 'checkbox' : 'string',
    },
    {
      title: 'Domain',
      optionsEnabled: false,
    },
    // {
    //   title: "Table Description",
    //   optionsEnabled: false,
    // },
  ];
  const data: any = {
    values: tableData,
    enableOptions: false,
  };

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState(data.values);
  // const [formattedDomains, setFormattedDomains] = useState([]);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.values.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  // Efffects
  useEffect(() => {
    setFilteredData(tableData);
  }, [tableData]);

  return (
    <div className="flex h-full flex-col">
      <div className="flex w-full flex-row items-center justify-between">
        <span className="font-sans lg:text-base text-sm font-semibold text-gray-400">
          {mode === 'EDIT'
            ? 'Select tables to ingest the data'
            : 'Selected tables'}
        </span>
        <div className="w-[30vw]">
          <Searchbar
            isCondensed
            value={searchItem}
            placeholder="Search Table"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>
      <div className="my-2 h-[65vh] w-full overflow-auto bg-gray-300">
        <Table
          isCondensedTable
          isDashTable={false}
          header={header}
          data={filteredData}
          enableOptions={data.enableOptions}
          onRowSelection={onRowSelection}
        />
      </div>
    </div>
  );
};

export default DomainTable;
