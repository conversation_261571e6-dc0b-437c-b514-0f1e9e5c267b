/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';

import { Button } from './Button';

export const FeedBackModal: React.FC<any> = ({
  isOpen,
  closeModal,
  panelWidth,
  sendFeedback,
}) => {
  // State
  const [message, setMessage] = useState('');

  // Method
  const handleEnterPress = (e: any) => {
    if (e.key === 'Enter') {
      if (message !== '') {
        sendFeedback(message);
        setMessage('');
        closeModal();
      }
    }
  };

  const handleSubmit = () => {
    if (message !== '') {
      sendFeedback(message);
      setMessage('');
      closeModal();
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-40 h-3/4 " onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className=" fixed inset-0 bg-blueGray-500" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`h-full pt-2  ${
                  panelWidth ?? 'w-[50vw]'
                }  max-w-[90%] overflow-hidden bg-gray-300 px-4 pb-3 pt-0 text-left align-middle shadow-xl transition-all`}
              >
                <Dialog.Title
                  as="h3"
                  className="flex  flex-row items-center justify-between px-2 pt-[10px] text-lg font-medium  text-blueGray-300"
                >
                  <div className="flex flex-row ">
                    <div className="flex flex-row items-center space-x-3">
                      <img
                        src="/assets/images/like.svg"
                        className="h-5 w-5 rotate-180 cursor-pointer"
                        alt="dislike"
                      />
                      <span className="font-sans text-[16px] leading-6 text-gray-500">
                        Provide Additional feedback
                      </span>
                    </div>
                  </div>

                  <button type="button" onClick={closeModal}>
                    <img
                      src="/assets/images/gray-cross.svg"
                      alt="Close modal"
                      className="h-[24px] w-[24px]"
                    />
                  </button>
                </Dialog.Title>
                <div className="relative flex max-h-[30vh] flex-row  justify-between overflow-auto pb-2 pt-3">
                  <textarea
                    value={message}
                    placeholder="What is wrong with this response? How can we improve it?"
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyDown={handleEnterPress}
                    className=" border-1 h-[25vh] w-[50vw] rounded
          border-lightgray-100
          bg-white-200
          px-3
          py-4
          font-sans
          text-sm
          font-normal
          leading-4
          text-blueGray-300
          placeholder:text-gray-400 hover:text-gray-400 focus:border-none focus:border-purple-200 focus:text-gray-400 focus:shadow-blue focus:outline-none focus:ring-0 active:text-blueGray-300"
                  />
                </div>
                <div className=" flex justify-end py-2 ">
                  {' '}
                  <Button
                    type="button"
                    onClick={handleSubmit}
                    className="ml-4 "
                  >
                    Submit
                  </Button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
