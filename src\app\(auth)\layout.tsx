'use client';

import Image from 'next/image';
import React from 'react';

import AppLoader from '@/components/appLoader';

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="flex h-[100vh] w-[100vw] flex-row  ">
      <div className="hidden h-full w-[50%] bg-auth-back lg:flex lg:flex-col">
        <div className="flex h-full w-full flex-col items-center justify-center space-y-8 ">
          <div>
            <Image
              src="/assets/images/astrai-logo-white.svg"
              width={175}
              height={65}
              alt="astrai-logo"
              className="max-w-none"
            />
          </div>
          <div className="flex flex-col items-center justify-center space-y-3 ">
            <div className="font-sans text-xl font-normal leading-[45px] text-white-200/80 xl:text-5xl xl:leading-[58px]">
              Simplify Data
            </div>
            <div className="font-sans text-xl font-normal leading-[45px] text-white-200/80 xl:text-5xl xl:leading-[58px]">
              Amplify Intelligence
            </div>
            <div className="font-sans text-xl font-normal leading-[45px] text-white-200/80 xl:text-5xl xl:leading-[58px]">
              Elevate Collaboration
            </div>
          </div>
        </div>
        <div className="mb-2 flex w-full items-center justify-center bg-slate-500/20 py-1 ">
          <span className="font-sans text-xs font-normal  text-white-200/70">
            @2024. Mid-Office Data. All Rights Reserved.
          </span>
        </div>
      </div>
      <main className=" w-full lg:w-[50%]">{children}</main>
      <AppLoader />
    </div>
  );
};

export default AuthLayout;
