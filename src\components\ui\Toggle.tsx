import type { SwitchProps } from '@headlessui/react';
import { Switch } from '@headlessui/react';
import { cva } from 'class-variance-authority';
import React from 'react';

const toggle = cva(
  ' focus-visible:ring-white relative inline-flex h-4 w-7 shrink-0  rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none  focus-visible:ring-2 focus-visible:ring-opacity-75',
  {
    variants: {
      checked: {
        true: ['bg-blue-200'],
        false: ['bg-gray-200'],
      },
    },
    compoundVariants: [
      {
        checked: false,
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      checked: false,
    },
  },
);

const toggleElement = cva(
  'bg-white pointer-events-none inline-block h-3 w-3 rounded-full bg-white-100 shadow-lg ring-0 transition duration-200 ease-in-out',
  {
    variants: {
      checked: {
        true: ['translate-x-3'],
        false: ['translate-x-0'],
      },
    },
    compoundVariants: [
      {
        checked: false,
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      checked: false,
    },
  },
);

const containerDiv = cva(' flex items-center', {
  variants: {
    disabled: {
      true: ['pointer-events-none opacity-50 '],
      false: ['cursor-pointer opacity-100'],
    },
  },
  compoundVariants: [
    {
      disabled: false,
    },
  ],
  defaultVariants: {
    disabled: false,
  },
});

export interface ToggleProps extends SwitchProps<any> {}

export const Toggle: React.FC<ToggleProps> = ({
  className,
  checked,
  onChange,
  disabled,
  label,
  ...props
}) => {
  return (
    <div className={containerDiv({ className, disabled })}>
      <Switch
        checked={checked}
        onChange={onChange}
        className={toggle({ checked, className })}
        {...props}
      >
        <span className={toggleElement({ checked, className })} />
      </Switch>
      {label ? <span className="ml-2 ">{label}</span> : null}
    </div>
  );
};
