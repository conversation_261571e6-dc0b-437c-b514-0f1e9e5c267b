/* eslint-disable jsx-a11y/anchor-is-valid */

import moment from 'moment';
import { useRouter } from 'next/navigation';
import dataQualityTables from 'public/testdata/dataQuality/reportFreshness.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import SimpleTable from '@/components/ui/SimpleTable';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

const ReportFreshness: React.FC<{ selectedYear?: any }> = ({
  selectedYear,
}) => {
  const [selectedYearForView, setSelectedYearForView] =
    useState<any>(selectedYear);
  const [tableData, setTableData] = useState<any>(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  const formatReportData = (data: any) => {
    return data.map((report: any) => {
      return {
        Report: {
          data: report.reportName,
          id: report.id,
        },
        'Last Updated date': {
          data: moment(report.lastRefresh).format('YYYY-MM-DD'),
        },
        Status: {
          data: report.status,
          isLink: !!(
            report.status === 'Pending' || report.status === 'pending'
          ),
        },
      };
    });
  };

  const sortDataBySortId = (data: any) => {
    return data.sort((a: any, b: any) => a.sortId - b.sortId);
  };

  const fetchReportFreshnessTableData = () => {
    try {
      const url = `
      ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getReportFreshnessData.url}`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const header: any = [
              {
                data: 'Report',
              },
              {
                data: 'Last Updated date',
                hasSorting: true,
              },
              {
                data: 'Status',
              },
            ];
            const tempTableData = {
              headers: header,
              rows: formatReportData(sortDataBySortId(res.data)),
            };
            setTableData(tempTableData);
          }
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    setSelectedYearForView(selectedYear);
  }, [selectedYear]);

  useEffect(() => {
    if (!isTourOpen) {
      fetchReportFreshnessTableData();
    } else {
      setTableData(dataQualityTables);
    }
  }, [selectedYearForView]);

  return (
    <div className="flex h-[calc(100vh-250px)] w-full flex-col space-y-3 overflow-auto px-2 py-4 xl:h-[calc(100vh-210px)]">
      <div className="w-full">
        <span className=" w-full text-left font-sans text-sm font-medium capitalize text-gray-500">
          Status as on Date
        </span>
      </div>
      <div className="flex size-full flex-row space-x-4">
        {tableData && (
          <div className="size-full">
            <SimpleTable
              yearSelected={selectedYear}
              isFreshnessTable
              hasPagination={tableData.rows.length > 10}
              headers={tableData.headers}
              tableRows={tableData.rows}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportFreshness;
