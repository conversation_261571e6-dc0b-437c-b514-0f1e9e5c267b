'use client';

/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */
import { Formik } from 'formik';
import React, { useState } from 'react';

import PageLoader from '../loader';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const CreatePassword: React.FC = () => {
  // States
  const [isLoading, setIsLoading] = useState(false);

  // Method
  function setLoader() {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }
  return (
    <div className="flex h-full w-full items-center justify-center">
      {!isLoading && (
        <div className="flex min-h-[30vh] w-[30vw] flex-col">
          <div className=" mb-10 flex flex-col space-y-1">
            <span className="font-sans text-6xl font-normal leading-[70.81px] text-gray-800">
              Create Password
            </span>
            <span className="font-sans text-base font-normal leading-5 text-gray-900">
              Create a strong password for your account
            </span>
          </div>
          <Formik
            initialValues={{ email: '', password: '', confirmPassword: '' }}
            validate={(values) => {
              const errors: any = {};
              if (!values.email) {
                errors.email = 'Required.';
              } else if (
                !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
              ) {
                errors.email = 'Invalid email address.';
              }
              if (!values.password) {
                errors.password = 'Required.';
              } else if (values.password.length < 8) {
                errors.password =
                  'Password must be at least 8 characters long.';
              } else if (
                !/(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,}/.test(
                  values.password,
                )
              ) {
                errors.password =
                  'Password must contain at least one uppercase letter, one lowercase letter, one special character, and one number, and be at least 8 characters long.';
              }

              if (!values.confirmPassword) {
                errors.confirmPassword = 'Required.';
              } else if (values.confirmPassword !== values.password) {
                errors.confirmPassword = 'Passwords do not match.';
              }
              return errors;
            }}
            onSubmit={(values, { setSubmitting }) => {
              setTimeout(() => {
                console.log(values);
                setLoader();
                // can show loader if wanted
                setSubmitting(false);
              }, 400);
            }}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              isSubmitting,
              handleSubmit,
            }) => (
              <form className="flex flex-col" onSubmit={handleSubmit}>
                <div className="mb-4 w-full">
                  <Input
                    label="Email ID"
                    name="email"
                    className="w-full"
                    placeholder="<EMAIL>"
                    type="email"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    value={values.email}
                    intent={
                      errors.email && touched.email ? 'hasError' : 'enabled'
                    }
                    error={errors.email && touched.email && errors.email}
                  />
                </div>
                <div className="mb-4 w-full">
                  <Input
                    className="w-full"
                    label="Password"
                    name="password"
                    placeholder="Password"
                    onChange={handleChange}
                    type="password"
                    onBlur={handleBlur}
                    value={values.password}
                    error={
                      errors.password && touched.password && errors.password
                    }
                    intent={
                      errors.password && touched.password
                        ? 'hasError'
                        : 'enabled'
                    }
                  />
                </div>
                <div className="mb-6 w-full">
                  <Input
                    className="w-full"
                    label="Confirm Password"
                    name="confirmPassword"
                    placeholder="Confirm Password"
                    onChange={handleChange}
                    type="password"
                    onBlur={handleBlur}
                    value={values.confirmPassword}
                    error={
                      errors.confirmPassword &&
                      touched.confirmPassword &&
                      errors.confirmPassword
                    }
                    intent={
                      errors.confirmPassword && touched.confirmPassword
                        ? 'hasError'
                        : 'enabled'
                    }
                  />
                </div>
                <Button
                  intent="primary"
                  disabled={
                    isSubmitting ||
                    Object.keys(errors).length > 0 ||
                    !values.email ||
                    !values.password ||
                    values.password !== values.confirmPassword
                  }
                >
                  Get Started
                </Button>
              </form>
            )}
          </Formik>
        </div>
      )}

      {isLoading && (
        <PageLoader
          isLoading={isLoading}
          message="Creating Account and setting up AstRai"
        />
      )}
    </div>
  );
};
export default CreatePassword;
