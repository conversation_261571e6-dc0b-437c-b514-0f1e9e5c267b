import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const input = cva(
  ' rounded border-[1px] bg-white-200 px-3 py-4 font-sans text-sm font-normal leading-4  ',
  {
    variants: {
      intent: {
        enabled: [
          'border-lightgray-100',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'text-blueGray-300',
          'placeholder:text-gray-400',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:border-purple-200',
          'focus:outline-none',
        ],
        disabled: [
          'border-lightgray-100 ',
          'pointer-events-none',
          'opacity-50',
          'text-gray-400',
          'placeholder:text-gray-400',
        ],
        hasError: [
          'border-red-200',
          'focus:outline-none',
          'text-blueGray-300',
          'placeholder:text-gray-400',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'focus:border-purple-200',
        ],
      },
    },
    compoundVariants: [
      {
        intent: 'enabled',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'enabled',
    },
  },
);

const containerDiv = cva(' flex flex-col', {
  variants: {
    intent: {
      enabled: [],
      disabled: ['opacity-50', 'text-gray-400'],
      hasError: [],
    },
  },
  compoundVariants: [
    {
      intent: 'enabled',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'enabled',
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof input> {
  error?: any;
  label?: string;
}

export const Input: React.FC<InputProps> = ({
  className,
  content,
  placeholder,
  intent,
  name,
  type,
  label,
  onChange,
  error,
  ...props
}) => (
  <div className={containerDiv({ className, intent })}>
    {label && (
      <div className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
        {label}
      </div>
    )}
    <input
      type={type}
      placeholder={placeholder}
      name={name}
      value={content}
      onChange={onChange}
      className={input({ intent, className })}
      {...props}
    />
    {error && (
      <div className="pt-1 font-sans text-xs font-normal leading-4 text-red-200">
        {error}
      </div>
    )}{' '}
  </div>
);
