/* eslint-disable func-names */

'use client';

/* eslint-disable tailwindcss/no-custom-classname */
import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';

import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Button } from '../ui/Button';
import { Input } from '../ui/Input';

const Login: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const localService = new LocalService();
  
  // Always use GenPro context - BSM APIs are deactivated
  const isGenProContext = true;

  // Methods

  function parseJwt(token: any) {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map(function (c) {
          return `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`;
        })
        .join(''),
    );
    return JSON.parse(jsonPayload);
  }

  const loginUserBSM = async (email: string, password: string) => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrlBsm + ApiUtilities.apiPath.login.url;
    const params = {
      username: email,
      password,
    };
    apiService
      .unauthorizedPostRequest(url, params)
      .then((res) => {
        localService.setItem('id_token', res.data.data.id_token);
        localService.setItem('refresh_token', res.data.data.refresh_token);
        localService.setItem('access_token', res.data.data.access_token);
        localService.setItem('login_time', new Date().getTime().toString());
        localService.setItem('expires_in', res.data.data.expires_in.toString());
        const userType: any = parseJwt(res.data.data.access_token)[
          'cognito:groups'
        ]
          ? parseJwt(res.data.data.access_token)['cognito:groups'][0]
          : '';
        localService.setItem('user_type', userType);
        const user = parseJwt(res.data.data.id_token);
        localService.setItem('userName', user['cognito:username']);
        localService.setItem('name', user.name);
        localService.setItem('userEmail', user.email);
        router.push('/home');
        dispatch(setIsLoading(false));
      })
      .catch(() => {
        dispatch(setIsLoading(false));
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Invalid Credentials',
            content: 'Please check your email and password and try again.',
          }),
        );
      });
  };

  const loginUserGenPro = async (email: string, password: string) => {
    dispatch(setIsLoading(true));
    const url = apiService.buildUrl(ApiUtilities.apiPath.genproLogin);
    const params = {
      email,
      password,
    };
    apiService
      .genproUnauthorizedPostRequest(url, params)
      .then((res) => {
        // GenPro returns access and refresh tokens directly
        localService.setItem('access_token', res.data.access);
        localService.setItem('refresh_token', res.data.refresh);
        localService.setItem('login_time', new Date().getTime().toString());
        
        // Store user information from GenPro response
        const user = res.data.user;
        localService.setItem('userName', user.username);
        localService.setItem('name', `${user.first_name} ${user.last_name}`);
        localService.setItem('userEmail', user.email);
        localService.setItem('user_id', user.id.toString());
        localService.setItem('is_staff', user.is_staff.toString());
        
        // Set id_token to the access token for compatibility
        localService.setItem('id_token', res.data.access);
        
        // Navigate to home dashboard
        router.push('/home');
        dispatch(setIsLoading(false));
      })
      .catch(() => {
        dispatch(setIsLoading(false));
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Invalid Credentials',
            content: 'Please check your email and password and try again.',
          }),
        );
      });
  };

  const loginUser = async (email: string, password: string) => {
    if (isGenProContext) {
      return loginUserGenPro(email, password);
    } else {
      return loginUserBSM(email, password);
    }
  };

  useEffect(() => {
    router.prefetch('/genpro/workflow');
  });

  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex min-h-[30vh] w-[50vw] flex-col lg:w-[30vw]">
        <div className="mb-4 flex flex-col space-y-1">
          <span className="font-sans text-6xl font-normal leading-[70.81px] text-gray-800">
            Login
          </span>
          <span className="font-sans text-base font-normal leading-5 text-gray-900">
            Enter your credential to access your account.
          </span>
        </div>
        {/* <div className="mt-4 flex flex-col space-y-3">
          <button
            type="button"
            className="flex h-[54px] w-[50vw] items-center justify-center rounded-md border-[1px] border-lightgray-50 hover:border-purple-200 focus:border-purple-200 focus:shadow-blue lg:w-[30vw]"
          >
            <div className="flex flex-row items-center justify-center space-x-3">
              <img src="/assets/images/google.svg" alt="google" />

              <span className="font-sans text-base font-normal leading-5 text-gray-800">
                Login with Google
              </span>
            </div>
          </button>
          <button
            type="button"
            className="flex h-[54px] w-[50vw] items-center justify-center rounded-md border-[1px] border-lightgray-50 hover:border-purple-200 focus:border-purple-200 focus:shadow-blue lg:w-[30vw]"
          >
            <div className="flex flex-row items-center justify-center space-x-3">
              <img src="/assets/images/microsoft.svg" alt="microsoft" />
              <span className="font-sans text-base font-normal leading-5 text-gray-800">
                Login with Microsoft
              </span>
            </div>
          </button>
        </div>
        <div className="flex w-full flex-row justify-between py-8 ">
          <img src="/assets/images/line.svg" alt="line" />
          <span>Or</span>
          <img src="/assets/images/line.svg" alt="line" />
        </div> */}
        <Formik
          initialValues={{ email: '', password: '' }}
          validate={(values) => {
            const errors: any = {};
            if (!values.email) {
              errors.email = 'Required.';
            } else if (
              !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
            ) {
              errors.email = 'Invalid email address.';
            }
            if (!values.password) {
              errors.password = 'Required.';
            } else if (!isGenProContext) {
              // Apply strict validation only for BSM
              if (values.password.length < 8) {
                errors.password = 'Password must be at least 8 characters long.';
              } else if (
                !/(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,}/.test(
                  values.password,
                )
              ) {
                errors.password =
                  'Password must contain at least one uppercase letter, one lowercase letter, one special character, and one number, and be at least 8 characters long.';
              }
            }
            return errors;
          }}
          onSubmit={(values, { setSubmitting }) => {
            setSubmitting(true);
            loginUser(values.email, values.password);
            setSubmitting(false);
          }}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            isSubmitting,
            handleSubmit,
          }) => (
            <form className="flex flex-col" onSubmit={handleSubmit}>
              <div className="mb-4 w-full">
                <Input
                  label="Email ID"
                  name="email"
                  type="email"
                  className="w-full"
                  placeholder="<EMAIL>"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.email}
                  intent={
                    errors.email && touched.email ? 'hasError' : 'enabled'
                  }
                  error={errors.email && touched.email && errors.email}
                />
              </div>
              <div className="mb-6 w-full">
                <Input
                  className="w-full"
                  label="Password"
                  name="password"
                  type="password"
                  placeholder="Password"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.password}
                  error={errors.password && touched.password && errors.password}
                  intent={
                    errors.password && touched.password ? 'hasError' : 'enabled'
                  }
                />
              </div>
              <Button
                intent="primary"
                disabled={
                  isSubmitting ||
                  Object.keys(errors).length > 0 ||
                  !values.email ||
                  !values.password
                }
              >
                Login
              </Button>
            </form>
          )}
        </Formik>
        {/* <div className="flex w-full items-center justify-center py-6">
          <span className="font-sans text-base font-normal leading-5 text-gray-800">
            Don’t have an account?
            <Link className="px-1" href="register" content="Register here" />
          </span>
          <span className="font-sans text-base font-normal leading-5 text-gray-800">
            Forgot your password?
            <Link className="px-1" href="reset-pw" content="Reset Password" />
          </span>
        </div> */}
      </div>
      {/* <Loader isLoading={is} /> */}
    </div>
  );
};
export default Login;
