/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Link } from '@/components/ui/Link';
import SchemaJoinTable from '@/components/ui/SchemaJoinTable';
import { createMenuName } from '@/constants/menuSvg';

const JoinTables: React.FC<{
  mode: string | string[] | undefined;
  onRowChange?: any;
}> = (props) => {
  // Constants
  const header: any = ['Parent', 'Join', 'Child'];

  // Selectors
  const joinConfig = useSelector(
    (state: any) => state.logicalEntityCrud.joinConfig,
  );
  const activatedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.activatedAttributes,
  );

  // Methods
  const resolveMapper = (attributeToMap: any) => {
    return attributeToMap?.reduce((prevValue: any, currValue: any) => {
      prevValue.push({
        name: currValue,
      });
      return prevValue;
    }, []);
  };

  const getTablesFromDatsets = () => {
    const dataSets = Object.keys(activatedAttributes || {});
    const tables: any = [];
    dataSets.forEach((ds) => {
      const tableList = Object.keys(activatedAttributes[ds].tables);
      tableList.forEach((table) => {
        tables.push({
          name: table,
          dataset: ds,
        });
      });
    });
    return tables;
  };

  const resolveData = () => {
    const tableData: any = [];
    Object.keys(joinConfig || {}).forEach((row: any) => {
      const rowData: any = {
        components: [],
        dataset: joinConfig[row]?.dataset || '',
      };
      const parentTableList = getTablesFromDatsets();
      rowData.components.push({
        type: 'joinItem',
        data: [
          {
            value: joinConfig[row]?.parent_table,
            columnKey: 'parent_table',
            type: 'select',
            placeholder: 'Select Table',
            selectList: [...parentTableList],
          },
        ],
      });
      const parentAttributes = Object.keys(
        activatedAttributes?.[joinConfig[row]?.dataset]?.tables[
          joinConfig[row].parent_table
        ] || {},
      );
      const parentAttributesMapper = resolveMapper(parentAttributes);
      rowData.components[0]?.data.push({
        value: joinConfig[row]?.parent_attribute,
        columnKey: 'parent_attribute',
        type: 'select',
        placeholder: 'Select Attribute',
        selectList: [...parentAttributesMapper],
      });
      rowData.components.push({
        type: 'join',
        data: {
          value: joinConfig[row]?.join_type,
          type: 'select',
          placeholder: 'Select Join',
          selectList: [
            { name: 'Left Join' },
            { name: 'Right Join' },
            { name: 'Inner Join' },
          ],
        },
        input: joinConfig[row]?.join_condition,
      });
      const childTables = parentTableList.filter(
        (t: any) =>
          t.dataset === joinConfig[row].dataset &&
          t.name !== joinConfig[row].parent_table,
      );
      const childAttributes = Object.keys(
        activatedAttributes?.[joinConfig[row]?.dataset]?.tables[
          joinConfig[row].child_table
        ] || {},
      );
      const childAttributesMapper = resolveMapper(childAttributes);
      rowData.components.push({
        type: 'joinItem',
        data: [
          {
            value: joinConfig[row]?.child_table,
            columnKey: 'child_table',
            type: 'select',
            placeholder: 'Select Table',
            selectList: [...childTables],
          },
          {
            value: joinConfig[row]?.child_attribute,
            columnKey: 'child_attribute',
            type: 'select',
            placeholder: 'Select Attribute',
            selectList: [...childAttributesMapper],
          },
        ],
      });
      tableData.push(rowData);
    });
    return tableData;
  };
  // States
  const [data, setData]: any = useState(resolveData());

  // Methods
  const addRow = () => {
    const parentTableList = getTablesFromDatsets();

    const newRow = {
      id: `New Entry ${data.length + 1}`,
      components: [
        {
          type: 'joinItem',
          data: [
            {
              value: '',
              columnKey: 'parent_table',
              type: 'select',
              placeholder: 'Select Table',
              selectList: [...parentTableList],
            },
            {
              value: '',
              columnKey: 'parent_attribute',
              type: 'select',
              placeholder: 'Select Attribute',
              selectList: [],
            },
          ],
        },
        {
          type: 'join',
          data: {
            value: '',
            type: 'select',
            placeholder: 'Select Join',
            selectList: [
              { name: 'Right Join' },
              { name: 'Left Join' },
              { name: 'Inner Join' },
            ],
          },
          input: '',
        },
        {
          type: 'joinItem',
          data: [
            {
              value: '',
              columnKey: 'child_table',
              type: 'select',
              placeholder: 'Select Table',
              selectList: [],
            },
            {
              value: '',
              type: 'select',
              columnKey: 'child_attribute',
              placeholder: 'Select Attribute',
              selectList: [],
            },
          ],
        },
      ],
    };
    const updatedData = [...data, newRow];
    setData(updatedData);
  };

  // Effects
  useEffect(() => {
    const temp = resolveData();
    setData(temp);
  }, [activatedAttributes, joinConfig]);

  // useEffect(() => {
  //   setData(data);
  // }, [data]);

  if (!data) {
    return null;
  }
  return (
    <div className="flex h-[66vh] flex-col  overflow-auto">
      <div className="flex w-full flex-row items-center justify-between py-2">
        <span className="font-sans text-base font-semibold text-blueGray-300">
          Table Joins
        </span>
        {props.mode !== 'View' && (
          <Link content={createMenuName('Add Row')} onClick={addRow} />
        )}
      </div>
      <div className=" h-full w-full">
        <SchemaJoinTable
          isDashTable={false}
          header={header}
          data={data}
          isView={props.mode === 'View'}
          enabledCross={props.mode !== 'View'}
          enableOptions={false}
          onRowChange={props.onRowChange}
        />
      </div>
    </div>
  );
};

export default JoinTables;
