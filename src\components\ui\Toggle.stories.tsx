import type { Meta, StoryObj } from '@storybook/react';

import { Toggle } from './Toggle';

const meta = {
  title: 'UI/Toggle',
  component: Toggle,
  args: {
    checked: true,
    disabled: false,
  },
} satisfies Meta<typeof Toggle>;

export default meta;
type Story = StoryObj<typeof meta>;

const Checked: Story = {
  args: {
    checked: true,
    label: 'Switch',
  },
};
const UnChecked: Story = {
  args: {
    checked: false,
    label: 'Switch',
  },
};
const WithoutLabel: Story = {
  args: {
    checked: false,
  },
};

const Disabled: Story = {
  args: {
    disabled: true,
    label: 'Switch',
  },
};

export { Checked, Disabled, UnChecked, WithoutLabel };
