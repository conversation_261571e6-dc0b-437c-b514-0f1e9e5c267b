import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Link } from './Link';

const meta = {
  title: 'UI/Link',
  component: Link,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof Link>;

export default meta;
type Story = StoryObj<typeof meta>;

const Enabled: Story = {
  args: {
    content: 'Hyperlink',
    href: '#',
  },
};
const Disabled: Story = {
  args: {
    content: 'Hyperlink',
    href: '#',
    intent: 'disabled',
  },
};

export { Disabled, Enabled };
