/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable no-nested-ternary */
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/systemintegration/onlineConnection.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import { setAllSystemIntegrationTableDataFetched } from '@/slices/sourceSystemCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

interface SystemIntegrationTableProps {
  onOnlineConnectFilteredDataCountChange: (AllData: any) => void;
  scrollPosition?: any;
  allConnections?: any;
}

const SystemIntegrationTable: React.FC<SystemIntegrationTableProps> = ({
  scrollPosition,
  onOnlineConnectFilteredDataCountChange,
  allConnections = [],
}) => {
  // essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const mappedSourceSystems = useSelector(
    (state: RootState) => state.metadata.mappedSourceSystems,
  );
  const warningState = useSelector(
    (state: RootState) => state.metadata.warningAlert,
  );
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  // States
  const [searchItem, setSearchItem] = useState('');
  const [onlineConnections, setOnlineConnections] = useState<any>([]);
  const [filteredData, setFilteredData] = useState([]);
  const [RowID, setRowID] = useState(null);

  // Constants
  const header: any = [
    {
      title: 'Connection Name',
      optionsEnabled: false,
      options: ['sort'],
      inputType: 'checkbox',
    },
    {
      title: 'Data Source',
      optionsEnabled: true,
      options: ['sort'],
    },
    { title: 'Domain', optionsEnabled: true, options: ['sort'] },
    { title: 'Latest pull', optionsEnabled: true, options: ['sort'] },
    { title: 'Created by', optionsEnabled: true, options: ['sort'] },
    { title: 'Status', optionsEnabled: false, options: ['sort'] },
  ];
  const enableOptions: boolean = true;

  // Methods

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = onlineConnections.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(setAllSystemIntegrationTableDataFetched(false));
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Connection ${RowID} deleted successfully`,
              content: `Connection ${RowID} has been successfully deleted and all the items connected with the connection 01 has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };

  const menuData: any = [
    {
      option: 'View Log',
      clickFn: () => {
        router.push(`/systemIntegration/viewLog`);
      },
    },
    {
      option: 'View Connection Details',
      clickFn: (key: any) => {
        const parameter: any = {
          mode: 'View',
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/systemIntegration/addNewOnlineConnection?${queryString}`);
      },
    },
    {
      option: 'Edit Connection',
      clickFn: (key: any) => {
        const parameter: any = {
          mode: 'Edit',
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/systemIntegration/addNewOnlineConnection?${queryString}`);
      },
    },
    {
      option: 'Delete Connection',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete Entry ${key}`,
            message: `Are you sure? Do you want to delete the Entity ${key} Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete.',
            cancelButtonText: ' No, Discard',
          }),
        );
      },
    },
  ];

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  const resolveData = (result: any) => {
    return result.map((conn: any) => {
      const {
        id,
        isFavourite = false,
        connection_name,
        source_system_id,
        connection_details,
        created_at,
        created_by = '',
        is_enabled,
      } = conn;

      const row = {
        id,
        selected: false,
        isFavourite: isFavourite ?? false,
        components: [
          {
            value: connection_name,
            disabled: false,
            userAvatarLink: null,
            type: 'checkbox',
          },
          {
            value:
              mappedSourceSystems[source_system_id]?.source_system_name || '',
            disabled: false,
            userAvatarLink: null,
            type: 'text',
          },
          {
            value: connection_details.analytics
              .map((item: any) => item.name)
              .join(', '),
            userAvatarLink: null,
            disabled: false,
            type: 'text',
          },
          {
            value: new Date(created_at).toLocaleDateString('en-US'),
            userAvatarLink: null,
            disabled: false,
            type: 'text',
          },
          {
            value: created_by,
            userAvatarLink: null,
            disabled: false,
            type: 'avatar',
          },
          { value: is_enabled, disabled: false, type: 'toggle' },
        ],
      };

      return row;
    });
  };

  // Effects
  useEffect(() => {
    onOnlineConnectFilteredDataCountChange(String(filteredData.length));
  }, [filteredData, onOnlineConnectFilteredDataCountChange]);

  useEffect(() => {
    if (isTourOpen) {
      const formattedData: any = eData;
      setOnlineConnections(formattedData);
      setFilteredData(formattedData);
    } else {
      const formattedData = resolveData(
        allConnections.filter((resp: any) => resp.connection_type === 'erp'),
      );
      setOnlineConnections(formattedData);
      setFilteredData(formattedData);
    }
  }, [allConnections, isTourOpen]);

  return (
    <div className="flex h-full w-full flex-col py-4">
      <div className="w-[20vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search Connection"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="h-[70vh] w-full overflow-auto">
        <Table
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          enableOptions={enableOptions}
          scrollPosition={scrollPosition}
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>

      <WarningDialog
        onConfirm={
          warningState.actionbuttonText === 'Yes, make it Inactive'
            ? () => {
                console.log('do nothing');
              }
            : deleteItem
        }
      />
    </div>
  );
};

export default SystemIntegrationTable;
