/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Button } from '../../ui/Button';
import { OfflineERPSelector } from './systemIntegrationModals/OfflineERPSelector';

const AddNewOfflineConnection: React.FC = () => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Derivatives
  const mode: any = searchParams?.get('mode');

  // States
  const [selectedOption, setSelectedOption] = useState<any>(null);
  const [OffConnectionName, setOffConnectionName] = useState<string | null>(
    null,
  );
  const [selectedFile, setSelectedFile] = useState<any>(null);

  // Methods
  const goBack = () => {
    router.push('/systemIntegration');
  };

  const submitOfflineConnection = () => {
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOfflineConnections.url;
    // const options = ['local_upload', 'ftp', 'teams_folder'];
    const payload = {
      connection_name: OffConnectionName,
      file_name: selectedFile.name.replace(/\.[^/.]+$/, ''),
    };
    dispatch(setIsLoading(true));

    const getUploadUrl = `${
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url
    }/presigned_url?file_name=${selectedFile.name.replace(
      /\.[^/.]+$/,
      '',
    )}&file_format=csv`;

    const head = {
      'Content-Type': 'text/csv',
    };

    apiService.customGetRequest(getUploadUrl, head).then((resp) => {
      apiService
        .customPutRequest(resp.data.upload_url, selectedFile, head)
        .then(() => {
          apiService
            .postRequest(url, payload)
            .then((res) => {
              dispatch(setIsLoading(false));
              if (res.status === 200) {
                goBack();
                dispatch(
                  setToastAlert({
                    isToastOpen: true,
                    intent: 'success',
                    title: 'Connection Created successfully',
                    content: 'New Connection has been created successfully',
                  }),
                );
              } else {
                dispatch(setIsLoading(false));
                alert('something went wrong');
              }
            })
            .catch((err) => {
              console.log(err);
              dispatch(setIsLoading(false));
            });
        });
    });
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} Offline Connection{' '}
        </span>
      </div>
      <div className="flex h-[82vh] w-full flex-col  rounded border-[1px] border-lightgray-100 bg-white-200 p-4 py-2">
        <OfflineERPSelector
          selectedOption={selectedOption}
          setSelectedOption={setSelectedOption}
          OffConnectionName={OffConnectionName}
          setOffConnectionName={setOffConnectionName}
          selectedFile={selectedFile}
          setSelectedFile={setSelectedFile}
        />
      </div>
      <div className="flex h-[40px] w-full flex-row items-center justify-between">
        <Button
          className="flex h-[40px] items-center"
          type="button"
          onClick={goBack}
          intent="secondary"
        >
          Save as Draft
        </Button>

        <div className="flex flex-row space-x-4">
          {' '}
          <Button
            className="flex h-[40px] items-center"
            type="button"
            onClick={goBack}
            intent="secondary"
          >
            Cancel
          </Button>
          <Button
            className="flex h-[40px] items-center"
            type="button"
            disabled={!(selectedOption && OffConnectionName && selectedFile)}
            onClick={() => {
              submitOfflineConnection();
            }}
          >
            {mode !== 'Create' ? 'Update' : 'Create'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddNewOfflineConnection;
