import { useDispatch, useSelector } from 'react-redux';

import { Paragraph } from './components/ui/Paragraph';
import { setSelectedSourceSystem } from './slices/sourceSystemCrudSlice';
import type { RootState } from './store/store';

const logoMap: any = {
  'SAP ECC': 'SAP-ECC.png',
  'SAP S/4 HANA': 'SAP-S4.png',
  'Oracle Financials Cloud': 'ORACLE-FUSHION.png',
  'Oracle EBS': 'ORACLE-12.png',
  Salesforce: 'salesforce.png',
  'Microsoft Dynamics 365': 'Dynamics-365.png',
  Snowflake: 'snowflake.png',
  Redshift: 'redshift.png',
  'SQL Server': 'sqlserver.png',
  PostgreSQL: 'postgres.png',
  MySQL: 'placeholder-erp.png',
  BaaN: 'baan.png',
  JDE: 'jde.png',
  'SAP Ariba': 'ariba.png',
  NetSuite: 'netsuite.png',
};

export default function ERPList() {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const selectedSourceSystem = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedSourceSystem,
  );
  const sourceSystems = useSelector(
    (state: RootState) => state.metadata.sourceSystems,
  );
  const isEditing = useSelector(
    (state: RootState) => state.sourceSystemsCrud.isEditing,
  );

  // States
  // const [selectedOption, setSelectedOption] = useState<string | null>(null);

  // Methods
  const onSourceSystemSelection = (id: string) => {
    dispatch(setSelectedSourceSystem(id));
  };

  return (
    <div className=" lg:h-[calc(100vh-210px)] h-[calc(100vh-300px)]  overflow-auto p-6">
      <Paragraph content="Select Application" intent="p300" />
      <div className="grid grid-cols-4 gap-4 pt-4">
        {sourceSystems.map((sourceSystem: any) => (
          <button
            type="button"
            disabled={isEditing}
            key={sourceSystem.source_system_id}
            className={`flex flex-col items-center space-y-2 ${
              selectedSourceSystem === sourceSystem.source_system_id
                ? 'border-[2px] border-blue-200'
                : 'border'
            } rounded px-7 pt-12`}
            onClick={() => {
              onSourceSystemSelection(sourceSystem.source_system_id);
            }}
          >
            <div className="h-[50px] w-full max-w-[230px] lg:h-[55px] xl:h-[65px]">
              <img
                className="h-full w-full"
                src={`/assets/images/${
                  logoMap[sourceSystem.source_system_name]
                }`}
                alt={sourceSystem.source_system_name}
              />
            </div>
            <Paragraph
              content={sourceSystem.source_system_name}
              intent="p200"
              className="py-2"
            />
          </button>
        ))}
      </div>
    </div>
  );
}
