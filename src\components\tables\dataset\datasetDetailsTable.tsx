/* eslint-disable react/no-array-index-key */
/* eslint-disable import/no-cycle */
import React from 'react';

const DatasetDetailsTable: React.FC<any> = ({ data }) => {
  return (
    <div className="h-[72vh] w-[74.9vw] overflow-auto border-t-[1px] border-lightgray-100 bg-white-200 ">
      {' '}
      <table className="">
        <thead className="">
          <tr>
            {data.header.map((headerText: string, index: number) => (
              <th
                className="sticky top-0 z-10  min-w-[161px] border-[1px] border-t-[0px]  border-lightgray-100 bg-white-200 py-2 pl-6 text-start font-sans text-sm font-medium  uppercase text-gray-400"
                key={index}
              >
                {headerText}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.data.map((rowData: any, rowIndex: number) => (
            <tr key={rowIndex}>
              {rowData.map((cellData: any, cellIndex: number) => (
                <td
                  className=" min-w-[161px] border-[1px] border-lightgray-100 py-2 pl-6 font-sans text-sm font-semibold  capitalize text-gray-500"
                  key={cellIndex}
                >
                  {cellData}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DatasetDetailsTable;
