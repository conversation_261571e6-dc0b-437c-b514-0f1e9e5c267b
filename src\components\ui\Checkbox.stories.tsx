import type { Meta, StoryObj } from '@storybook/react';

import { Checkbox, CheckBoxStates } from './Checkbox';

const meta = {
  title: 'UI/Checkbox',
  component: Checkbox,
  args: {
    value: CheckBoxStates.Checked,
    disabled: false,
    labelText: 'Label',
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

const UnSelected: Story = {
  args: {
    value: CheckBoxStates.Empty,
  },
};
const Selected: Story = {
  args: {
    value: CheckBoxStates.Checked,
  },
};
const Disabled: Story = {
  args: {
    value: CheckBoxStates.Checked,
    disabled: true,
  },
};
const Indeterminate: Story = {
  args: {
    value: CheckBoxStates.Indeterminate,
  },
};

export { Disabled, Indeterminate, Selected, UnSelected };
