'use client';

import React from 'react';

import Table from '@/components/ui/Table';

const LogsTable = () => {
  const header = [
    { title: 'ID', align: 'left' },
    { title: 'Message', align: 'left' },
    { title: 'Timestamp', align: 'left' },
  ];

  const data = [
    {
      components: [
        { type: 'text', value: '1' },
        { type: 'text', value: 'File uploaded successfully' },
        { type: 'text', value: '2025-07-01 10:00:00' },
      ],
    },
    {
      components: [
        { type: 'text', value: '2' },
        { type: 'text', value: 'Validation complete' },
        { type: 'text', value: '2025-07-01 10:05:00' },
      ],
    },
    {
      components: [
        { type: 'text', value: '3' },
        { type: 'text', value: 'Transformation complete' },
        { type: 'text', value: '2025-07-01 10:10:00' },
      ],
    },
  ];

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Logs</h1>
      <Table header={header} data={data} isDashTable={false} enableOptions={false} />
    </div>
  );
};

export default LogsTable;
