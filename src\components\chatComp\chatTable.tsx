/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/anchor-is-valid */

'use client';

import { useState } from 'react';

import { downloadMenuTitle } from '@/constants/menuSvg';
import { convertToCSV, formatSimpleTableData } from '@/utils/utilityHelper';

import { Link } from '../ui/Link';
import SimpleTable from '../ui/SimpleTable';

const ChatTable: React.FC<{ tableData: any }> = ({ tableData }) => {
  const [tableDataForChat] = useState<any>(formatSimpleTableData(tableData));

  const handleDownload = () => {
    const csv = convertToCSV(tableDataForChat?.tableData);
    const csvData = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(csvData);
      link.setAttribute('href', url);
      link.setAttribute('download', 'table.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="chat-table flex w-full flex-col space-y-1 rounded bg-white-200 p-2">
      <div className="flex justify-end pl-2">
        <Link content={downloadMenuTitle} onClick={handleDownload} />
      </div>
      <div className="h-fit border-[.5px] border-lightgray-100 ">
        <SimpleTable
          isAvailabilityTable
          isChatTable
          isReconTable
          hasPagination={tableDataForChat?.tableData?.rows.length > 10}
          headers={tableDataForChat?.tableData?.headers}
          tableRows={tableDataForChat?.tableData?.rows}
        />
      </div>
    </div>
  );
};

export default ChatTable;
