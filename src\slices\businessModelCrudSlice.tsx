/* eslint-disable no-param-reassign */
import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface BusinessModelCrudState {
  businessTableFetched: boolean;
  businessTable: any;
  entityAttributes: any;
  businessModelName: string;
  businessModelDescription: string;
  joinConfig: any;
  existingAttributes: any;
  customAttributes: any;
  aggregationTable: any;
  selectedBusinessSchema: any;
  aggregateTo: any;
  aggregateDataType: any;
  schedulingConfig: any;
}

const initialState: BusinessModelCrudState = {
  businessTableFetched: false,
  businessTable: [],
  entityAttributes: [],
  businessModelName: '',
  businessModelDescription: '',
  joinConfig: {},
  existingAttributes: {},
  customAttributes: [],
  aggregationTable: [],
  selectedBusinessSchema: {},
  aggregateTo: {},
  aggregateDataType: {},
  schedulingConfig: '',
};

export const businessModelCrudSlice = createSlice({
  name: 'businessModelCrud',
  initialState,
  reducers: {
    setBusinessTable: (state, action) => {
      state.businessTable = action.payload;
    },
    setBusinessTableFetched: (state, action) => {
      state.businessTableFetched = action.payload;
    },
    setEntityAttributes: (state, action) => {
      state.entityAttributes = action.payload;
    },
    setBusinessModelName: (state, action) => {
      state.businessModelName = action.payload;
    },
    setBusinessModelDescription: (state, action) => {
      state.businessModelDescription = action.payload;
    },
    setJoinConfig: (state, action) => {
      state.joinConfig = action.payload;
    },
    setExistingAttributes: (state, action) => {
      state.existingAttributes = action.payload;
    },
    setCustomAttributes: (state, action) => {
      state.customAttributes = action.payload;
    },
    setAggregationTable: (state, action) => {
      state.aggregationTable = action.payload;
    },
    setSelectedBusinessSchema: (state, action) => {
      state.selectedBusinessSchema = action.payload;
    },
    setAggregateTo: (state, action) => {
      state.aggregateTo = action.payload;
    },
    setAggregateDataType: (state, action) => {
      state.aggregateDataType = action.payload;
    },
    setSchedulingConfig: (state, action) => {
      state.schedulingConfig = action.payload;
    },
    resetState: () => {
      return initialState;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setEntityAttributes,
  setBusinessModelName,
  setBusinessModelDescription,
  setSelectedBusinessSchema,
  setJoinConfig,
  setExistingAttributes,
  setCustomAttributes,
  setAggregationTable,
  setSchedulingConfig,
  setAggregateTo,
  setBusinessTableFetched,
  setAggregateDataType,
  setBusinessTable,
  resetState,
} = businessModelCrudSlice.actions;

export default businessModelCrudSlice.reducer;
