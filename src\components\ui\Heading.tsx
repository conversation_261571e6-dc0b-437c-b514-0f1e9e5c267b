import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import React from 'react';

const heading = cva('font-sans', {
  variants: {
    intent: {
      h900: ['text-3xl font-semibold leading-10 text-blueGray-300'],
      h800: ['text-2xl font-semibold leading-8 text-blueGray-300'],
      h700: ['text-xl font-semibold leading-6 text-blueGray-300'],
      h600: ['text-lg font-semibold leading-6 text-blueGray-300'],
      h500: ['text-base font-semibold leading-8 text-blueGray-300'],
      h400: ['text-sm font-semibold leading-8 text-blueGray-300'],
      h300: ['text-xs font-semibold leading-4 text-blueGray-300'],
      h200: ['text-xs font-semibold uppercase leading-4 text-blueGray-400'],
      h100: ['text-xxxs font-semibold uppercase leading-4 text-blueGray-400'],
    },
  },
  compoundVariants: [
    {
      intent: 'h900',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'h900',
  },
});

export interface SpanProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof heading> {}

export const Heading: React.FC<SpanProps> = ({
  className,
  intent,
  content,
  ...props
}) => (
  <span {...props} className={heading({ intent, className })}>
    {content}
  </span>
);
