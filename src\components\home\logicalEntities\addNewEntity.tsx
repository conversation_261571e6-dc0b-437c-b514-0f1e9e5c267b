/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-prototype-builtins */

import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import eData from 'public/testdata/logicalEntities/editLogicalEntities.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import CustomAttribute from '@/components/tables/logicalEntities/customAttribute';
import JoinTables from '@/components/tables/logicalEntities/joinTables';
import { WarningDialog } from '@/components/ui/Dialog';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  appendMappedAttributes,
  resetState,
  setActivatedAttributes,
  setCustomAttributes,
  setJoinConfig,
  setLogicalEntityName,
  setMappedAttributes,
  setSelectedDatasets,
} from '@/slices/logicalEntityCrudSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import AttributeHarmonization from '../../tables/logicalEntities/attributeHarmonization';
import { Badge } from '../../ui/Badge';
import { Button } from '../../ui/Button';
import { Tabs } from '../../ui/Tabs';
import SelectData from './addLogicalEntitiesTabs/selectData';

const AddNewEntity: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const searchParams = useSearchParams();

  // Constants
  const result = {};

  // States
  const [completedTabs, setCompletedTabs] = useState([-1]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [logicalEntities, setLogicalEntities] = useState<any>(result);

  // Selectors
  const activatedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.activatedAttributes,
  );
  const customAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.customAttributes,
  );
  const mappedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.mappedAttributes,
  );
  const joinConfig = useSelector(
    (state: any) => state.logicalEntityCrud.joinConfig,
  );
  const logicalEntityName = useSelector(
    (state: any) => state.logicalEntityCrud.logicalEntityName,
  );

  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Derivatives
  const mode: any = searchParams?.get('mode');
  const id: any = searchParams?.get('id');

  // Methods
  const goBack = () => {
    dispatch(resetState());
    router.push('/logicalEntities');
  };

  const moveToNextTab = () => {
    dispatch(setIsWarningAlertOpen(false));
    setSelectedTab(2);
    setCompletedTabs([1, ...completedTabs]);
  };

  const handleLogicalNameChange = (e: any) => {
    dispatch(setLogicalEntityName(e.target.value));
    setLogicalEntities((prev: any) => ({
      ...prev,
      entity_name: e.target.value,
    }));
  };

  const handleDataSetChange = (sds: any) => {
    dispatch(setSelectedDatasets(sds));
    setLogicalEntities((prev: any) => {
      const updatedActivatedAttributes = { ...prev.activated_attributes };
      sds?.forEach((dataset: any) => {
        updatedActivatedAttributes[dataset.name] = {
          name: dataset.name,
          tables: {},
        };
      });
      return {
        ...prev,
        activated_attributes: { ...updatedActivatedAttributes },
      };
    });
  };

  const handleTableChange = (dataSet: any, table: any) => {
    setLogicalEntities((prev: any) => {
      return {
        ...prev,
        activated_attributes: {
          ...prev.activated_attributes,
          [dataSet]: {
            name: dataSet,
            tables: {
              ...(prev.activated_attributes?.[dataSet]?.tables || {}),
              [table]: {},
            },
          },
        },
      };
    });
  };

  const handleRowSelection = (dataSet: any, table: any, selectedRows: any) => {
    const storedActivatedAttributes = structuredClone(activatedAttributes);
    if (selectedRows?.length === 0) {
      delete storedActivatedAttributes[dataSet]?.tables?.[table];
    }
    if (!storedActivatedAttributes[dataSet]) {
      storedActivatedAttributes[dataSet] = { tables: {} };
    }
    storedActivatedAttributes[dataSet].tables[table] = selectedRows?.reduce(
      (prevValue: any, currValue: any) => {
        const selAttr = { ...prevValue };
        selAttr[currValue.id] = {
          datatype: currValue.components[1].value,
          alias: currValue.components[4].value,
          length: currValue.components[2].value,
        };
        return selAttr;
      },
      {},
    );
    dispatch(setActivatedAttributes(storedActivatedAttributes));
  };

  const handleAttributeHarmonisationRowChange = (rows: any) => {
    const tableData: any = {};
    const tableKeys: any = {};
    rows.forEach((row: any, rowIndex: any) => {
      row.components.forEach((component: any) => {
        if (
          ['table', 'attribute', 'harmonizedAttribute'].includes(
            component?.columnType,
          )
        ) {
          if (component?.columnKey) {
            if (component?.columnType === 'table') {
              tableKeys[component.columnKey] = component.columnValue;
              tableData[rowIndex] = {
                ...tableData[rowIndex],
                [component.columnKey]: {
                  [component.columnType]:
                    component?.value === 'None' ? null : component?.value,
                },
              };
            } else if (component?.columnType === 'attribute') {
              tableData[rowIndex] = {
                ...tableData[rowIndex],
                [component.columnKey]: {
                  ...tableData[rowIndex]?.[component.columnKey],
                  [component.columnType]:
                    component?.value === 'None' ? null : component?.value,
                  datatype: component?.selectedValue?.datatype,
                  length: component?.selectedValue?.length,
                  col: component?.selectedValue?.col,
                },
              };
            } else {
              tableData[rowIndex] = {
                ...tableData[rowIndex],
                [component.columnKey]: {
                  ...tableData[rowIndex]?.[component.columnKey],
                  [component.columnType]:
                    component?.value === 'None' ? null : component?.value,
                },
              };
            }
          } else {
            tableData[rowIndex] = {
              ...tableData[rowIndex],
              [component.columnType]: component?.value,
            };
          }
        }
      });
    });
    Object.keys(tableData || {}).forEach((row: any) => {
      const rowValue: any = tableData[row];
      Object.keys(rowValue || {})
        .filter((key) => key !== 'harmonizedAttribute')
        .forEach((dataSet: any) => {
          const tableValue: any = rowValue?.[dataSet]?.table;
          const attrValue = rowValue?.[dataSet]?.attribute;
          const attrKeys = Object.keys(
            activatedAttributes?.[dataSet]?.tables?.[tableValue] || {},
          );
          const attrOptions = attrKeys.reduce(
            (prevValue: any, currValue: any) => {
              if (
                activatedAttributes?.[dataSet]?.tables?.[tableValue]?.[
                  currValue
                ]?.alias
              ) {
                prevValue.push(
                  activatedAttributes?.[dataSet]?.tables?.[tableValue]?.[
                    currValue
                  ]?.alias,
                );
              } else {
                prevValue.push(currValue);
              }
              return prevValue;
            },
            [],
          );
          if (!attrOptions.includes(attrValue)) {
            tableData[row][dataSet].attribute = null;
          }
        });
    });
    dispatch(setMappedAttributes(tableData));
  };

  const handleCustomAttributeRowChange = (rows: any) => {
    const tableData: any = {};
    rows.forEach((row: any) => {
      const attributeName = row.id;
      row.components.slice(1).forEach((component: any) => {
        tableData[attributeName] = {
          ...tableData[attributeName],
          [component.columnKey]: component.value,
        };
      });
    });
    dispatch(setCustomAttributes(tableData));
  };

  const handleJoinTablesChange = (rows: any) => {
    const tableData: any = {};
    rows.forEach((row: any, rowIndex: any) => {
      row.components[0].data.forEach((component: any) => {
        if (
          row.editedComponentIndex === 0 &&
          row.editedCellIndex === 0 &&
          component.columnKey === 'parent_attribute'
        ) {
          component.value = '';
        }
        tableData[rowIndex] = {
          ...tableData[rowIndex],
          [component.columnKey]: component.value,
        };
      });
      tableData[rowIndex] = {
        ...tableData[rowIndex],
        join_type: row.components[1].data.value,
        join_condition: row.components[1].input,
        dataset: row.dataset,
      };
      row.components[2].data.forEach((component: any) => {
        if (
          row.editedComponentIndex === 0 &&
          row.editedCellIndex === 0 &&
          (component.columnKey === 'child_attribute' ||
            component.columnKey === 'child_table')
        ) {
          component.value = '';
        } else if (
          row.editedComponentIndex === 2 &&
          row.editedCellIndex === 0 &&
          component.columnKey === 'child_attribute'
        ) {
          component.value = '';
        }
        tableData[rowIndex] = {
          ...tableData[rowIndex],
          [component.columnKey]: component.value,
        };
      });
    });
    dispatch(setJoinConfig(tableData));
  };

  const tabData: any = [
    {
      title: mode === 'View' ? 'Data Selected' : 'Select Data',
      completed: completedTabs.includes(0),
      // isForm: mode !== 'View',
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(0)
                ? 'success'
                : selectedTab === 0
                ? 'counter'
                : 'neutral'
            }
            content="1"
          />
        ),
      component: (
        <SelectData
          mode={mode}
          onLogicalEntityChange={handleLogicalNameChange}
          onDataSetChange={handleDataSetChange}
          onTableChange={handleTableChange}
          onRowSelection={handleRowSelection}
        />
      ),
    },
    {
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(1)
                ? 'success'
                : selectedTab === 1
                ? 'counter'
                : 'neutral'
            }
            content="2"
          />
        ),
      title: mode === 'View' ? 'Join Tables' : 'Join Tables',
      component: (
        <JoinTables mode={mode} onRowChange={handleJoinTablesChange} />
      ),
      completed: completedTabs.includes(1),
      // isForm: mode !== 'View',
    },
    {
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(2)
                ? 'success'
                : selectedTab === 2
                ? 'counter'
                : 'neutral'
            }
            content="3"
          />
        ),
      title:
        mode === 'View' ? 'Attribute Harmonization' : 'Attribute Harmonization',
      component: (
        <AttributeHarmonization
          mode={mode}
          onRowChange={handleAttributeHarmonisationRowChange}
        />
      ),
      completed: completedTabs.includes(2),
      // isForm: mode !== 'View',
    },
    {
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(3)
                ? 'success'
                : selectedTab === 3
                ? 'counter'
                : 'neutral'
            }
            content="4"
          />
        ),
      title: mode === 'View' ? 'Custom Attributes' : 'Custom Attributes',
      component: (
        <CustomAttribute
          mode={mode}
          onRowChange={handleCustomAttributeRowChange}
          result={logicalEntities}
        />
      ),
      completed: completedTabs.includes(3),
      // isForm: mode !== 'View',
    },
  ];

  function LengthHarmonizedAttribute() {
    const ma = mappedAttributes;

    if (ma) {
      return Object.values(ma).filter((mapping: any) => {
        return mapping.harmonizedAttribute === undefined || '';
      }).length;
    }

    return 0;
  }

  function checkAllHaveHarmonizedAttribute() {
    const ma = mappedAttributes;

    if (ma) {
      return Object.values(ma).every((mapping: any) => {
        return mapping.harmonizedAttribute !== undefined || '';
      });
    }

    return undefined;
  }

  const handleCompleteTab = (currentTab: number) => {
    if (!completedTabs.includes(currentTab)) {
      if (currentTab === 1) {
        if (mode !== 'View' && !checkAllHaveHarmonizedAttribute()) {
          dispatch(
            setWarningAlert({
              isWarningOpen: true,
              headerTitle: `${LengthHarmonizedAttribute()} Attributes Pending`,
              message: `There are nearly ${LengthHarmonizedAttribute()} attributes currently selected in the dataset, awaiting mapping and Harmonisation. Are you certain you wish to proceed without addressing them?`,
              actionbuttonText: 'Proceed',
              cancelButtonText: 'Harmonise Them',
            }),
          );
          // setCompletedTabs([currentTab, ...completedTabs]);
          // setSelectedTab(currentTab + 1);
        } else {
          setCompletedTabs([currentTab, ...completedTabs]);
          setSelectedTab(currentTab + 1);
        }
      } else {
        setCompletedTabs([currentTab, ...completedTabs]);
      }
    }
    if (completedTabs.length >= 6) {
      goBack();
    }
    if (currentTab !== 1) {
      setSelectedTab(currentTab + 1);
    }
  };

  function convertCustomAttributesToArray() {
    let customAttributesArray;
    if (customAttributes) {
      customAttributesArray = Object.keys(customAttributes).map(
        (attributeName) => {
          const attribute = customAttributes[attributeName];
          return {
            attribute_name: attributeName,
            attribute_datatype: attribute.attribute_datatype,
            attribute_config: {
              description: attribute.description,
              formula: attribute.formula,
            },
          };
        },
      );
    }

    return customAttributesArray;
  }

  function convertMappedAttributesToArray() {
    let mappedAttributesArray;

    if (mappedAttributes) {
      mappedAttributesArray = Object.keys(mappedAttributes).map(
        (attributeName) => {
          const attribute = mappedAttributes[attributeName];
          return {
            attribute_name: attribute.harmonizedAttribute,
            attribute_datatype: 'VARCHAR(255)',
            attribute_config: structuredClone(attribute),
          };
        },
      );
    }
    return mappedAttributesArray;
  }

  const createLogicalEntity = () => {
    dispatch(setIsLoading(true));
    const customAttributesArray = convertCustomAttributesToArray();
    const mappedAttributesArray = convertMappedAttributesToArray();
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getLogicalEntities.url;
    const params = {
      entity_name: logicalEntityName,
      activated_attributes: activatedAttributes,
      custom_attributes: customAttributesArray,
      mapped_attributes: mappedAttributesArray,
      join_config: joinConfig,
    };
    // if (modalHeading === 'Create Connection') {
    apiService
      .postRequest(url, params)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          console.log('connection created');
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'Entity Created successfully',
              content: 'New entity has been created successfully',
            }),
          );
          goBack();
        } else {
          alert('something went wrong');
        }
        handleCompleteTab(selectedTab);
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
    // } else {
    //   apiService.putRequest(url, params).then((res) => {
    //     if (res.status === 200) {
    //       console.log('connection updated');
    //     } else {
    //       alert('something went wrong');
    //     }
    //     closeModal();
    //   });
    // }
  };

  function isAnyKeyPresent(obj: any) {
    if (!obj) {
      return false;
    }
    if (typeof obj === 'object') {
      const keys = Object.keys(obj);
      for (const key of keys) {
        if (obj[key]) {
          if (Object.keys(obj[key].tables).length > 0) {
            return true;
          }
        }
      }
    }
    return false;
  }

  function isAnyAttrPresent(obj: any) {
    if (!obj) {
      return false;
    }
    const ob = obj[0];
    let foundNonNullValues = false;

    if (ob) {
      Object?.keys(ob).forEach((prop) => {
        if (
          ob[prop] &&
          ob[prop].table !== null &&
          ob[prop].attribute !== null
        ) {
          foundNonNullValues = true;
        }
      });
      return foundNonNullValues;
    }

    return false;
  }

  const handelCancel = () => {
    goBack();
  };

  const arrayHasNonEmptyObject = (ob: any) => {
    if (Object.keys(ob).length > 0) {
      const hasNonEmptyProperty = Object.values(ob).some((obj: any) => {
        return Object.values(obj).every((value, index) => {
          if (index === 3) {
            return true;
          }
          return value !== null && value !== '';
        });
      });
      return hasNonEmptyProperty;
    }
    return false;
  };

  const handleSave = () => {};

  const mapCustomAttribute = (custAttributes: any) => {
    const outputObject: any = {};

    Object.keys(custAttributes).forEach((key: any) => {
      outputObject[key] = {
        ...custAttributes[key],
        ...custAttributes[key].attribute_config,
      };

      // Remove the "attribute_config" property
      delete outputObject[key].attribute_config;
    });
    return outputObject;
  };

  const updateStateMappedAttribute = (dummyObject: any) => {
    dispatch(appendMappedAttributes(dummyObject));
  };

  const checkAndSetHarmonization = (
    dataset: any,
    tablename: any,
    attribute: any,
    harmonizedName: any,
  ) => {
    let flagPreset: number = 0;
    Object.values(mappedAttributes).forEach((item: any) => {
      if (item.hasOwnProperty(dataset)) {
        if (Array.isArray(item[dataset])) {
          item[dataset].forEach((data: any) => {
            if (data.table === tablename && data.attribute === attribute) {
              flagPreset += 1;
            }
          });
        } else if (typeof item[dataset] === 'object') {
          if (
            item[dataset].table === tablename &&
            item[dataset].attribute === attribute
          ) {
            flagPreset += 1;
          }
        }
      }
    });
    if (flagPreset === 0) {
      const newObject: any = {};
      const newData: any = {};
      Object.keys(activatedAttributes).forEach((dataSet: any) => {
        if (dataSet === dataset) {
          newData[dataSet] = {
            table: tablename,
            attribute,
          };
        } else {
          newData[dataSet] = {
            table: null,
            attribute: null,
          };
        }
      });
      newData.harmonizedAttribute = harmonizedName;
      newObject[`harmonized ${Object.keys(mappedAttributes).length + 1}`] =
        newData;
      updateStateMappedAttribute(newObject);
    }
  };

  const formatHarmonizeData = () => {
    for (const key in activatedAttributes) {
      const { tables } = activatedAttributes[key];
      const attributeData = Object.entries(tables);
      for (const item of attributeData) {
        const tableName = item[0];
        const attributesArray: any = item[1];
        const attributes: any = Object.entries(attributesArray);
        for (const attribute of attributes) {
          checkAndSetHarmonization(
            key,
            tableName,
            attribute[1].alias,
            attribute[0],
          );
        }
      }
    }
  };

  // Effects
  useEffect(() => {}, [completedTabs]);
  const mapHarmonizedAttribute = (mapAttributes: any) => {
    return Object.keys(mapAttributes).reduce((acc: any, curr: any) => {
      acc[curr] = mapAttributes[curr].attribute_config;
      return acc;
    }, {});
  };

  useEffect(() => {
    if (!isTourOpen) {
      if (id) {
        dispatch(setIsLoading(true));
        const url = `${
          ApiUtilities.getApiServerUrl +
          ApiUtilities.apiPath.getLogicalEntities.url
        }/${id}`;
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              // Set all values
              dispatch(setLogicalEntityName(res.data[0].entity_name));
              dispatch(
                setActivatedAttributes(res.data[0].activated_attributes),
              );
              dispatch(
                setCustomAttributes(
                  mapCustomAttribute(res.data[0].custom_attributes),
                ),
              );
              dispatch(
                setMappedAttributes(
                  mapHarmonizedAttribute(res.data[0].mapped_attributes),
                ),
              );
              dispatch(setJoinConfig(res.data[0].join_config));
              if (mappedAttributes && activatedAttributes && mode === 'View') {
                formatHarmonizeData();
              }
            } else {
              // alert('something went wrong');
              console.log('something went wrong');
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      }
    } else {
      const apiData: any = eData;
      dispatch(setLogicalEntityName(apiData.data[0].entity_name));
      dispatch(setActivatedAttributes(apiData.data[0].activated_attributes));
      dispatch(
        setCustomAttributes(
          mapCustomAttribute(apiData.data[0].custom_attributes),
        ),
      );
      dispatch(
        setMappedAttributes(
          mapHarmonizedAttribute(apiData.data[0].mapped_attributes),
        ),
      );
      dispatch(setJoinConfig(apiData.data[0].join_config));
      if (mappedAttributes && activatedAttributes && mode === 'View') {
        formatHarmonizeData();
      }
    }
  }, [id]);

  return (
    <div className="view-logical-entities edit-logical-entities-table view-logical-entities-table flex w-full flex-col space-y-4 ">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} Logical Entity{' '}
          {logicalEntityName && mode !== 'Create' && (
            <span>- {logicalEntityName}</span>
          )}
        </span>
      </div>
      <div
        className={` flex ${
          mode !== 'View' ? 'h-[82vh]' : 'h-[89vh]'
        } w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4`}
      >
        {(mode === 'Create' ||
          (mode === 'Edit' && Object.keys(activatedAttributes).length > 0)) && (
          <Tabs
            data={tabData}
            iconPosition="FIRST"
            selectedIndex={selectedTab}
            onChange={(e: number) => {
              setSelectedTab(e);
            }}
          />
        )}
        {mode === 'View' && (
          <div className="flex h-full w-full flex-col space-y-1">
            {/* <span className="font-sans text-lg font-semibold leading-8 text-blueGray-300">
              Attribute Harmonization
            </span> */}
            <AttributeHarmonization
              mode={mode}
              onRowChange={handleAttributeHarmonisationRowChange}
            />
          </div>
        )}
      </div>
      {mode !== 'View' && (
        <div className="flex h-[40px] w-full flex-row items-center justify-between">
          <Button
            className="flex h-[40px] items-center"
            type="button"
            onClick={handleSave}
            intent="secondary"
          >
            Save as Draft
          </Button>
          <div className="flex flex-row space-x-4">
            {' '}
            <Button
              className="flex h-[40px] items-center"
              type="button"
              onClick={handelCancel}
              intent="secondary"
            >
              Cancel
            </Button>
            <Button
              className="flex h-[40px] items-center"
              type="button"
              disabled={
                selectedTab === 0
                  ? !(isAnyKeyPresent(activatedAttributes) && logicalEntityName)
                  : selectedTab === 1
                  ? !arrayHasNonEmptyObject(joinConfig)
                  : selectedTab === 2
                  ? !isAnyAttrPresent(mappedAttributes)
                  : selectedTab === 3
                  ? !customAttributes
                  : false
              }
              onClick={() => {
                if (selectedTab !== 3) {
                  handleCompleteTab(selectedTab);
                } else {
                  createLogicalEntity();
                }
              }}
            >
              {selectedTab === 3
                ? mode !== 'Create'
                  ? 'Update'
                  : 'Create'
                : 'Next'}
            </Button>
          </div>
          <WarningDialog onConfirm={moveToNextTab} />
        </div>
      )}
    </div>
  );
};

export default AddNewEntity;
