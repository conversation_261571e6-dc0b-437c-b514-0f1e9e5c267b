'use client';

import moment from 'moment';
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/businessViews/businessViews.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@/components/ui/Button';
import ListBoxTable from '@/components/ui/ListBoxTable';
import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface BusinessViewsTableProps {
  onBusinessViewCountChange: (AllData: any) => void;
}

const BusinessViewsTable: React.FC<BusinessViewsTableProps> = ({
  onBusinessViewCountChange,
}) => {
  // essentials
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // States
  const [filteredData, setFilteredData] = useState([]);
  const [allData, setAllData] = useState<any>([]);
  const filters: any = localService.getData('businessViewFilters') || '';
  // const [RowID, setRowID] = useState(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [selectedBusinessView, setSelectedBusinessView] = useState<any>(
    filters?.selectedBusinessView ? filters.selectedBusinessView : 'All',
  );
  const [businessViewList, setBusinessViewList] = useState<any>([]);

  // Constants
  const header: any = [
    {
      title: 'Logical Name',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Last Updated',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];

  const menuData: any = [
    {
      option: 'View Table',
      clickFn: (key: any) => {
        const selectedData: any = filteredData.filter(
          (item: any) => item.id === key,
        );
        const parameter: any = {
          physicalName: selectedData[0].components[0].physicalViewName,
          name: selectedData[0].components[0].value,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/businessViews/businessViewsTable?&${queryString}`);
      },
    },
  ];

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const applyFilter = (tableData: any) => {
    const tempFilter: any = {
      selectedBusinessView,
    };
    localStorage.setItem('businessViewFilters', JSON.stringify(tempFilter));
    if (tableData.length !== 0) {
      const tempSelectedBusinessView: any =
        selectedBusinessView === 'All' ? '' : selectedBusinessView;
      const filter: { [key: number]: any } = {};
      if (tempSelectedBusinessView !== '')
        filter[0] =
          tempSelectedBusinessView === ' ' ? '' : tempSelectedBusinessView;

      const tempFilteredData = tableData.filter((item: any) =>
        Object.entries(filter).every(
          ([key, value]) => item.components[key].value === value,
        ),
      );
      setFilteredData(tempFilteredData);
    } else {
      setFilteredData([]);
    }
  };

  const resetFilter = () => {
    const tempFilter: any = {
      selectedBusinessView: 'All',
    };
    localStorage.setItem('businessViewFilters', JSON.stringify(tempFilter));
    setSelectedBusinessView('All');
    setFilteredData(allData);
  };

  const resolveData = (data: any) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data.map((row) => {
      const components = [
        {
          value: row.logicalViewName,
          physicalViewName: row.physicalViewName,
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.lastRefresh
            ? moment(row.lastRefresh).format('YYYY-MM-DD')
            : 'N/A',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        },
      ];
      return {
        id: row.logicalViewName,
        components,
      };
    });
  };

  useEffect(() => {
    const fetchSource = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsm +
          ApiUtilities.apiPath.getBsmBusinessViews.url
        }`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const uniqueBusinessViews = Array.from(
                new Set(res.data.map((obj: any) => obj.logicalViewName)),
              ).map((logicalViewName) => ({ name: logicalViewName }));
              setBusinessViewList([{ name: 'All' }, ...uniqueBusinessViews]);
              const formattedData: any = resolveData(res.data);
              setAllData(formattedData);
              applyFilter(formattedData);
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (!isTourOpen) {
      fetchSource();
    } else {
      const formattedData: any = eData;
      setFilteredData(formattedData);
    }
  }, []);

  useEffect(() => {
    if (filteredData.length > 0) {
      setFilteredData(filteredData);
      onBusinessViewCountChange(String(filteredData.length));
    }
  }, [filteredData]);

  return (
    <div className=" dataset-table business-views-table flex size-full flex-col">
      <div className="flex w-full items-end justify-between px-2 py-3">
        <div className="flex flex-row space-x-4">
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Select Business View
            </span>
            <div className="mt-3 w-[25vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                isInTable
                hasValue
                items={businessViewList}
                placeholder="Select"
                selectedValue={selectedBusinessView}
                onSelectionChange={(selectedValue) => {
                  setSelectedBusinessView(selectedValue.name);
                }}
                name="dataset"
              />
            </div>
          </div>
        </div>
        <div className=" flex flex-row space-x-2">
          <Button
            intent="primary"
            disabled={selectedBusinessView === ''}
            onClick={() => applyFilter(allData)}
          >
            Apply
          </Button>
          <Button intent="secondary" onClick={resetFilter}>
            Reset
          </Button>
        </div>
      </div>
      <div className="size-full overflow-auto " onScroll={handleScroll}>
        <Table
          menuData={menuData}
          hasPagination
          isDashTable
          header={header}
          data={filteredData}
          scrollPosition={scrollPosition}
          enableOptions
        />
      </div>
    </div>
  );
};

export default BusinessViewsTable;
