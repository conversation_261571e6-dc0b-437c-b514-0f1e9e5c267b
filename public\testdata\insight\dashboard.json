{"enterprise": [{"name": "Performance", "bgcolor": "#85BDDE", "heading": "Persona-based near real-time contextual information to enable senior executives actions", "dashboards": [{"id": 1, "name": "Single Pane of Glass", "url": "https://app.powerbi.com/reportEmbed?reportId=50fa8600-f33a-4241-a345-168091f5abda&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 2, "name": "CFO Dashboard", "url": "https://app.powerbi.com/reportEmbed?reportId=50fa8600-f33a-4241-a345-168091f5abda&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 3, "name": "Metrics That Matter", "url": "https://app.powerbi.com/reportEmbed?reportId=8b3cf0d5-a219-4173-b688-f9f0c70a8b74&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 4, "name": "Product Insights"}, {"id": 5, "name": "Segment Insights"}, {"id": 6, "name": "Regional Insights"}]}, {"name": "Projections", "bgcolor": "#B06482", "heading": "AI and advanced statistical models based projections to enable corrective actions", "dashboards": [{"id": 1, "name": "P&L"}, {"id": 2, "name": "Operating Margin"}, {"id": 3, "name": "Cash Flow"}, {"id": 4, "name": "Product Sales"}, {"id": 5, "name": "Customer Sales"}, {"id": 6, "name": "Price Realization"}]}, {"name": "Liquidity", "bgcolor": "#fec756", "heading": "Manage cash and inflow-outflow, leverage and financial risk", "dashboards": [{"id": 1, "name": "Cash Conversion Cycle"}, {"id": 2, "name": "Acid Test"}, {"id": 3, "name": "Cash Quality Index"}, {"id": 4, "name": "Collection Effectiveness"}, {"id": 5, "name": "Pay Term Rationalization"}, {"id": 6, "name": "Liquidity Risk"}]}, {"name": "Initiatives", "bgcolor": "#82CDA8", "heading": "Track progress and bottlnecks on enterprise performance improvement initiatives", "dashboards": [{"id": 1, "name": "Improvement Spend"}, {"id": 2, "name": "Benefit Realized Trend"}, {"id": 3, "name": "M&A Integration"}, {"id": 4, "name": "Overhead Initiatives"}, {"id": 5, "name": "Sales Initiatives"}, {"id": 6, "name": "ESG Initiatives"}]}], "customerFocus": [{"name": "Working Capital", "bgcolor": "#85BDDE", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Daily Sales Outstanding (DSO)", "url": "https://app.powerbi.com/reportEmbed?reportId=edff1449-47d1-4b81-a39c-8dfd5be8a338&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 2, "name": "Open Receivables", "url": "https://app.powerbi.com/reportEmbed?reportId=df71019b-9714-48d2-a7a2-49818da823c1&ap[…]1986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 3, "name": "Receivables Aging", "url": "https://app.powerbi.com/reportEmbed?reportId=c5077a23-8da6-4c7b-b8ad-5e666ed22ca6&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 4, "name": "Cash Flow Impact", "url": "https://app.powerbi.com/reportEmbed?reportId=c5077a23-8da6-4c7b-b8ad-5e666ed22ca6&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 6, "name": "Forecast Accuracy"}]}, {"name": "Payment Behavior", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Receipt Trend", "url": "https://app.powerbi.com/reportEmbed?reportId=8517cebf-83ce-439d-84b6-17f6a496926b&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 2, "name": "Collection Effectiveness", "url": "https://app.powerbi.com/reportEmbed?reportId=8517cebf-83ce-439d-84b6-17f6a496926b&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 3, "name": "Cash Flow Impact", "url": "https://app.powerbi.com/reportEmbed?reportId=8517cebf-83ce-439d-84b6-17f6a496926b&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 4, "name": "Overdue Analysis", "url": "https://app.powerbi.com/reportEmbed?reportId=8517cebf-83ce-439d-84b6-17f6a496926b&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 5, "name": "Receipt Forecasting", "url": "https://app.powerbi.com/reportEmbed?reportId=8517cebf-83ce-439d-84b6-17f6a496926b&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}, {"name": "Payment Term", "bgcolor": "#fec756", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Contracted vs Invoice Accuracy"}, {"id": 2, "name": "Wtd Avg Days to collect"}, {"id": 3, "name": "Cash Flow Impact"}, {"id": 4, "name": "Pay Term Changes"}, {"id": 5, "name": "Discount Analysis"}]}, {"name": "Revenue & Disputes", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Credit Vs Cash Sales"}, {"id": 2, "name": "Invoice vs Billed"}, {"id": 3, "name": "Dispute Trend"}, {"id": 4, "name": "Average Dispute Days"}, {"id": 5, "name": "Disputes by reason"}]}], "supplierFocus": [{"name": "Working Capital", "bgcolor": "#85BDDE", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Cash Flow Impact", "url": "https://app.powerbi.com/reportEmbed?reportId=c807f212-9a6b-46b5-8cf0-85fde9a1a829&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 2, "name": "Open Payables", "url": "https://app.powerbi.com/reportEmbed?reportId=c807f212-9a6b-46b5-8cf0-85fde9a1a829&appId=8e186025-0ca9-48cf-af02-c0c7ae91986c&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 3, "name": "Payables Aging"}, {"id": 4, "name": "Daily Purchase Outstanding (DPO)"}, {"id": 6, "name": "Forecast Accuracy"}]}, {"name": "Payment Behavior", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Payment Trend"}, {"id": 2, "name": "Payment Effectiveness"}, {"id": 3, "name": "Cash Flow Impact"}, {"id": 4, "name": "Ontime Vs Late Payments"}, {"id": 5, "name": "Payment Forecasting"}]}, {"name": "Payment Term", "bgcolor": "#fec756", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Contracted vs Invoice Accuracy"}, {"id": 2, "name": "Wtd Avg Days to Pay"}, {"id": 3, "name": "Cash Flow Impact"}, {"id": 4, "name": "Pay Term Changes"}, {"id": 5, "name": "Discount Loss"}]}, {"name": "Purchase & Disputes", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "COGS Trend"}, {"id": 2, "name": "Trend by Material"}, {"id": 3, "name": "Dispute Trend"}, {"id": 4, "name": "Average Dispute Days"}, {"id": 5, "name": "Disputes by reason"}]}], "financialFocus": [{"name": "<PERSON><PERSON>", "bgcolor": "#85BDDE", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Earnings (Gross, Net, Ops)", "url": "https://app.powerbi.com/reportEmbed?reportId=edff1449-47d1-4b81-a39c-8dfd5be8a338&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"id": 2, "name": "Returns (ROI, ROCE, ROA)"}, {"id": 3, "name": "Ratios (SG&A, Sales, Labor)"}, {"id": 4, "name": "Burn Rates (Gross, Ops, OH)"}, {"id": 5, "name": "NPI/ CAC"}]}, {"name": "Turnovers", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "<PERSON><PERSON>"}, {"id": 2, "name": "Inventory"}, {"id": 3, "name": "Receivables"}, {"id": 4, "name": "Payables"}, {"id": 5, "name": "Equity"}]}, {"name": "Tax", "bgcolor": "#fec756", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Tax Rates"}, {"id": 2, "name": "Tax Submissions"}, {"id": 3, "name": "Assessment Cycle Trend"}, {"id": 4, "name": "Penalties/Surcharges"}, {"id": 5, "name": "Arms Length/Transfer"}]}, {"name": "Actions", "bgcolor": "#B06482", "heading": "Manage the extracted data from various connections all together here and start ideating your thoughts", "dashboards": [{"id": 1, "name": "Cross Portfolio Leverage"}, {"id": 2, "name": "Forward/ Back Integration"}, {"id": 3, "name": "Ops Cash/Defensive"}, {"id": 4, "name": "Invest/ Cash outcome"}, {"id": 5, "name": "F&A Resource Trend"}]}]}