import '@/styles/global.scss';
import '../styles/fonts.scss';

import type { AppProps } from 'next/app';
// import NextNProgress from 'nextjs-progressbar';
import { Provider } from 'react-redux';

import { store } from '../store/store';

const MyApp = ({ Component, pageProps }: AppProps) => (
  <>
    {/* <NextNProgress /> */}
    <Provider store={store}>
      <Component {...pageProps} />
    </Provider>
  </>
);

export default MyApp;
