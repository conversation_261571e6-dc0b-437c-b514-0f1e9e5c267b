/* eslint-disable no-nested-ternary */

import ERPList from '../../../../ERPList';
import { Badge } from '../../../ui/Badge';
import { Tabs } from '../../../ui/Tabs';
import ConnectionDetailsForm from './onlineConnection/ConnectionDetailsForm';
import { DomainAndTables } from './onlineConnection/DomainAndTables';
import { ScheduleIntegration } from './onlineConnection/ScheduleIntegration';

export const OnlineERPSelector = ({
  selectedModalTab,
  setSelectedModalTab,
  completedTabs,
}: any) => {
  // Methods
  const getBadgeIntent = (
    activeCondition: boolean,
    successCondition: boolean,
  ) => {
    if (successCondition) return 'success';
    if (activeCondition) return 'counter';
    return 'neutral';
  };

  const newConnectionTabData: any = [
    {
      title: 'Select Application',
      icon: (
        <Badge
          intent={
            completedTabs?.includes(0)
              ? 'success'
              : selectedModalTab === 0
              ? 'counter'
              : 'neutral'
          }
          content="1"
        />
      ),
      component: <ERPList />,
      completed: completedTabs?.includes(0),
      // isForm: true,
    },
    {
      icon: (
        <Badge
          intent={
            completedTabs?.includes(1)
              ? 'success'
              : selectedModalTab === 1
              ? 'counter'
              : 'neutral'
          }
          content="2"
        />
      ),
      title: 'Configure Application',
      component: <ConnectionDetailsForm />,
      completed: completedTabs?.includes(1),
      // isForm: true,
    },
    {
      icon: (
        <Badge
          intent={
            completedTabs?.includes(2)
              ? 'success'
              : selectedModalTab === 2
              ? 'counter'
              : 'neutral'
          }
          content="3"
        />
      ),
      title: 'Domain and Tables',
      component: <DomainAndTables />,
      completed: completedTabs?.includes(2),
      // isForm: true,
    },
    {
      icon: (
        <Badge
          intent={getBadgeIntent(selectedModalTab === 3, selectedModalTab > 3)}
          content="4"
        />
      ),
      title: 'Schedule Integration',
      component: <ScheduleIntegration />,
      completed: selectedModalTab > 3,
      // isForm: true,
    },
  ];
  return (
    <div className="h-[calc(100vh-170px)] w-full rounded bg-white-200 p-2">
      <Tabs
        data={newConnectionTabData}
        selectedIndex={selectedModalTab}
        onChange={(e: number) => {
          setSelectedModalTab(e);
        }}
        iconPosition="FIRST"
      />
    </div>
  );
};
