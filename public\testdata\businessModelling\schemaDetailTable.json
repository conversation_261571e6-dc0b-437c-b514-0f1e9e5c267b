[{"id": "Parent 01", "components": [{"type": "joinItem", "data": [{"value": "", "columnKey": "parent_dataset", "type": "select", "placeholder": "select", "selectList": [{"name": "VENDOR_MASTER"}, {"name": "PAYMENT_TERMS"}, {"name": "ACCOUNTING_DOCUMENT_LINES"}, {"name": "ACCOUNTING_PAYMENT_DETAILS"}]}, {"value": "", "type": "select", "columnKey": "parent_table", "placeholder": "select", "selectList": [{"name": "PAYMENT_TERM"}, {"name": "VENDOR_CODE"}, {"name": "DOCUMENT_NUMBER"}, {"name": "DOC_LINE_ITEM"}]}]}, {"type": "join", "data": {"value": "", "type": "select", "placeholder": "select", "selectList": [{"name": "Left Join"}, {"name": "Right Join"}, {"name": "Inner Join"}]}, "input": "Where ID < 98"}, {"type": "joinItem", "data": [{"value": "", "columnKey": "child_dataset", "type": "select", "placeholder": "select", "selectList": [{"name": "VENDOR_MASTER"}, {"name": "PAYMENT_TERMS"}, {"name": "ACCOUNTING_DOCUMENT_LINES"}, {"name": "ACCOUNTING_PAYMENT_DETAILS"}]}, {"value": "", "type": "select", "columnKey": "child_table", "placeholder": "select", "selectList": [{"name": "PAYMENT_TERM"}, {"name": "VENDOR_CODE"}, {"name": "DOCUMENT_NUMBER"}, {"name": "DOC_LINE_ITEM"}]}]}]}, {"id": "Parent 02", "components": [{"type": "joinItem", "data": [{"value": "", "columnKey": "parent_dataset", "type": "select", "placeholder": "select", "selectList": [{"name": "VENDOR_MASTER"}, {"name": "PAYMENT_TERMS"}, {"name": "ACCOUNTING_DOCUMENT_LINES"}, {"name": "ACCOUNTING_PAYMENT_DETAILS"}]}, {"value": "", "columnKey": "parent_table", "type": "select", "placeholder": "select", "selectList": [{"name": "PAYMENT_TERM"}, {"name": "VENDOR_CODE"}, {"name": "DOCUMENT_NUMBER"}, {"name": "DOC_LINE_ITEM"}]}]}, {"type": "join", "data": {"value": "", "type": "select", "placeholder": "select", "selectList": [{"name": "Left Join"}, {"name": "Right Join"}, {"name": "Inner Join"}]}, "input": "Where ID < 98"}, {"type": "joinItem", "data": [{"value": "", "columnKey": "child_dataset", "type": "select", "placeholder": "select", "selectList": [{"name": "VENDOR_MASTER"}, {"name": "PAYMENT_TERMS"}, {"name": "ACCOUNTING_DOCUMENT_LINES"}, {"name": "ACCOUNTING_PAYMENT_DETAILS"}]}, {"value": "", "columnKey": "child_table", "type": "select", "placeholder": "select", "selectList": [{"name": "PAYMENT_TERM"}, {"name": "VENDOR_CODE"}, {"name": "DOCUMENT_NUMBER"}, {"name": "DOC_LINE_ITEM"}]}]}]}]