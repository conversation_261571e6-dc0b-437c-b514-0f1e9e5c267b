{"message": "Files uploaded but validation failed for one or more files", "validation_summary": {"total_files": 3, "valid_files": 2, "invalid_files": 1, "total_errors": 2, "total_warnings": 2, "all_files_valid": false}, "files": [{"id": 63, "file_type": "BF_OVERVIEW", "original_name": "April 2025 Brokerage Fee Overview.xlsx", "validation_status": "VALID", "validation_errors": [], "validation_warnings": [], "total_errors": 0, "total_warnings": 0}, {"id": 64, "file_type": "EXPENSE", "original_name": "EXPENSES-30.04.2025.xlsx", "validation_status": "VALID", "validation_errors": [], "validation_warnings": [], "total_errors": 0, "total_warnings": 0}, {"id": 65, "file_type": "PO", "original_name": "April 2025 Estimated Turnover_GenPro Report (with SCM Data) - Workings file with Raw Data.xlsx", "validation_status": "INVALID", "validation_errors": ["Vessel 'Sea Runner' has multiple different SMCs in PO file: BSM-hel, BSM-helo. A vessel cannot have multiple SMCs. Found in rows: 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 53, 54, 55. Please ensure each vessel has only one SMC throughout the PO file.", {"issue": "Vessel 'Rancid' does not exist in vessel entity mappings", "action_required": "Please add this vessel to the vessel entity mapping file first", "record_details": "Found in 1 records (rows: 62)", "fix_data": {"vessel_name": "<PERSON><PERSON><PERSON>", "smc_name": "BSM-IND", "smc_id": 10, "action": "create_vessel_mapping"}, "details": {"record_count": 1, "affected_rows": [62], "smc_in_po": "BSM-IND", "smc_id_in_po": 10}}], "validation_warnings": [{"issue": "Vessel 'Sea Runner' has different SMC in PO file vs vessel entity mapping (PO has 'BSM-helo', mapping has 'BSM-HEL')", "action_suggested": "Consider updating vessel entity mapping ID 180 to use SMC 'BSM-helo' if this is the correct SMC", "record_details": "Found in 1 records (rows: 8)", "fix_data": {"vessel_entity_mapping_id": 180, "current_smc_name": "BSM-HEL", "current_smc_id": 8, "suggested_smc_name": "BSM-helo", "suggested_smc_id": null, "action": "update_vessel_mapping_smc"}, "details": {"record_count": 1, "affected_rows": [8], "smc_in_po": "BSM-helo", "smc_id_in_po": null, "smc_in_mapping": "BSM-HEL", "smc_id_in_mapping": 8, "mapping_id": 180}}, {"type": "smc_mismatch_suggestion", "vessel_name": "Sea Runner", "issue": "Vessel 'Sea Runner' has different SMC in PO file vs vessel entity mapping", "action_suggested": "Consider updating vessel entity mapping ID 180 to use SMC 'BSM-helo' if this is the correct SMC", "details": {"record_count": 1, "affected_rows": [8], "smc_in_po": "BSM-helo", "smc_id_in_po": null, "smc_in_mapping": "BSM-HEL", "smc_id_in_mapping": 8, "mapping_id": 180}, "fix_data": {"vessel_entity_mapping_id": 180, "current_smc_name": "BSM-HEL", "current_smc_id": 8, "suggested_smc_name": "BSM-helo", "suggested_smc_id": null, "action": "update_vessel_mapping_smc"}}], "total_errors": 2, "total_warnings": 2}]}