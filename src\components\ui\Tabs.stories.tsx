import type { Meta, StoryObj } from '@storybook/react';

import { Badge } from './Badge';
import { Tabs } from './Tabs';

const meta = {
  title: 'UI/Tabs',
  component: Tabs,
  args: {
    data: [
      {
        title: 'Tab Name 01',
        component: 'Table 01 content',
        icon: '',
        iconPosition: 'FIRST',
        completed: true,
      },
      {
        title: 'Tab Name 02',
        component: 'Table 02 content',
        disabled: true,
        icon: '',
        iconPosition: 'FIRST',
        completed: true,
      },
      {
        title: 'Tab Name 03',
        component: 'Table 03 content',
        iconPosition: 'FIRST',
        completed: true,
        icon: <Badge intent="counter" content="25" />,
      },
    ],
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;

const Primary: Story = {
  args: {
    selectedIndex: 2,
  },
};

export { Primary };
