/* eslint-disable jsx-a11y/anchor-is-valid */

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/logicalEntities/customHierarchyEntry.json';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import UploadLogicalEntity from '@/components/home/<USER>/addLogicalEntitiesTabs/modal/uploadLogicalEntity';
import { Link } from '@/components/ui/Link';
import { Modal } from '@/components/ui/Modal';
import Table from '@/components/ui/Table';
import { createMenuNameForUpload } from '@/constants/menuSvg';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Searchbar } from '../../ui/Searchbar';

const CustomHierarchy: React.FC<{ mode: string | string[] | undefined }> = (
  props,
) => {
  // Constants
  const header: any = [
    {
      title: 'Hierarchy Setup NAme',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Orientation',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Description',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'File',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    { title: 'Last Update', optionsEnabled: true, options: ['sort'] },
    { title: 'Updated By', optionsEnabled: true, options: ['sort'] },
  ];

  // States
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [searchItem, setSearchItem] = useState('');
  const [data, setData]: any = useState<any>(
    props.mode !== 'Create' ? eData : [],
  );
  const [filteredData, setFilteredData] = useState(data);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [MappingEntityName, setMappingEntityName] = useState<any>(null);
  const [MappingDesc, setMappingDesc] = useState<any>(null);
  const [Orientation, setOrientation] = useState<any>(null);
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const uploadHierarchyEntry = () => {
    setIsPopupOpen(true);
  };

  const closePopupModal = () => {
    setIsPopupOpen(false);
  };

  const editHierarchyEntry = () => {
    setIsPopupOpen(true);
    console.log(setData);
  };

  // const addHierarchyData = () => {
  //   setIsPopupOpen(false);
  //   const newRow = {
  //     id: `Business Unit ${data.length + 1}`,
  //     selected: true,
  //     components: [
  //       {
  //         value: 'Business Unit',
  //         disabled: false,
  //         userAvatarLink: null,
  //         type: 'text',
  //       },
  //       {
  //         value: 'Vertical',
  //         disabled: false,
  //         userAvatarLink: null,
  //         type: 'text',
  //       },
  //       {
  //         value: 'Business Line',
  //         type: 'text',
  //         disabled: false,
  //         userAvatarLink: null,
  //       },
  //       {
  //         value: 'Entity.csv',
  //         disabled: false,
  //         userAvatarLink: null,
  //         type: 'file',
  //         link: 'njnjnjnj',
  //       },
  //       {
  //         value: '2023-08-10T18:25:43.511Z',
  //         userAvatarLink: null,
  //         disabled: false,
  //         type: 'text',
  //       },
  //       {
  //         value: 'Sonika Sharan',
  //         userAvatarLink: 'http:/',
  //         disabled: false,
  //         type: 'avatar',
  //       },
  //     ],
  //   };
  //   const updatedData = [newRow, ...data];
  //   setData(updatedData);
  //   setFilteredData(updatedData);
  // };

  const submitUploadHierarchy = () => {
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getLogicalEntities
    }/custom-hierarchies`;
    const payload = {
      file_name: selectedFile.name.replace(/\.[^/.]+$/, ''),
      file_format: 'csv',
      entity_name: MappingEntityName,
      desc: MappingDesc,
      orientation: Orientation,
    };
    dispatch(setIsLoading(true));

    const getUploadUrl = `${
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url
    }/presigned_url?file_name=${selectedFile.name.replace(
      /\.[^/.]+$/,
      '',
    )}&file_format=csv`;

    const head = {
      'Content-Type': 'text/csv',
    };
    apiService.customGetRequest(getUploadUrl, head).then((resp) => {
      apiService
        .customPutRequest(resp.data.upload_url, selectedFile, head)
        .then(() => {
          apiService
            .postRequest(url, payload)
            .then((res) => {
              dispatch(setIsLoading(false));
              if (res.status === 200) {
                dispatch(
                  setToastAlert({
                    isToastOpen: true,
                    intent: 'success',
                    title: 'File Uploaded successfully',
                    content: 'New File has been uploaded successfully',
                  }),
                );
              } else {
                alert('something went wrong');
              }
              // closeModal();
            })
            .catch((err) => {
              console.log(err);
              dispatch(setIsLoading(false));
            })
            .finally(() => setIsPopupOpen(false));
        });
    });
  };

  return (
    <div className="flex h-full w-full flex-col space-y-6 pt-6">
      <div className="flex w-full flex-row items-center justify-between">
        <div className="w-[40vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        {props.mode !== 'View' && (
          <Link
            content={createMenuNameForUpload('Upload Hierarchy Setup')}
            onClick={uploadHierarchyEntry}
          />
        )}
      </div>

      {data.length > 0 && (
        <div className="h-[68vh] w-full overflow-auto">
          <Table
            isCondensedTable
            isDashTable
            header={header}
            isEdit={props.mode !== 'View'}
            enabledCross={props.mode !== 'View'}
            data={filteredData}
            enableOptions={false}
            isEyeVisible
            EyeFunction={uploadHierarchyEntry}
            isView={props.mode === 'View'}
            onEdit={editHierarchyEntry}
          />
        </div>
      )}
      {data.length === 0 && (
        <div className="flex h-full w-full items-center  justify-center overflow-auto">
          <div className="w-[296px] text-center">
            <span className="font-sans text-sm font-medium text-gray-400">
              Click on Upload Hierarchy Setup to upload your organisation’s
              hierarchy setup
            </span>
          </div>
        </div>
      )}

      <Modal
        isActionButtonVisible
        actionbuttonText="Upload"
        isOpen={isPopupOpen}
        headerTitle="Upload Hierarchy Setup"
        component={
          <UploadLogicalEntity
            hierarchyModal
            setSelectedFile={setSelectedFile}
            selectedFile={selectedFile}
            MappingEntityName={MappingEntityName}
            setMappingEntityName={setMappingEntityName}
            MappingDesc={MappingDesc}
            setMappingDesc={setMappingDesc}
            setOrientation={setOrientation}
          />
        }
        closeModal={closePopupModal}
        disablePrimaryFooterBtn={
          !MappingEntityName || !selectedFile || !Orientation
        }
        footerPrimaryEventHandler={() => {
          // addHierarchyData();
          submitUploadHierarchy();
        }}
      />
    </div>
  );
};

export default CustomHierarchy;
