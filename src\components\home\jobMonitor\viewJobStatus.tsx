/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

import JobStatusTable from '@/components/tables/jobMonitor/jobStatusTable';

const ViewJobStatusComp: React.FC<{
  screen?: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const router = useRouter();
  console.log(props);
  // Methods
  const goBack = () => {
    router.push('/systemAdmin');
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Job Status
        </span>
      </div>
      <div className="flex h-[88vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <JobStatusTable />
      </div>
    </div>
  );
};

export default ViewJobStatusComp;
