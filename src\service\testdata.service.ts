/* eslint-disable class-methods-use-this */
import axios from 'axios';
import type { GetServerSideProps } from 'next';

export class TestDataService {
  data: any;

  path = '../../testdata/';

  getTestData = (async (url) => {
    const res = await fetch(`/testdata${url}.json`);
    const repo = await res.json();
    return { props: { repo } };
  }) satisfies GetServerSideProps<{}>;

  getAllApiData(fileName: string) {
    return axios.get<any>(`${this.path}${fileName}`);
  }

  async getAllTestDataFromFile(fileName: string) {
    try {
      const response = await fetch(`/testdata/${fileName}.json`);
      if (response.status === 404) {
        throw new Error('File not found');
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching data:', error);
      throw error;
    }
  }
}
