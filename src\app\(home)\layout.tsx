/* eslint-disable react/jsx-no-useless-fragment */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable @typescript-eslint/no-shadow */

'use client';

import { useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import AppLoader from '@/components/appLoader';
import Chats from '@/components/chatComp/chats';
import { Alert } from '@/components/ui/Alert';
import { ChatModal } from '@/components/ui/chatModal';
import { Notification } from '@/components/ui/Notification';
import Sidebar from '@/components/ui/Sidebar';
import { currentEnv } from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import ReduxProvider from '@/store/ReduxProvider';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface HomeLayoutProps {
  children: ReactNode;
}

const Home: React.FC<HomeLayoutProps> = ({ children }) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const [userName, setUserName] = useState('');

  // Selectors
  const isExpandedInitialValue = useSelector(
    (state: RootState) => state.sideBarExpand.value,
  );

  // const isChatbotVisible = useSelector(
  //   (state: RootState) => state.appConfig.isChatbotVisible,
  // );

  // States
  const [isChatbotVisible] = useState(false);
  const [isExpanded, setIsExpanded] = React.useState(isExpandedInitialValue);
  const [chatData, setChatData] = React.useState<any[]>([
    {
      name: 'Archimedes',
      type: 'text',
      message: `Hi ${userName}, I am Archimedes, your AI assistant. How can I help?`,
    },
  ]);
  const [isChatModalOpen, setIsChatModalOpen] = React.useState(false);
  const [isLoading, setisLoading] = React.useState('');

  const closePopupModal = () => {
    setIsChatModalOpen(false);
    setisLoading('');
    setChatData([
      {
        name: 'Archimedes',
        type: 'text',
        message: `Hi ${userName}, I am Archimedes, your AI assistant. How can I help?`,
      },
    ]);
  };

  const addItemToChat = (newItem: any) => {
    setChatData((prevChatData: any) => [...prevChatData, newItem]);
  };

  const formatData = (response: any) => {
    let chartData: any = null;
    if (response.graph_data && response.graph_data.data) {
      const { data } = response.graph_data;
      chartData =
        data.x.values.length !== 0 && data.y.values.length !== 0
          ? {
              options: {
                chart: {
                  id: 'basic-line',
                },
                dataLabels: {
                  enabled: false,
                },
                xaxis: {
                  name: data.x.name,
                  categories: data.x.values,
                  title: {
                    text: data.x.name,
                    style: {
                      fontSize: '10px',
                      fontWeight: 'normal',
                    },
                  },
                },
                yaxis: {
                  title: {
                    text: data.y.name,
                    style: {
                      fontSize: '10px',
                      fontWeight: 'normal',
                    },
                  },
                },
              },
              series: [
                {
                  name: data.y.name,
                  data: data.y.values,
                  title: {
                    text: data.y.name,
                  },
                },
              ],
            }
          : null;
    }
    const chatdata: any = {
      name: 'Archimedes',
      type: 'chart',
      message: response.paragraph,
      table: response.table ? response.table : null,
      chartData,
    };
    addItemToChat(chatdata);
  };

  const getResponse = (message: any) => {
    setisLoading('Typing....');
    const url = ApiUtilities.getChatApiServerUrl;
    const params = { message, env: currentEnv };
    apiService
      .postRequest(url, params)
      .then((res) => {
        if (res.status === 200) {
          setisLoading('');
          formatData(res.data);
        }
      })
      .catch((err) => {
        const tempErrorMessage = {
          name: 'Archimedes',
          type: 'text',
          message: `Dear ${userName}, I sincerely apologize, but I am currently unable to provide an answer to your question. Your understanding is greatly appreciated.`,
        };
        addItemToChat(tempErrorMessage);
        console.log(err);
        setisLoading('');
      });
  };

  const sendMessage = (message: any) => {
    const tempMessage: any = {
      name: userName,
      type: 'text',
      message,
    };
    addItemToChat(tempMessage);
    getResponse(message);
  };

  const sendFeedback = () => {
    const tempFeedBack: any = {
      name: userName,
      type: 'text',
      message: 'Feedback Sent.',
    };
    addItemToChat(tempFeedBack);
    const tempMessage: any = {
      name: 'Archimedes',
      type: 'text',
      message:
        'Thank you for taking the time to share your feedback, your insights are incredibly valuable to us.',
    };
    addItemToChat(tempMessage);
  };

  // Effects
  useEffect(() => {
    // getMetaDataForApp();
    const uName =
      localStorage.getItem('name') || localStorage.getItem('userName') || '';
    setUserName(uName);
  });

  const chatComponent = (
    <Chats
      chatData={chatData}
      isLoading={isLoading}
      onFeedback={sendFeedback}
    />
  );

  return (
    <ReduxProvider>
      <div className="flex h-screen w-screen flex-row overflow-x-auto">
        <Sidebar isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
        <main
          className={`${
            isExpanded
              ? 'w-[70vw] lg:w-[calc(100vw-256px)]'
              : ' w-[93vw] lg:w-[calc(100vw-60px)]'
          } h-fit min-h-full bg-lightgray-100/40 p-10 pb-2 pt-6`}
        >
          {children}
        </main>
        {isChatbotVisible && (
          <div className="absolute bottom-4 right-4 flex flex-row items-center justify-between">
            {/* <img
                src="/assets/images/close-circle.svg"
                onClick={() => {
                  dispatch(setIsChatbotVisible(false));
                }}
                className="cursor-pointer"
                alt="close"
              /> */}
            <div
              className="shadowAllCorners flex h-[35px] w-[140px] cursor-pointer items-center rounded-xl bg-white-200 px-4 py-2"
              onClick={() => {
                setIsChatModalOpen(true);
              }}
            >
              <span className=" cursor-pointer font-sans text-[12px] font-bold text-blue-200">
                Ask Archimedes
              </span>
            </div>
            <div
              onClick={() => {
                setIsChatModalOpen(true);
              }}
              className="shadowAllCorners -ml-5 size-[70px]  cursor-pointer rounded-full bg-white-200 p-2"
            >
              <div className=" flex size-full cursor-pointer items-center justify-center rounded-full bg-blue-200">
                <img
                  src="/assets/images/messages2.svg"
                  className=" cursor-pointer"
                  alt="message"
                />
              </div>
            </div>
          </div>
        )}

        <AppLoader />
        <ChatModal
          isOpen={isChatModalOpen}
          headerTitle="Ask Archimedes"
          component={chatComponent}
          sendMessage={sendMessage}
          removePannelBorder
          closeModal={closePopupModal}
          isActionButtonVisible
          actionbuttonText="Save"
        />
      </div>
      <Alert />
      <Notification />
    </ReduxProvider>
  );
};

export default Home;
