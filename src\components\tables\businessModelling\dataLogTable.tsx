/* eslint-disable import/no-cycle */
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/businessModelling/viewLog.json';
import React, { useState } from 'react';

import Table from '@/components/ui/Table';

import { Searchbar } from '../../ui/Searchbar';

const ViewLogTable: React.FC = () => {
  // Essentials
  const router = useRouter();
  // Constants
  const header: any = [
    {
      title: 'Run ID',
      optionsEnabled: false,
      inputType: 'text',
      options: [],
    },
    {
      title: 'Started ON',
      optionsEnabled: true,
      options: ['sort'],
      inputType: 'text',
    },
    {
      title: 'Ended ON',
      optionsEnabled: true,
      options: ['sort'],
      inputType: 'text',
    },
    {
      title: 'Duration',
      optionsEnabled: true,
      options: ['sort'],
      inputType: 'text',
    },
    {
      title: 'Resources Used',
      optionsEnabled: true,
      options: ['sort'],
      inputType: 'text',
    },
    {
      title: 'Status',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Log',
      optionsEnabled: false,
      options: [],
      inputType: 'link',
    },
  ];
  const data: any = {
    values: eData,
    enableOptions: false,
  };

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState(data.values);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.values.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const openLogModal = () => {
    router.push(`/businessModelling/viewLog`);
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex w-full flex-row items-center justify-between">
        <span className="font-sans text-base font-semibold text-blueGray-300">
          Log Runs
        </span>
        <div className="w-[40vw]">
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
      </div>
      <div className="my-4 h-[50vh] w-full overflow-auto bg-gray-300">
        <Table
          isCondensedTable
          isDashTable={false}
          header={header}
          data={filteredData}
          enableOptions={data.enableOptions}
          openTableModal={openLogModal}
        />
      </div>
    </div>
  );
};

export default ViewLogTable;
