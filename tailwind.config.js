/* eslint-disable global-require */
/* eslint-disable import/no-extraneous-dependencies */
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './app/**/*.{js,ts,jsx,tsx}',
  ],
  safelist: [
    // Ensure critical classes are never purged
    'rdg-light',
    'powerbi-grid',
    'filterContainerClassname',
    'text-left',
    'bg-green-100',
    'text-green-800',
    'border-green-300',
    'bg-gray-100',
    'text-gray-800',
    'border-gray-300',
    'bg-white',
    'text-green-600',
    'border-green-500',
    'text-gray-600',
    'border-gray-400',
    'rounded-full',
    'border-2',
  ],
  theme: {
    fontSize: {
      xxxs: '0.65rem',
      xxs: '0.69rem',
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '2.0rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.3rem',
      '7xl': '3.4rem',
    },
    extend: {
      backgroundImage: {
        headerLanding: "url('/assets/images/landingBg.svg')",
        'card-back':
          'linear-gradient(116.73deg, #074598 -2.41%, #072B5C 113.92%)',
        'auth-back': 'linear-gradient(180deg, #2F80ED 0%, #072D60 100%)',
        'table-back':
          'linear-gradient(0deg, #D0D0D0, #D0D0D0), linear-gradient(0deg, #FFFFFF, #FFFFFF)',
      },
      boxShadow: {
        blue: '0px 0px 0px 2px #D6E0FF',
        sidebar: '0px 12px 40px 0px #1018403D',
        list: '0px 4px 8px 0px #10184029',
      },
      fontFamily: {
        sans: ['OpenSans', 'sans'],
      },
      colors: {
        gray: {
          50: '#8F95B2',
          100: '#101840',
          200: '#D8DAE5',
          300: '#F0F0F0',
          400: '#7B7C7D',
          500: '#4F5152',
          600: '#545971',
          700: '#4F5159',
          800: '#333333',
          900: '#828282',
          1000: '#F2F5F8',
          2000: '#1C1C1C',
        },
        blue: {
          100: '#00AAE7',
          200: '#2F80ED',
          300: '#0F66DC',
          400: '#114E9F',
          500: 'rgb(231 0 0)',
        },
        blueGray: {
          100: '#696F8D',
          200: '#474D67',
          300: '#474D67',
          400: '#474D66',
          500: 'rgba(71, 77, 102, 0.64)',
        },
        lightblue: {
          100: '#2F80ED1A',
          200: '#2F80ED',
        },
        white: {
          100: '#FAFAFA',
          200: '#FFFFFF',
        },
        green: {
          100: '#1CA675',
          200: '#009C35',
        },
        yellow: {
          100: '#996A13',
          200: '#CA8C2E',
          300: '#FFB800',
        },
        red: {
          100: '#E5333E',
          200: '#D14343',
          300: '#EF4048',
          400: '#FF0B1A',
        },
        lightgray: {
          50: '#DDDDDD',
          100: '#D0D0D0',
          200: '#D6D6DC',
          300: '#5D606D',
          400: '#CDCED7',
          500: '#FAFAFA',
        },
        pink: {
          100: '#9747FF',
        },
        orange: {
          100: '#FF7347',
        },
        purple: {
          100: '#D6E0FF',
          200: '#9DB5FF',
          300: '#E7E4F9',
          400: '#524988',
          500: '#4759FF',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@headlessui/tailwindcss'),
  ],
};
