import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const iconLink = cva('fill-blue-200 transition duration-150 ease-in-out', {
  variants: {
    intent: {
      enabled: [
        'active:[&>*]:fill-blue-200',
        'hover:[&>*]:fill-blue-300',
        'focus:[&>*]:fill-blue-400',
      ],
      disabled: ['pointer-events-none', '[&>*]:fill-blue-200/50'],
    },
  },
  compoundVariants: [
    {
      intent: 'enabled',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'enabled',
  },
});

export interface LinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof iconLink> {}

export const IconLink: React.FC<LinkProps> = ({
  className,
  intent,
  children,
  href,
  ...props
}) => (
  <a href={href} className={iconLink({ intent, className })} {...props}>
    {children}
  </a>
);
