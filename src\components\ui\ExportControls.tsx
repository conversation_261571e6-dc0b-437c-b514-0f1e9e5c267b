'use client';

import React, { useState } from 'react';
import {
  Download,
  Mail,
  Eye,
  FileText,
  FileSpreadsheet,
  FileImage
} from 'lucide-react';

import { Button } from './Button';
import { Input } from './Input';
import { Toggle } from './Toggle';

export interface ExportFormat {
  value: string;
  label: string;
  icon: React.ComponentType<any>;
  description?: string;
}

export interface ExportOption {
  key: string;
  label: string;
  description?: string;
  icon?: React.ComponentType<any>;
}

export interface ExportControlsProps {
  formats: ExportFormat[];
  options?: ExportOption[];
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  optionValues?: Record<string, boolean>;
  onOptionChange?: (key: string, value: boolean) => void;
  onPreview?: () => void;
  onDownload?: () => void;
  onEmail?: (email: string) => void;
  disabled?: boolean;
  emailPlaceholder?: string;
  className?: string;
  showEmailInput?: boolean;
  showPreview?: boolean;
  customActions?: Array<{
    label: string;
    icon?: React.ComponentType<any>;
    onClick: () => void;
    intent?: 'primary' | 'secondary';
    disabled?: boolean;
  }>;
}

const ExportControls: React.FC<ExportControlsProps> = ({
  formats,
  options = [],
  selectedFormat,
  onFormatChange,
  optionValues = {},
  onOptionChange,
  onPreview,
  onDownload,
  onEmail,
  disabled = false,
  emailPlaceholder = 'Enter email for delivery',
  className = '',
  showEmailInput = true,
  showPreview = true,
  customActions = [],
}) => {
  const [email, setEmail] = useState('');

  const handleEmailSend = () => {
    if (email && onEmail) {
      onEmail(email);
      setEmail(''); // Clear email after sending
    }
  };

  const getDefaultIcon = (format: string) => {
    switch (format.toLowerCase()) {
      case 'excel':
      case 'xlsx':
        return FileSpreadsheet;
      case 'pdf':
        return FileImage;
      case 'csv':
      default:
        return FileText;
    }
  };

  return (
    <div className={`bg-white-200 rounded-lg border border-lightgray-100 p-6 ${className}`}>
      <h3 className="text-lg font-semibold mb-4">Export Options</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Format Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Export Format
          </label>
          <div className="space-y-2">
            {formats.map((format) => {
              const IconComponent = format.icon || getDefaultIcon(format.value);
              return (
                <label key={format.value} className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="format"
                    value={format.value}
                    checked={selectedFormat === format.value}
                    onChange={(e) => onFormatChange(e.target.value)}
                    disabled={disabled}
                    className="text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                  />
                  <IconComponent className="w-4 h-4 text-gray-500" />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">{format.label}</span>
                    {format.description && (
                      <span className="text-xs text-gray-500">{format.description}</span>
                    )}
                  </div>
                </label>
              );
            })}
          </div>
        </div>

        {/* Export Options */}
        {options.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Options
            </label>
            <div className="space-y-3">
              {options.map((option) => {
                const IconComponent = option.icon;
                return (
                  <div key={option.key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {IconComponent && <IconComponent className="w-4 h-4 text-gray-500" />}
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-700">{option.label}</span>
                        {option.description && (
                          <span className="text-xs text-gray-500">{option.description}</span>
                        )}
                      </div>
                    </div>
                    <Toggle
                      checked={optionValues[option.key] || false}
                      onChange={(value) => onOptionChange?.(option.key, value)}
                      disabled={disabled}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mt-6">
        <div className="flex flex-wrap gap-2">
          {showPreview && onPreview && (
            <Button 
              onClick={onPreview} 
              intent="secondary" 
              disabled={disabled}
              className="whitespace-nowrap flex justify-center items-center"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
          )}
          {onDownload && (
            <Button 
              onClick={onDownload} 
              disabled={disabled}
              className="whitespace-nowrap flex justify-center items-center"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          )}
          {customActions.map((action, index) => {
            const ActionIcon = action.icon;
            return (
              <Button
                key={index}
                onClick={action.onClick}
                intent={action.intent || 'secondary'}
                disabled={action.disabled || disabled}
                className="whitespace-nowrap flex justify-center items-center"
              >
                {ActionIcon && <ActionIcon className="w-4 h-4 mr-2" />}
                {action.label}
              </Button>
            );
          })}
        </div>
        
        {showEmailInput && onEmail && (
          <div className="flex items-center space-x-2">
            <Input
              type="email"
              placeholder={emailPlaceholder}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={disabled}
              className="w-64"
            />
            <Button 
              onClick={handleEmailSend} 
              disabled={disabled || !email}
              className="whitespace-nowrap flex justify-center items-center"
            >
              <Mail className="w-4 h-4 mr-2" />
              Send Email
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportControls;
