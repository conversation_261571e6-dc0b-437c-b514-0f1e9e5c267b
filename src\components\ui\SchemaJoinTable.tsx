/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable import/no-cycle */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */

import React, { useEffect, useState } from 'react';

import ListBox from './ListBox';
import { TableInput } from './TableInput';

const SchemaJoinTable: React.FC<{
  header: any[];
  data: any;
  isDashTable: boolean;
  enableOptions: boolean;
  menuData?: any;
  isEdit?: boolean;
  enabledCross?: boolean;
  hierarchyModal?: boolean;
  isView?: boolean;
  onRowChange?: any;
  scrollPosition?: any;
}> = ({
  header,
  enabledCross,
  data,
  isView,
  isEdit,
  onRowChange,
  scrollPosition,
}) => {
  // States
  const [rows, setRows] = useState(data);

  // Methods
  const removeRow = (rowIndex: number) => {
    const newData = [...rows];
    newData.splice(rowIndex, 1);
    setRows(newData);
    if (onRowChange) {
      onRowChange(newData);
    }
  };
  const handleRowInputStatusChange = (
    cellIndex: number,
    rowIndex: number,
    value: any,
  ) => {
    const newRows = rows.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.components[cellIndex].input = value;
      }

      return row;
    });
    if (onRowChange) {
      onRowChange(newRows);
    }
    setRows(newRows);
  };
  const handleSelectInputStatusChange = (
    rowIndex: number,
    componentIndex: number,
    cellIndex: number,
    value: any,
    dataset: any,
  ) => {
    const newRows = rows.map((row: any, index: any) => {
      if (index === rowIndex) {
        if (dataset) {
          row.dataset = dataset;
          row.editedComponentIndex = componentIndex;
          row.editedCellIndex = cellIndex;
        }
        row.components[componentIndex].data[cellIndex].value = value;
      }
      return row;
    });

    setRows(newRows);
    if (onRowChange) {
      onRowChange(newRows);
    }
  };

  const handleJoinSelectInputChange = (
    rowIndex: number,
    componentIndex: number,
    value: any,
  ) => {
    const newRows = rows.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.components[componentIndex].data.value = value;
      }
      return row;
    });
    setRows(newRows);
    if (onRowChange) {
      onRowChange(newRows);
    }
  };
  useEffect(() => {
    setRows(data);
  }, [data]);

  const renderValueCell = (
    cellData: any,
    index: number,
    rowIndex: number,
    rowData: any,
  ) => {
    return (
      <div
        key={index}
        className={`w-[32%] ${
          cellData.type === 'joinItem' ? '' : ' bg-transparent'
        }`}
      >
        <div style={{ pointerEvents: isView ? 'none' : 'auto' }}>
          {cellData.type === 'joinItem' && (
            <div className="flex w-[100%] flex-row items-center">
              {cellData.data.map((item: any, cellIndex: any) => (
                <div key={cellIndex} className="mx-[4px] h-[32px] w-[50%]">
                  {item.type === 'select' && (
                    <div>
                      <ListBox
                        isInTable
                        items={item.selectList}
                        selectedItems={[
                          { name: item.value, dataset: rowData.dataset },
                        ]}
                        scrollPosition={scrollPosition}
                        placeholder={item.placeholder}
                        isPreselected={false}
                        objectIdentifier="name"
                        onSelectionChange={(selectedValue) => {
                          handleSelectInputStatusChange(
                            rowIndex,
                            index,
                            cellIndex,
                            selectedValue?.name,
                            selectedValue?.dataset,
                          );
                        }}
                        name="select"
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          {cellData.type === 'join' && (
            <div className="flex w-[100%] flex-row items-center">
              <img
                className={` ${!isView || isEdit ? 'w-[10%]' : 'w-[15%]'}`}
                src="/assets/images/line2.svg"
                alt="line"
              />
              <div className={` ${!isView || isEdit ? 'w-[30%]' : 'w-[70%]'}`}>
                {cellData.data.type === 'select' && (
                  <div className="">
                    <ListBox
                      isInTable
                      refId={rowIndex}
                      scrollPosition={scrollPosition}
                      items={cellData.data.selectList}
                      placeholder={cellData.data.placeholder}
                      selectedItems={[{ name: cellData.data.value }]}
                      objectIdentifier="name"
                      isPreselected={false}
                      onSelectionChange={(selectedValue) => {
                        handleJoinSelectInputChange(
                          rowIndex,
                          index,
                          selectedValue?.name,
                        );
                      }}
                      name="select"
                    />
                  </div>
                )}
              </div>
              {(!isView || isEdit) && (
                <div className="ml-2 w-[50%]">
                  <TableInput
                    className="w-full"
                    name="Query"
                    type="text"
                    placeholder="Enter Query"
                    value={cellData.input}
                    onChange={(e) =>
                      !cellData.disabled &&
                      handleRowInputStatusChange(
                        index,
                        rowIndex,
                        e.target.value,
                      )
                    }
                  />
                  {/* <Input
                    className="w-full"
                    name="Query"
                    type="text"
                    placeholder="Enter Query"
                    value={cellData.input}
                    onChange={(e) =>
                      !cellData.disabled &&
                      handleRowInputStatusChange(
                        index,
                        rowIndex,
                        e.target.value,
                      )
                    }
                  /> */}
                </div>
              )}
              <img
                className={` ${!isView || isEdit ? 'w-[10%]' : 'w-[15%]'}`}
                src="/assets/images/line2.svg"
                alt="line"
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  // Effects
  useEffect(() => {
    setRows(data);
  }, [data]);

  return (
    <div className="flex flex-col">
      <div className="sticky top-0 z-10 flex w-full flex-row bg-gray-300 px-4 py-2">
        {header.map((rowData: any, rowIndex: any) => (
          <div
            className={`flex items-center justify-center ${
              rowData === 'Join' ? 'w-[32%]' : 'w-[32%]'
            }`}
            key={rowIndex}
          >
            <span className="font-sans text-sm font-normal uppercase leading-4 text-gray-400">
              {rowData}
            </span>
          </div>
        ))}
        <div className="flex w-[10%] items-center justify-center" />
      </div>
      {rows.length !== 0 && (
        <div className="">
          {rows.map((rowData: any, rowIndex: any) => (
            <div
              className="flex flex-row rounded border-b-[1px] border-lightgray-100 bg-white-200 px-4 py-1"
              key={rowIndex}
            >
              {rowData.components.map((cellData: any, cellIndex: number) =>
                renderValueCell(cellData, cellIndex, rowIndex, rowData),
              )}
              <div className="flex w-[4%] flex-row items-center justify-center">
                {' '}
                {enabledCross && (
                  <button
                    type="button"
                    className="h-[25px] w-[25px] cursor-pointer"
                    onClick={() => removeRow(rowIndex)}
                  >
                    <img src="/assets/images/redcross.svg" alt="clear" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      {rows.length === 0 && (
        <div className="flex h-full min-h-[30vh] w-full items-center justify-center">
          <span className="font-sans text-sm font-medium text-gray-400">
            No Data For Join
          </span>
        </div>
      )}
    </div>
  );
};
export default SchemaJoinTable;
