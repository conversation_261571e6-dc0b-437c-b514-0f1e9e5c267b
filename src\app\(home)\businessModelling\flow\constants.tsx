export const FLOW = {
  RAW: {
    id: 'raw',
    label: 'Raw Data',
    xPos: 100,
    yPos: 200,
    bgColor: 'rgb(255, 255, 255, 1)',
    minWidth: 130,
    minHeight: 370,
    zIndex: -2,
    classNames: 'main-grp',
    oracleBgColor: 'rgb(133, 189, 222, 0.2)',
    sapBgColor: 'rgb(254, 199, 86, 0.2)',
    oracleTableColor: '#FFFFFF',
    sapTableColor: '#FFFFFF',
    datasetMinWidth: 300,
    datasetMinHeight: 150,
    datasetZIndex: -1,
    initialSubGrpWidth: 10,
    subGroupBackground: 'rgba(47, 128, 237, 0.2)',
  },
  ENRICH: {
    id: 'enrich',
    label: 'Enriched Data',
    xPos: 1250,
    yPos: 200,
    bgColor: 'rgb(176, 100, 130, 0.2)',
    tableColor: '#FFFFFF',
    minWidth: 330,
    minHeight: 370,
    zIndex: -2,
    classNames: 'main-grp',
    datasetMinWidth: 100,
    datasetMinHeight: 80,
    datasetZIndex: 0,
    initialSubGrpWidth: 10,
  },
  CONSUME: {
    id: 'consume',
    label: 'Data Store',
    xPos: 2100,
    yPos: 200,
    bgColor: 'rgb(130, 205, 168, 0.2)',
    tableColor: '#FFFFFF',
    minWidth: 300,
    minHeight: 370,
    zIndex: -2,
    classNames: 'main-grp',
    datasetMinWidth: 130,
    datasetMinHeight: 80,
    datasetZIndex: 0,
    initialSubGrpWidth: 10,
  },
};
