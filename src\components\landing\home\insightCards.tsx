/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-danger */

'use client';

import { useRouter } from 'next/navigation';
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import eData from 'public/testdata/landing/insightCard.json';
import React from 'react';

const InsightCards: React.FC = () => {
  // States
  const router = useRouter();
  const [dashboards] = React.useState<any>(eData);
  // const dispatch = useDispatch();
  // const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Methods

  // const getBgColor = (score: any) => {
  //   if (score === 1) {
  //     return 'bg-red-400';
  //   }
  //   if (score === 2) {
  //     return 'bg-yellow-300';
  //   }
  //   if (score === 3) {
  //     return 'bg-green-200';
  //   }
  //   return '';
  // };

  const openDashboard = (url: string, name: string) => {
    if (name !== '' && url !== '') {
      const parameter: any = {
        name,
        url,
      };
      const queryString = new URLSearchParams(parameter).toString();
      router.push(`/insight?${queryString}`);
    }
  };

  // useEffect(() => {
  //   function getResolvedData() {
  //     const url =
  //       ApiUtilities.getApiServerUrlBsm + ApiUtilities.apiPath.getReports.url;
  //     const apiData = apiService.getRequest(url);
  //     apiData.then((resp) => {
  //       dispatch(setReports(resp.data));
  //     });
  //   }
  //   getResolvedData();
  // }, []);

  return (
    <div className="mr-6 grid h-fit grid-cols-3 pb-2 md:grid-cols-4 lg:grid-cols-5">
      {dashboards.map((cardData: any, index: any) => (
        <div
          className={` ${
            index === 0 ? 'insight-card' : ''
          } insight-enterprise-card col-span-1 mb-4 mr-4 h-fit  rounded-md bg-white-200`}
          key={index}
        >
          <div
            className="flex rounded-t-md border-b-[0.5px]  border-lightgray-100 bg-blue-200 py-2"
            // style={{
            //   backgroundColor: cardData.bgcolor,
            // }}
          >
            <span className="px-4 font-sans text-base font-semibold text-white-200">
              {cardData.name}
            </span>
          </div>
          <div className="flex w-full flex-col px-4 pb-4">
            <div className="flex h-[65%] flex-col space-y-1 overflow-auto pt-4">
              {cardData.dashboards.map((dashboardData: any, rowIndex: any) => (
                <div
                  key={rowIndex}
                  className="flex min-h-[44px] w-full flex-row items-center justify-between rounded-sm bg-slate-100 px-2"
                >
                  <button
                    onClick={() => {
                      openDashboard(dashboardData.url, dashboardData.name);
                    }}
                    type="button"
                    className="flex cursor-pointer text-left font-sans text-sm font-semibold  text-blue-200"
                  >
                    {dashboardData.name}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default InsightCards;
