'use client';

/* eslint-disable import/no-extraneous-dependencies */
import { useFormik } from 'formik';
import React, { useEffect } from 'react';
import * as Yup from 'yup';

import { Input } from '@/components/ui/Input';
import ListBox from '@/components/ui/ListBox';
import { Radio } from '@/components/ui/Radio';
import { TextField } from '@/components/ui/TextField';

const DynamicForm: React.FC<any> = ({
  fields,
  fieldData,
  isSubmit,
  onSubmit,
}) => {
  const formData: any = fields;
  // Sorting based on sort id
  formData.sort((a: any, b: any) => a.sort_id - b.sort_id);
  const initialValues: { [key: string]: string } = {};
  const validationSchema: { [key: string]: any } = {};
  const fieldValidations: any = {
    Textbox: (field: any) =>
      Yup.string().required(`${field.field_label} is required`),
    TextField: (field: any) =>
      Yup.string().required(`${field.field_label} is required`),
    Email: (field: any) =>
      Yup.string()
        .email('Invalid email address')
        .required(`${field.field_label} is required`),
    Radiobutton: (field: any) =>
      Yup.string().required(`${field.field_label} is required`),
    // RadioGroup: (field: any) =>
    //   Yup.string().required(`${field.field_label} is required`),
    Select: (field: any) =>
      Yup.array()
        .min(1, `${field.field_label} must have at least one selection`)
        .required(`${field.field_label} is required`),
    Password: (field: any) =>
      Yup.string()
        .matches(
          /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d).{8,}$/,
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and be at least 8 characters long',
        )
        .required(`${field.field_label} is required`),
  };

  formData.forEach((field: any) => {
    initialValues[field.field_name] = '';
    if (fieldValidations[field.sdc01_desc]) {
      validationSchema[field.field_name] =
        fieldValidations[field.sdc01_desc](field);
    }
  });

  const formik = useFormik({
    initialValues,
    validationSchema: Yup.object(validationSchema),
    onSubmit,
  });

  // Effects
  useEffect(() => {
    if (isSubmit) {
      formik.handleSubmit();
    }
  }, [isSubmit]);

  useEffect(() => {
    formik.setValues(fieldData);
  }, [fieldData]);

  return (
    <div className="flex h-full w-full flex-col  rounded-md bg-white-200 ">
      <div className=" flex h-fit w-full">
        <form
          className="flex h-full w-full flex-wrap"
          onSubmit={formik.handleSubmit}
        >
          {formData.map((field: any) => (
            <div
              key={field.field_name}
              className="ml-1 mr-4 flex h-[105px] min-w-[18%] max-w-[28%] "
            >
              {(field.sdc01_desc === 'textbox' ||
                field.sdc01_desc === 'Password' ||
                field.sdc01_desc === 'Email') && (
                <Input
                  label={field.field_label}
                  type={
                    field.field_name === 'password'
                      ? 'password'
                      : field.sdc01_desc
                  }
                  name={field.field_name}
                  id={field.field_name}
                  className="w-full "
                  placeholder={field.placeholder}
                  value={formik.values[field.field_name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  disabled={!field.is_active}
                  intent={
                    formik.touched[field.field_name] &&
                    formik.errors[field.field_name]
                      ? 'hasError'
                      : 'enabled'
                  }
                  error={
                    formik.touched[field.field_name] &&
                    formik.errors[field.field_name]
                  }
                />
              )}
              {field.sdc01_desc === 'multi-select-dropdown' && (
                <div className="flex h-full w-full flex-col space-y-1">
                  <span className="font-sans text-sm font-semibold text-gray-500">
                    {field.field_label}
                  </span>
                  <div className="">
                    <ListBox
                      items={field.list}
                      name={field.field_name}
                      multiselect={field.multiselect}
                      onSelectionChange={(selectedValue) => {
                        const array: any = [selectedValue.name];
                        formik.setFieldValue(field.field_name, array);
                      }}
                    />
                  </div>
                </div>
              )}
              {field.sdc01_desc === 'textarea' && (
                <TextField
                  label={field.field_label}
                  type={field.sdc01_desc}
                  name={field.field_name}
                  id={field.field_name}
                  className="h-[100px] w-[400px]"
                  placeholder={field.placeholder}
                  value={formik.values[field.field_name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  disabled={!field.is_active}
                  intent={
                    formik.touched[field.field_name] &&
                    formik.errors[field.field_name]
                      ? 'hasError'
                      : 'enabled'
                  }
                  error={
                    formik.touched[field.field_name] &&
                    formik.errors[field.field_name]
                  }
                />
              )}
              {/* {field.sdc01_desc === 'Radiobutton' && (
                <div className="w-[30%]">
                  <Radio
                    label={field.field_name}
                    name={field.field_name}
                    id={field.field_name}
                    onChange={formik.handleChange}
                    intent={field.is_active ? 'enabled' : 'disabled'}
                    error={
                      formik.touched[field.field_name] &&
                      formik.errors[field.field_name]
                    }
                  />
                </div>
              )} */}
              {field.sdc01_desc === 'radiobutton' && (
                <div
                  className="flex h-full w-[400px] flex-col space-y-1"
                  role="group"
                  aria-labelledby="radio-group-label"
                >
                  <span
                    id="radio-group-label"
                    className="font-sans text-sm font-semibold text-gray-500"
                  >
                    {field.label}
                  </span>

                  <div className="flex h-[50px] w-full flex-row items-center space-x-1">
                    <span className="font-sans text-sm font-semibold text-gray-500">
                      {field.field_label}
                    </span>
                    <div className="w-[100px] ">
                      <Radio
                        label="On"
                        name={field.field_name}
                        value={1}
                        checked={formik.values[field.field_name] === '1'}
                        id={field.field_name}
                        onChange={formik.handleChange}
                        intent={field.is_active ? 'enabled' : 'disabled'}
                      />
                    </div>
                    <div className="w-[100px]">
                      <Radio
                        label="Off"
                        name={field.field_name}
                        value={0}
                        checked={formik.values[field.field_name] === '0'}
                        id={field.field_name}
                        onChange={formik.handleChange}
                        intent={field.is_active ? 'enabled' : 'disabled'}
                      />
                    </div>
                  </div>

                  {/* <ErrorMessage
                    name={field.field_name}
                    component="div"
                    className="pt-1 font-sans text-xs font-normal leading-4 text-red-200"
                  /> */}
                </div>
              )}
            </div>
          ))}
          {/* <div className="relative mx-4 mt-8  flex w-full justify-end">
            <Button
              className="w-[150px]"
              intent="primary"
              type="submit"
              disabled={!formik.isValid || formik.isSubmitting}
            >
              Submit
            </Button>
          </div> */}
        </form>
      </div>
    </div>
  );
};

export default DynamicForm;
