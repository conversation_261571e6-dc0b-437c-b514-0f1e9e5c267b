/* eslint-disable unused-imports/no-unused-vars */
import type { TabProps } from '@headlessui/react';
import { Tab } from '@headlessui/react';
import { cva } from 'class-variance-authority';
import type { ReactNode } from 'react';
import React from 'react';

const tab = cva(
  '-mb-0.5 h-[44px] border-b-[1px] px-3 py-2 text-sm transition duration-200 ease-in-out',
  {
    variants: {
      selected: {
        true: [
          'text-blue-200',
          'font-medium',
          'border-blue-200',
          'border-b-[2px]',
          'focus:outline-none',
        ],
        false: ['text-gray-400', 'font-normal'],
      },
      completed: {
        true: ['text-green-100', 'font-normal'],
      },
      selectedAndCompleted: {
        true: [
          'text-green-100',
          'font-medium',
          'border-green-100',
          'border-b-[2px]',
          'focus:outline-none',
        ],
      },
      disabled: {
        true: ['opacity-50', 'font-normal'],
      },
    },
    compoundVariants: [
      {
        selected: false,
      },
    ],
    defaultVariants: {
      selected: false,
    },
  },
);

interface TabData {
  title: String;
  component: String | ReactNode;
  icon: String | ReactNode;
  disabled?: boolean;
  completed?: boolean;
  isForm?: boolean;
  iconPosition: 'FIRST' | 'LAST';
}
export interface TabsProps extends TabProps<any> {
  data: TabData[];
}

export const Tabs: React.FC<TabsProps> = ({
  className,
  data,
  selectedIndex,
  onChange,
  iconPosition = 'LAST',
}) => (
  <Tab.Group selectedIndex={selectedIndex} onChange={onChange}>
    <Tab.List className="border-b-[1px] border-lightgray-100">
      {data?.map(({ title, disabled, icon, completed }) => (
        <Tab
          key={`tab-title-${title}`}
          disabled={disabled}
          // style={{ pointerEvents: !completed && isForm ? 'none' : 'auto' }}
          className={({ selected }) =>
            tab({
              selected,
              className,
              disabled,
              completed,
              selectedAndCompleted: selected && completed,
            })
          }
        >
          {iconPosition === 'FIRST' ? icon : null}
          <span className={` ${iconPosition === 'FIRST' ? 'ml-2' : 'mr-2'} `}>
            {title}
          </span>
          {iconPosition === 'LAST' ? icon : null}
        </Tab>
      ))}
    </Tab.List>
    <Tab.Panels>
      {data?.map(({ title, component }) => (
        <Tab.Panel key={`tab-panel-${title}`}>{component}</Tab.Panel>
      ))}
    </Tab.Panels>
  </Tab.Group>
);
