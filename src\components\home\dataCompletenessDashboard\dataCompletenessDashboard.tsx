/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-danger */

'use client';

import dashboardData from 'public/testdata/dataCompletenessDashboardData/dataCompletenessDashboardData.json';
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useRef, useState } from 'react';

import DataCompletenessTable from '@/components/ui/completenessScoreTable';
import ListBoxTable from '@/components/ui/ListBoxTable';
import { yearList } from '@/constants/appConstants';
// import SelectData from './addLogicalEntitiesTabs/selectData';

const DataCompletenessDashboard: React.FC = () => {
  const [scrollPosition, setScrollPosition] = useState({ x: 0, y: 0 });
  const divRef = useRef<any>(null);
  const [selectedYear, setSelectedYear] = useState('2021');
  const [year] = useState<any>(yearList);
  const updateScrollPosition = () => {
    if (divRef.current) {
      const { scrollTop, scrollLeft } = divRef.current;
      setScrollPosition({ x: scrollLeft, y: scrollTop });
    }
  };
  const handleScroll = () => {
    updateScrollPosition();
  };

  useEffect(() => {
    updateScrollPosition();
    if (divRef.current) {
      divRef.current.addEventListener('scroll', handleScroll);
    }
    return () => {
      if (divRef.current) {
        divRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  return (
    <div className="flex h-full w-full flex-col space-y-4">
      <div className="flex w-fit flex-row items-center space-x-2">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Completeness Dashboard
        </span>
      </div>
      <div className="data-completeness-table  rounded border-[1px] border-lightgray-100 bg-white-200 p-6 ">
        <div
          className="flex h-[calc(100vh-150px)]  w-full flex-col space-y-4 overflow-auto"
          ref={divRef}
        >
          <div className="mb-2 flex flex-col space-y-1">
            <span className="font-sans text-sm font-medium uppercase text-gray-500">
              Year
            </span>
            <div className="mt-1 flex w-[15vw] flex-col space-y-2 rounded border-[1px] border-lightgray-100 py-1">
              <ListBoxTable
                placeholder="Select Year"
                isInTable
                items={year}
                selectedValue={selectedYear}
                hasValue
                onSelectionChange={(selectedValue) => {
                  setSelectedYear(selectedValue.name);
                }}
                name="select"
              />
            </div>
          </div>
          {dashboardData.map((data: any, index: number) => (
            <div className="flex w-full flex-col" key={data.id}>
              <span className="font-sans text-sm font-medium uppercase text-gray-500">
                {data.name} | Year {selectedYear}
              </span>
              {data.name !== 'Annual and Ad hoc Deck' ? (
                <DataCompletenessTable
                  scrollPosition={scrollPosition}
                  tableIndex={index}
                  headers={data.tableData.headers}
                  rows={data.tableData.rows}
                />
              ) : (
                <div className="w-full">
                  {data.tableData.headers.map(
                    (headerData: any, indexOfHeader: number) => (
                      <DataCompletenessTable
                        key={indexOfHeader}
                        scrollPosition={scrollPosition}
                        tableIndex={index}
                        headers={headerData}
                        rows={data.tableData.rows[indexOfHeader]}
                      />
                    ),
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DataCompletenessDashboard;
