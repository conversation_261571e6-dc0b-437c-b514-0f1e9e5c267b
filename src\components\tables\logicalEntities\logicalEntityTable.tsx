'use client';

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/logicalEntities/logicalEntityTable.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  resetState,
  setLogicalEntityTableFetched,
} from '@/slices/logicalEntityCrudSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

interface LogicalEntityTableProps {
  scrollPosition?: any;
  LogicalEntityData?: any;
}

const LogicalEntityTable: React.FC<LogicalEntityTableProps> = ({
  scrollPosition,
}) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const logicalEntityTableFetched = useSelector(
    (state: any) => state.logicalEntityCrud.logicalEntityTableFetched,
  );

  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Constants
  const header: any = [
    {
      title: 'Entity Name',
      optionsEnabled: true,
      options: ['sort'],
      inputType: 'checkbox',
    },
    {
      title: 'DATASETS',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'TOTAL ENTITIES',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'TOTAL ATTRIBUTES',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'STATUS',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];
  // const data: any = eData;
  const statusmapper: any = {
    RUNNING: 'success',
    PAUSE: 'error',
    DRAFT: 'warning',
  };

  // States
  const [searchItem, setSearchItem] = useState('');
  const [RowID, setRowID] = useState(null);
  const [filteredData, setFilteredData] = useState<any>();
  const [ResolvedEntity, setResolvedEntity] = useState<any>();

  // Methods
  const resolveComponent = (value: any, type: any, badges: any) => {
    const resolvedComponent: any = {
      value,
      disabled: false,
      userAvatarLink: null,
      type,
    };
    if (type === 'badges') {
      resolvedComponent.badges = [...badges];
    }
    return resolvedComponent;
  };

  const resolveData = (data: any) => {
    if (!data || !Array.isArray(data)) {
      return [];
    }

    return data.map((row) => {
      const dataSets = (row?.data_sets || []).map((currValue: any) => ({
        intent: 'counter',
        content: currValue,
      }));

      const components = [
        resolveComponent(row?.entity_name, 'checkbox', []),
        resolveComponent('', 'badges', dataSets),
        resolveComponent(row?.total_entities, 'text', []),
        resolveComponent(row?.total_attributes, 'text', []),
        resolveComponent('', 'badges', [
          { intent: statusmapper[row?.status], content: row?.status },
        ]),
      ];

      return {
        id: row?.entity_id,
        selected: false,
        isFavourite: row?.isFavourite ?? false,
        is_default: row.is_default,
        components,
      };
    });
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = ResolvedEntity?.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getLogicalEntities.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Entity ${RowID} deleted successfully`,
              content: `Entity ${RowID} has been successfully deleted and all the items connected with the Entity ${RowID} has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };

  const isDefaultPresentById = (id: number) => {
    const logicalEntry = filteredData.find((entry: any) => entry.id === id);
    return logicalEntry.is_default ? logicalEntry.is_default : false;
  };

  const menuData: any = [
    {
      option: 'Pause',
      disabled: true,
    },
    {
      option: 'View',
      clickFn: (key: any) => {
        const parameter: any = {
          mode: 'View',
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/logicalEntities/addNewEntity?${queryString}`);
      },
    },
    {
      option: 'Edit',
      clickFn: (key: any) => {
        if (isDefaultPresentById(key)) {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Cannot be Edited',
              content: 'This Logical Entity Cannot be Edited',
            }),
          );
        } else {
          const parameter: any = {
            mode: 'Edit',
            id: key,
          };
          const queryString = new URLSearchParams(parameter).toString();
          router.push(`/logicalEntities/addNewEntity?${queryString}`);
        }
      },
    },
    {
      option: 'Delete',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete Entry ${key}`,
            message: `Are you sure? Do you want to delete the Entity ${key}
        Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete',
          }),
        );
      },
    },
  ];

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    console.log('isFav', isFav);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  const getResolvedData = () => {
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getLogicalEntities.url;
    const apiData = apiService.getRequest(url);
    apiData.then((resp) => {
      setFilteredData(resolveData(resp.data));
      setResolvedEntity(resolveData(resp.data));
      dispatch(setLogicalEntityTableFetched(true));
    });
  };

  // Effects
  useEffect(() => {
    if (!isTourOpen) {
      if (!filteredData || !logicalEntityTableFetched) {
        getResolvedData();
      }
    }
  });

  useEffect(() => {
    if (!isTourOpen) {
      getResolvedData();
    } else {
      const data: any = eData;
      setFilteredData(resolveData(data));
      setResolvedEntity(resolveData(data));
      dispatch(setLogicalEntityTableFetched(true));
    }
  }, [isTourOpen]);

  if (!filteredData) {
    return <span>Loading....</span>;
  }

  return (
    <div className="flex h-full w-full flex-col py-4">
      <div className="w-[40vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="h-[70vh] w-full overflow-auto">
        <Table
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          scrollPosition={scrollPosition}
          enableOptions
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>

      <WarningDialog onConfirm={deleteItem} />
    </div>
  );
};

export default LogicalEntityTable;
