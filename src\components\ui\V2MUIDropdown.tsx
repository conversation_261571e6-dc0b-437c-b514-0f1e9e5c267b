import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import * as React from "react";

const V2MUIDropdown: React.FC<any> = ({
  menuData,
  menuIcon,
  currentData,
  forGenie,
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <div className="flex justify-center h-full">
      <Button
        disableRipple
        className="flex h-full w-full justify-center border cursor-pointer"
        sx={{
          all: "unset",
          "&:hover": {
            backgroundColor: "inherit",
            boxShadow: "none",
          },
        }}
        id="basic-butt
                on"
        aria-controls={open ? "basic-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        onClick={handleClick}
      >
        {menuIcon}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {menuData.map((item: any) => (
          <MenuItem
            key={item.key}
            onClick={() => {
              handleClose();
              if (item.clickFn) {
                if (forGenie) {
                  item.clickFn(item);
                } else {
                  item.clickFn(currentData);
                }
              }
            }}
            sx={{
              color: "#7B7C7D",
              "&:hover": {
                backgroundColor: "rgba(47, 128, 237, 0.1)",
                color: "#2F80ED",
              },
            }}
            className="hover:bg-blue-700 text-sm"
          >
            {item.option}
          </MenuItem>
          // </div>
        ))}
      </Menu>
    </div>
  );
};
export default V2MUIDropdown;
