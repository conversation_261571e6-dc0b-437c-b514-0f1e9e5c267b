import type { Meta, StoryObj } from '@storybook/react';

import { Button } from './Button';

const meta = {
  title: 'UI/Button',
  component: Button,
  args: {
    intent: 'primary',
    disabled: false,
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

const Primary: Story = {
  args: {
    intent: 'primary',
    children: 'Primary button',
  },
};

const Secondary: Story = {
  args: { intent: 'secondary', children: 'Secondary button' },
};

export { Primary, Secondary };
