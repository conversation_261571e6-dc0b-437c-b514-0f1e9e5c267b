/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/businessModelling/businessModelling.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  resetState,
  setBusinessTable,
  setBusinessTableFetched,
} from '@/slices/businessModelCrudSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

const BusinessModellingTable: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  // Constants
  const header: any = [
    {
      title: 'Schema Name',
      optionsEnabled: false,
      options: [],
      inputType: 'checkbox',
    },
    {
      title: 'Attributes',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Data Load Frequency',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Changed By',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Latest Data Load',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Status',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];
  const statusmapper: any = {
    RUNNING: 'success',
    PAUSE: 'error',
    DRAFT: 'warning',
  };

  // States
  const [RowID, setRowID] = useState(null);
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState<any>([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const businessModelTableFetched = useSelector(
    (state: any) => state.businessModelCrud.businessTableFetched,
  );
  const businessModelTable = useSelector(
    (state: any) => state.businessModelCrud.businessTable,
  );

  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Methods

  const isDefaultPresentById = (id: number) => {
    const businessEntry = filteredData.find((entry: any) => entry.id === id);
    return businessEntry.is_default ? businessEntry.is_default : false;
  };

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getBusinessModels.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Schema ${RowID} deleted successfully`,
              content: `Schema ${RowID} has been successfully deleted and all the items connected with the Schema ${RowID} has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };
  const menuData: any = [
    {
      option: 'Pause',
      disabled: true,
    },
    {
      option: 'View Data Log',
      clickFn: () => {
        router.push('/businessModelling/viewDataLog');
      },
    },
    {
      option: 'View Schema',
      clickFn: (key: any) => {
        const parameter: any = {
          mode: 'View',
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/businessModelling/addNewBusinessModel?${queryString}`);
      },
    },
    {
      option: 'View Data Lineage',
      clickFn: (key: any) => {
        const parameter: any = {
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/businessModelling/flow?${queryString}`);
      },
    },
    {
      option: 'Edit Schema',
      clickFn: (key: any) => {
        if (isDefaultPresentById(key)) {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Cannot be Edited',
              content: 'This Business Model Cannot be Edited',
            }),
          );
        } else {
          const parameter: any = {
            mode: 'Edit',
            id: key,
          };
          const queryString = new URLSearchParams(parameter).toString();
          router.push(`/businessModelling/addNewBusinessModel?${queryString}`);
        }
      },
    },
    {
      option: 'Delete',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete Schema ${key}`,
            message: `Are you sure? Do you want to delete the Schema ${key} Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete',
          }),
        );
      },
    },
  ];
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };
  const resolveComponent = (value: any, type: any, badges: any) => {
    const resolvedComponent: any = {
      value,
      disabled: false,
      userAvatarLink: null,
      type,
    };
    if (type === 'badges') {
      resolvedComponent.badges = [...badges];
    }
    return resolvedComponent;
  };

  const formatBusinessModelData = (result: any) => {
    return (
      result?.map((row: any) => {
        const {
          business_entity_id: id,
          business_entity_name: name,
          isFavourite,
          attribute_count,
          scheduling_config,
          updated_by,
          updated_at,
          status,
          is_default,
        } = row;

        const components = [
          resolveComponent(name, 'checkbox', []),
          resolveComponent(attribute_count, 'text', []),
          resolveComponent(scheduling_config || 'Daily', 'text', []),
          resolveComponent(updated_by, 'text', []),
          resolveComponent(updated_at, 'text', []),
          resolveComponent('', 'badges', [
            { intent: statusmapper[status], content: status },
          ]),
        ];

        return {
          id,
          name,
          selected: false,
          isFavourite: isFavourite || false,
          components,
          is_default,
        };
      }) || []
    );
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = formatBusinessModelData(businessModelTable).filter(
      (row: any) =>
        row.components.some((cell: any) => {
          if (typeof cell.value === 'string') {
            return cell.value.toLowerCase().includes(value.toLowerCase());
          }
          return false;
        }),
    );
    setFilteredData(filteredValues);
  };

  const getResolvedData = () => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getBusinessModels.url;
    const apiData = apiService.getRequest(url);
    apiData
      .then((resp) => {
        setFilteredData(formatBusinessModelData(resp.data));
        dispatch(setBusinessTable(resp.data));
        dispatch(setBusinessTableFetched(true));
      })
      .finally(() => dispatch(setIsLoading(false)));
  };

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  // Effects
  useEffect(() => {
    if (!isTourOpen) {
      if (!filteredData.length || !businessModelTableFetched) {
        getResolvedData();
      }
    }
  });

  useEffect(() => {
    if (isTourOpen) {
      const businessTableData = eData;
      setFilteredData(formatBusinessModelData(businessTableData));
      // dispatch(setBusinessTable(resp.data));
      dispatch(setBusinessTableFetched(true));
    } else {
      getResolvedData();
    }
  }, [isTourOpen]);

  return (
    <div className="business-modelling-table flex h-full min-h-[60vh] w-full flex-col">
      <div className="w-[40vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div
        className=" h-full min-h-[40vh] w-full overflow-auto"
        onScroll={handleScroll}
      >
        <Table
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          enableOptions
          scrollPosition={scrollPosition}
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>

      <WarningDialog onConfirm={deleteItem} />
    </div>
  );
};

export default BusinessModellingTable;
