[{"name": "Profitability Overview", "bgcolor": "#2F80ED", "heading": "Persona-based near real-time contextual information to enable senior executives actions", "dashboards": [{"completenessScore": 3, "id": 1, "name": "MDA Report", "url": "https://app.powerbi.com/reportEmbed?reportId=82c4b84d-93c6-47a7-ae07-ce75b8de2656&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 2, "name": "Business Unit wise Profitability", "url": "https://app.powerbi.com/reportEmbed?reportId=82c4b84d-93c6-47a7-ae07-ce75b8de2656&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 3, "name": "Owner wise Profitability", "url": "https://app.powerbi.com/reportEmbed?reportId=82c4b84d-93c6-47a7-ae07-ce75b8de2656&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 2, "name": "Performance Overview", "url": "https://app.powerbi.com/reportEmbed?reportId=82c4b84d-93c6-47a7-ae07-ce75b8de2656&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 3, "name": "Financial & Operational Metrics", "url": "https://app.powerbi.com/reportEmbed?reportId=82c4b84d-93c6-47a7-ae07-ce75b8de2656&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}, {"name": "Profitability Analysis", "bgcolor": "#2F80ED", "heading": "AI and advanced statistical models based projections to enable corrective actions", "dashboards": [{"completenessScore": 2, "id": 1, "name": "Profitability Analysis", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 2, "name": "Monthly Comparison", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 3, "name": "<PERSON><PERSON><PERSON>", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 1, "name": "FC1 vs FC2 vs FC3", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 2, "name": "Group Cost", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 3, "name": "Operating Income", "url": "https://app.powerbi.com/reportEmbed?reportId=4481e4a9-c082-46b9-921b-46c2c0994ce1&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}, {"name": "Employee Salary Metric", "bgcolor": "#2F80ED", "heading": "AI and advanced statistical models based projections to enable corrective actions", "dashboards": [{"completenessScore": 3, "id": 1, "name": "Employee Salary Metric", "url": "https://app.powerbi.com/reportEmbed?reportId=821f1397-3875-43cd-890d-5a8c94c04a07&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 2, "name": "<PERSON><PERSON>el Employee Ratios", "url": "https://app.powerbi.com/reportEmbed?reportId=821f1397-3875-43cd-890d-5a8c94c04a07&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 1, "id": 3, "name": "Salary & Headcount Forecast", "url": "https://app.powerbi.com/reportEmbed?reportId=821f1397-3875-43cd-890d-5a8c94c04a07&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}, {"name": "Fleet Revenue Analysis", "bgcolor": "#2F80ED", "heading": "AI and advanced statistical models based projections to enable corrective actions", "dashboards": [{"completenessScore": 3, "id": 1, "name": "Vessel-wise Profitability", "url": "https://app.powerbi.com/reportEmbed?reportId=3f0f80e8-5c44-46e9-b25e-99801ef9c3c5&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 2, "name": "<PERSON><PERSON><PERSON>", "url": "https://app.powerbi.com/reportEmbed?reportId=3f0f80e8-5c44-46e9-b25e-99801ef9c3c5&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 3, "name": "Fleet Revenue Analysis", "url": "https://app.powerbi.com/reportEmbed?reportId=3f0f80e8-5c44-46e9-b25e-99801ef9c3c5&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 2, "id": 3, "name": "Vessel Movement", "url": "https://app.powerbi.com/reportEmbed?reportId=3f0f80e8-5c44-46e9-b25e-99801ef9c3c5&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}, {"name": "Advanced Analytics", "bgcolor": "#2F80ED", "heading": "AI and advanced statistical models based projections to enable corrective actions", "dashboards": [{"completenessScore": 3, "id": 1, "name": "Overview", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 1, "name": "P&L Performance", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 1, "name": "Trending", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 1, "name": "Budget", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 1, "name": "Drivers", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}, {"completenessScore": 3, "id": 1, "name": "Exec Summary", "url": "https://app.powerbi.com/reportEmbed?reportId=e0b46806-cc50-4792-8062-21d70f74feea&appId=1cc91abd-21bd-45b3-a2b2-81b3737fba84&autoAuth=true&ctid=1806032f-a719-453e-8e14-5a5385413df4"}]}]