/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-param-reassign */
/* eslint-disable no-prototype-builtins */
/* eslint-disable react/no-array-index-key */
import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';

import SetDataTable from '@/components/tables/logicalEntities/setDataTable';
import { Badge } from '@/components/ui/Badge';
import { Checkbox, CheckBoxStates } from '@/components/ui/Checkbox';
import { Input } from '@/components/ui/Input';
import ListBox from '@/components/ui/ListBox';
import { Searchbar } from '@/components/ui/Searchbar';
import { APIService } from '@/service/api.service';
import {
  setCheckedTableNames,
  setSelectedDatasets,
} from '@/slices/logicalEntityCrudSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const SelectData: React.FC<{
  mode: string | string[] | undefined;
  onLogicalEntityChange: (e: any) => void;
  onDataSetChange: (e: any) => void;
  onTableChange: (dataSet: any, table: any) => void;
  onRowSelection?: any;
}> = (props) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const logicalEntityName = useSelector(
    (state: any) => state.logicalEntityCrud.logicalEntityName,
  );
  const selectedDataSets = useSelector(
    (state: any) => state.logicalEntityCrud.selectedDatasets,
  );
  const activatedAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.activatedAttributes,
  );
  const checkedTableNames = useSelector(
    (state: any) => state.logicalEntityCrud.checkedTableNames,
  );

  // States
  const [rowExpanded, setRowExpanded] = useState(0);
  const [isrowExpanded, setIsRowExpanded] = useState(true);
  const [searchTableItem, setSearchTableItem] = useState('');
  const [listItems, setListItems] = useState<any>();
  const [result, setResult] = useState<any>();
  const [loading, setLoading] = useState(true);
  const [searchResults, setSearchResults] = useState<any>([]);
  const [datasetTableData, setDatasetTableData] = useState<any>([]);
  const [selectedTableIndex, setSelectedTableIndex] = useState<any>({});

  // Methods
  const handleDataSetChange = (sds: any) => {
    const dataSetWithTables = result?.reduce((acc: any, currValue: any) => {
      if (
        sds?.some((dataSet: any) => dataSet?.name === currValue?.dataset_name)
      ) {
        const tables: any = currValue?.tables.reduce(
          (childAcc: any, childCurrValue: any) => {
            childAcc.push(childCurrValue?.table_name);
            return childAcc;
          },
          [],
        );
        acc.push({
          name: currValue?.dataset_name,
          tables: [...tables],
        });
        return acc;
      }
      return acc;
    }, []);
    setSearchResults(dataSetWithTables);
    setDatasetTableData(dataSetWithTables);
  };

  const resolveDatasets = (data: any) => {
    return data?.reduce((acc: any, currValue: any) => {
      acc.push({
        name: currValue.dataset_name,
        id: currValue.id,
        selected:
          selectedDataSets.filter((dataset: any) => dataset.id === currValue.id)
            .length > 0,
      });
      return acc;
    }, []);
  };

  const setChecboxStateForTable = () => {
    if (Object.keys(activatedAttributes).length > 0) {
      const checkedTables: any = {};
      for (const key in activatedAttributes) {
        if (activatedAttributes[key]?.tables) {
          checkedTables[key] = Object.keys(activatedAttributes[key].tables);
        }
      }
      dispatch(setCheckedTableNames(checkedTables));
    }
  };

  const populateSelectedDataset = (data: any) => {
    const datasetNames = Object.keys(activatedAttributes);
    const sds = data?.reduce((acc: any, currValue: any) => {
      if (datasetNames.includes(currValue.dataset_name)) {
        acc.push({
          name: currValue.dataset_name,
          id: currValue.id,
        });
      }
      return acc;
    }, []);
    dispatch(setSelectedDatasets(sds));
    setChecboxStateForTable();
    return sds;
  };

  const setSelectedIndexAndData = (datasetName: string, tableName: string) => {
    props.onTableChange(datasetName, tableName);
    const dataSet: any = result?.find(
      (dataset: any) => dataset?.dataset_name === datasetName,
    );
    const tableSet: any = dataSet?.tables?.find(
      (table: any) => table?.table_name === tableName,
    );
    setSelectedTableIndex({
      dataSetId: dataSet?.id,
      datasetName,
      tableId: tableSet?.id,
      tableName,
    });
  };

  const handleCheckboxChange = (
    isChecked: boolean,
    datasetName: string,
    tableName: string,
  ) => {
    const updatedState: any = { ...checkedTableNames };

    if (isChecked) {
      updatedState[datasetName] = [
        ...(updatedState[datasetName] || []),
        tableName,
      ];
    } else if (updatedState[datasetName]) {
      updatedState[datasetName] = updatedState[datasetName].filter(
        (name: string) => name !== tableName,
      );
      if (updatedState[datasetName].length === 0) {
        delete updatedState[datasetName];
      }
    }
    dispatch(setCheckedTableNames(updatedState));
  };

  const handleRowSelectionWithTableData = (selectedRows: any) => {
    const filteredRows = selectedRows.filter((row: any) => row.selected);
    // if (isSelectionChange) {
    //   filteredRows.forEach((item: any) => {
    //     if (item.components[4].value === '') {
    //       item.components[4].value = item.components[3].value;
    //     }
    //   });
    // }
    props.onRowSelection(
      selectedTableIndex.datasetName,
      selectedTableIndex.tableName,
      filteredRows,
    );
  };
  const handleTableSearch = (value: string) => {
    const results: any[] = [];
    setSearchTableItem(value);
    datasetTableData.forEach((dataset: any) => {
      const matchingTables = dataset.tables.filter((table: string) =>
        table.toLowerCase().includes(value.toLowerCase()),
      );
      if (matchingTables.length > 0) {
        results.push({
          name: dataset.name,
          tables: matchingTables,
        });
      }
    });
    setSearchResults(results);
  };

  // Effects
  useEffect(() => {
    function getResolvedData() {
      const url =
        ApiUtilities.getApiServerUrl +
        ApiUtilities.apiPath.getDatasetTableList.url;
      const apiData = apiService.getRequest(url);
      apiData.then((resp) => {
        setResult(resp?.data);
        populateSelectedDataset(resp?.data);
        setListItems(resolveDatasets(resp.data));
        setLoading(false);
      });
    }
    if (!listItems) {
      setLoading(true);
      getResolvedData();
    }
  }, [activatedAttributes]);

  useEffect(() => {
    if (props.mode === 'View') {
      handleDataSetChange(selectedDataSets); // for view mode
    }
  }, [result]);

  // useEffect(() => {
  //   console.log(selectedDataSets);
  //   setListItems(resolveDatasets(result));
  // }, [selectedDataSets]);

  if (loading || !listItems) {
    return <span>Loading...</span>;
  }

  return (
    <div
      className={`flex ${
        props.mode !== 'View' ? 'h-[72vh]' : 'h-[78vh]'
      } w-full flex-col space-y-4 px-4 pt-2`}
    >
      {props.mode !== 'View' && (
        <Formik
          initialValues={
            props.mode === 'Edit'
              ? {
                  Ename: 'Vendor Master',
                  datasets: 'VW_ACCOUNTS_PAYABLE',
                }
              : { EName: '', datasets: '' }
          }
          validate={(values) => {
            const errors: any = {};
            if (!values.Ename) {
              errors.Ename = 'Required.';
            }
            return errors;
          }}
          onSubmit={(values, { setSubmitting }) => {
            setTimeout(() => {
              // can show loader if wanted
              console.log(values);
              setSubmitting(false);
              // router.push('/dashboard');
            }, 400);
          }}
        >
          {({ values, errors, touched, handleBlur, handleSubmit }) => (
            <form className="flex w-full flex-col" onSubmit={handleSubmit}>
              <div className="flex flex-row space-x-6">
                <div className="w-[20%]">
                  <Input
                    label="Logical Entity Name"
                    name="EName"
                    type="text"
                    className="h-[50px]"
                    placeholder="Enter Logical Entity Name"
                    onChange={props.onLogicalEntityChange}
                    onBlur={handleBlur}
                    value={logicalEntityName}
                    intent={
                      errors.Ename && touched.Ename ? 'hasError' : 'enabled'
                    }
                    error={errors.Ename && touched.Ename && errors.Ename}
                  />
                </div>

                <div className="flex h-full min-w-[216px] max-w-[400px] flex-col">
                  <span className="mb-[5px] font-sans text-sm font-semibold text-gray-500">
                    Choose Datasets
                  </span>
                  <ListBox
                    items={listItems}
                    multiselect
                    name="datasets"
                    isPreselected={false}
                    selectedItems={selectedDataSets}
                    onSelectionChange={(selectedValue) => {
                      console.log(values);
                      props.onDataSetChange(selectedValue);
                      handleDataSetChange(selectedValue);
                    }}
                  />
                </div>
              </div>
            </form>
          )}
        </Formik>
      )}
      <div
        className={`flex w-full flex-row  rounded bg-lightgray-500 ${
          props.mode !== 'View' ? 'h-[62vh]' : 'h-full'
        }`}
      >
        <div className="flex h-full min-w-[15vw] flex-col  border-r-[1px] border-lightgray-200 ">
          <div className="w-full px-4">
            <span className=" font-sans text-base font-semibold leading-8 text-blueGray-300">
              Table
            </span>
          </div>
          <div className="flex h-full w-full flex-col space-y-2 px-2 ">
            {' '}
            <Searchbar
              value={searchTableItem}
              placeholder="Search"
              onChange={(e) => handleTableSearch(e.target.value)}
            />
            <div
              className={`overflow-auto pb-2 ${
                props.mode !== 'View' ? 'h-[48vh]' : 'h-[65vh]'
              }`}
            >
              {searchResults?.map((item: any, index: number) => (
                <div className="w-full" key={index}>
                  <button
                    type="button"
                    onClick={() => {
                      if (rowExpanded === index) {
                        setIsRowExpanded(!isrowExpanded);
                      } else {
                        setIsRowExpanded(true);
                      }
                      setRowExpanded(index);
                    }}
                    className="flex w-full cursor-pointer flex-row items-center justify-between px-[13px] py-3"
                  >
                    <span className="font-sans text-sm font-semibold text-gray-500">
                      {' '}
                      {item.name}
                    </span>
                    <img
                      className={`
                   ${rowExpanded === index && isrowExpanded ? '' : 'rotate-180'}
                     `}
                      src="/assets/images/menuDrop-Arrow-black.svg"
                      alt="expand"
                    />
                  </button>{' '}
                  {rowExpanded === index && isrowExpanded && (
                    <div className="flex h-full w-full flex-col space-y-1">
                      {item.tables
                        .sort((a: any, b: any) => {
                          const aIsInOb =
                            checkedTableNames[item.name]?.includes(a);
                          const bIsInOb =
                            checkedTableNames[item.name]?.includes(b);

                          if (aIsInOb && !bIsInOb) return -1;
                          if (!aIsInOb && bIsInOb) return 1;

                          return 0;
                        })
                        .map((table: any) => (
                          <button
                            type="button"
                            className={`flex flex-row items-center space-x-1 px-2 ${
                              selectedTableIndex?.datasetName === item.name &&
                              selectedTableIndex?.tableName === table
                                ? 'bg-lightblue-100'
                                : 'hover:bg-lightblue-100 focus:bg-lightblue-100 active:bg-lightblue-100'
                            }`}
                            key={table}
                          >
                            <div className="">
                              <Checkbox
                                disabled={props.mode === 'View'}
                                value={
                                  checkedTableNames[item.name]?.includes(table)
                                    ? CheckBoxStates.Checked
                                    : CheckBoxStates.Empty
                                }
                                onChange={(e) => {
                                  handleCheckboxChange(
                                    e.target.checked,
                                    item.name,
                                    table,
                                  );
                                }}
                              />
                            </div>
                            <button
                              data-tooltip-id="table-tooltip"
                              data-tooltip-content={
                                table?.length > 12 ? table : ''
                              }
                              key={table}
                              type="button"
                              onClick={() => {
                                setSelectedIndexAndData(item.name, table);
                              }}
                              // disabled={
                              //   !checkedTableNames[item.name]?.includes(table)
                              // }
                              className={`flex w-full items-center px-[10px] py-3 text-left font-sans text-sm font-medium disabled:text-opacity-40  ${
                                selectedTableIndex?.datasetName === item.name &&
                                selectedTableIndex?.tableName === table
                                  ? ' text-lightblue-200 disabled:text-gray-400'
                                  : 'text-gray-400 hover:text-lightblue-200 focus:text-lightblue-200 active:text-lightblue-200 disabled:text-gray-400'
                              }`}
                            >
                              <span className="truncateTablename">{table}</span>{' '}
                              {table?.length > 12 && (
                                <Tooltip
                                  className="custom-tooltip"
                                  id="table-tooltip"
                                  place="top"
                                  variant="info"
                                  positionStrategy="fixed"
                                />
                              )}
                              {activatedAttributes[item.name]?.tables?.[
                                table
                              ] &&
                                Object.keys(
                                  activatedAttributes[item.name]?.tables?.[
                                    table
                                  ],
                                ).length > 0 && (
                                  <Badge
                                    className="ml-2"
                                    intent="success"
                                    content={Object.keys(
                                      activatedAttributes[item.name]?.tables?.[
                                        table
                                      ],
                                    ).length.toString()}
                                  />
                                )}
                              {/*  */}
                            </button>
                          </button>
                        ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="flex h-full w-full flex-col overflow-x-hidden">
          <SetDataTable
            selectedTableData={selectedTableIndex}
            mode={props.mode}
            onRowSelection={handleRowSelectionWithTableData}
            activatedAttributes={activatedAttributes}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectData;
