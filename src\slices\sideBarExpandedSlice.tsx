import { createSlice } from '@reduxjs/toolkit';

export interface SideBarExpandedState {
  value: boolean;
}

const initialState: SideBarExpandedState = {
  value: false,
};

export const sideBarExpandedSlice = createSlice({
  name: 'sideBarExpand',
  initialState,
  reducers: {
    setExpandedValue: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.value = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { setExpandedValue } = sideBarExpandedSlice.actions;

export default sideBarExpandedSlice.reducer;
