/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react-hooks/exhaustive-deps */

'use client';

/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import { Formik, useFormik } from 'formik';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Input } from '@/components/ui/Input';
import { Link } from '@/components/ui/Link';
import ListBox from '@/components/ui/ListBox';
import { Radio } from '@/components/ui/Radio';
import { downloadMenuTitle } from '@/constants/menuSvg';
import { TestDataService } from '@/service/testdata.service';

import { Button } from '../../ui/Button';

const AddNewCollab: React.FC = () => {
  // Essentials
  const router = useRouter();
  const searchParams = useSearchParams();
  const testDataService = new TestDataService();

  // Derivatives
  const mode: any = searchParams?.get('mode');

  // States
  const [collabName, setCollabName] = useState('');
  const [listItems, setListItems] = useState<any>([
    { name: 'Payment Analytics' },
    { name: 'Order History' },
    { name: 'Payable Insights' },
    { name: 'Purchase Details' },
  ]);
  const [mappingItems] = useState<any>([
    { name: 'Order Mapping' },
    { name: 'Entity Mapping' },
    { name: 'Product Mapping' },
    { name: 'Region Mapping' },
  ]);
  const [data, setData] = useState<{
    [key: string]: any;
  }>({
    values: [],
  });

  const { setFieldValue, values, errors, touched, handleBlur, handleSubmit } =
    useFormik({
      initialValues: {
        CName: '',
        desc: '',
        OName: '',
        PName: '',
        PMailID: '',
        APIURL: '',
      },
      onSubmit: () => {},
    });

  // Methods
  const goBack = () => {
    router.push('/dataCollaboration');
  };

  const handelCancel = () => {
    setListItems([]);
  };

  // Effects
  useEffect(() => {
    if (mode === 'Edit') {
      const fetchData = async () => {
        const response = await testDataService.getAllTestDataFromFile(
          'dataCollaboration/viewDetails',
        );
        setData({
          ...data,
          values: response,
        });
        setCollabName(response[0].collaboration_name);
        setFieldValue('CName', response[0].collaboration_name);
        setFieldValue('desc', response[0].description);
        setFieldValue('OName', response[0].owner_name);
        setFieldValue('PName', response[0].partner_name);
        setFieldValue('PMailID', response[0].partner_mail_id);
        setFieldValue('APIURL', response[0].api_url);
      };
      fetchData();
    } else if (mode === 'View') {
      const fetchData = async () => {
        const response = await testDataService.getAllTestDataFromFile(
          'dataCollaboration/viewDetails',
        );
        setData({
          ...data,
          values: response,
        });
        setCollabName(response[0]?.collaboration_name);
      };
      fetchData();
    }
  }, [mode]);

  return (
    <div className="data-collaboration-cred edit-data-collaboration view-data-collaboration flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} Data Collaboration Suite{' '}
          {collabName && mode !== 'Create' && <span>- {collabName}</span>}
        </span>
      </div>
      {mode !== 'View' ? (
        <div className="flex h-[82vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
          <div className="flex w-full flex-col space-y-4 overflow-auto p-6">
            <Formik
              initialValues={{
                CName: '',
                desc: '',
                OName: '',
                PName: '',
                PMailID: '',
                APIURL: '',
              }}
              enableReinitialize
              validate={(value) => {
                const error: any = {};
                if (!value.CName) {
                  error.CName = 'Required.';
                }
                if (!value.desc) {
                  error.desc = 'Required.';
                }
                if (!value.OName) {
                  error.OName = 'Required.';
                }
                if (!value.PName) {
                  error.PName = 'Required.';
                }
                if (!value.PMailID) {
                  error.PMailID = 'Required.';
                }
                if (!value.APIURL) {
                  error.APIURL = 'Required.';
                }
                // setFormikErrors(errors);
                return error;
              }}
              onSubmit={(v, { setSubmitting }) => {
                setTimeout(() => {
                  console.log(v);
                  // can show loader if wanted
                  setSubmitting(false);
                  // router.push('/dashboard');
                }, 400);
              }}
            >
              {/* {({ values, errors, touched, handleBlur, handleSubmit }) => ( */}
              <form
                className="flex h-full w-full flex-col space-y-6 "
                onSubmit={handleSubmit}
                id="myform"
              >
                <div>
                  <div className="pb-4 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
                    Collaboration Suite Details
                  </div>
                  <div className="flex flex-wrap space-x-6">
                    <div className="w-[20%]">
                      <Input
                        label="Collaboration Name"
                        name="CName"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter Collaboration Name"
                        onChange={(e) => setFieldValue('CName', e.target.value)}
                        onBlur={handleBlur}
                        value={values.CName}
                        intent={
                          errors.CName && touched.CName ? 'hasError' : 'enabled'
                        }
                        error={errors.CName && touched.CName && errors.CName}
                      />
                    </div>

                    <div className="w-[75%]">
                      <Input
                        label="Description"
                        name="desc"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter Description"
                        onChange={(e) => setFieldValue('desc', e.target.value)}
                        onBlur={handleBlur}
                        value={values.desc}
                        intent={
                          errors.desc && touched.desc ? 'hasError' : 'enabled'
                        }
                        error={errors.desc && touched.desc && errors.desc}
                      />
                    </div>
                  </div>
                  <div className="pb-4 pt-16 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
                    Collaboration Parties
                  </div>
                  <div className="flex flex-wrap space-x-6">
                    <div className="w-[20%]">
                      <Input
                        label="Owner Name"
                        name="OName"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter Collaboration Parties Name"
                        onChange={(e) => setFieldValue('OName', e.target.value)}
                        onBlur={handleBlur}
                        value={values.OName}
                        intent={
                          errors.OName && touched.OName ? 'hasError' : 'enabled'
                        }
                        error={errors.OName && touched.OName && errors.OName}
                      />
                    </div>

                    <div className="w-[20%]">
                      <Input
                        label="Partner Name"
                        name="PName"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter Partner Name"
                        onChange={(e) => setFieldValue('PName', e.target.value)}
                        onBlur={handleBlur}
                        value={values.PName}
                        intent={
                          errors.PName && touched.PName ? 'hasError' : 'enabled'
                        }
                        error={errors.PName && touched.PName && errors.PName}
                      />
                    </div>

                    <div className="w-[26%]">
                      <Input
                        label="Partner Mail ID"
                        name="PMailID"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter Partner Mail ID"
                        onChange={(e) =>
                          setFieldValue('PMailID', e.target.value)
                        }
                        onBlur={handleBlur}
                        value={values.PMailID}
                        intent={
                          errors.PMailID && touched.PMailID
                            ? 'hasError'
                            : 'enabled'
                        }
                        error={
                          errors.PMailID && touched.PMailID && errors.PMailID
                        }
                      />
                    </div>

                    <div className="flex w-[10%] flex-col space-y-3">
                      <div className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
                        Share Data Via
                      </div>
                      <div className="flex flex-row">
                        <Radio
                          label="API"
                          name="VIA"
                          className="max-w-[210px]"
                          value="VIA"
                          // onChange={(e) => setFieldValue('CName', e.target.value)}
                          onBlur={handleBlur}
                          intent="enabled"
                        />
                        <Radio
                          label="File Upload"
                          name="VIA"
                          value="VIA"
                          className="max-w-[70px]"
                          // onChange={(e) => setFieldValue('CName', e.target.value)}
                          onBlur={handleBlur}
                          intent="enabled"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="pb-4 pt-16 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
                    Collaboration Configuration
                  </div>
                  <div className="flex flex-wrap space-x-6">
                    <div className="flex h-full min-w-[216px] max-w-[400px] flex-col">
                      <span className="mb-[5px] font-sans text-sm font-semibold text-gray-500">
                        Select Analytics to Monitor
                      </span>
                      <ListBox
                        items={listItems}
                        multiselect
                        name="datasets"
                        onSelectionChange={() => {}}
                        // onSelectionChange={(selectedValue) => {
                        //   props.onDataSetChange(selectedValue);
                        //   handleDataSetChange(selectedValue);
                        //   console.log(selectedValue);
                        // }}
                      />
                    </div>

                    <div className="flex w-[22%] flex-col space-y-3">
                      <div className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
                        Data Share Via
                      </div>
                      <div className="flex flex-row">
                        <Radio
                          label="API"
                          name="API"
                          className="max-w-[210px]"
                          value="API"
                          // onChange={handleChange}
                          onBlur={handleBlur}
                          intent="enabled"
                        />
                        <Radio
                          label="File Upload"
                          name="API"
                          value="API"
                          className="max-w-[40px]"
                          // onChange={handleChange}
                          onBlur={handleBlur}
                          intent="enabled"
                        />
                      </div>
                    </div>

                    <div className="w-[53%]">
                      <Input
                        label="API URL"
                        name="APIURL"
                        type="text"
                        className="h-[50px]"
                        placeholder="Enter API URL"
                        onChange={(e) =>
                          setFieldValue('APIURL', e.target.value)
                        }
                        onBlur={handleBlur}
                        value={values.APIURL}
                        intent={
                          errors.APIURL && touched.APIURL
                            ? 'hasError'
                            : 'enabled'
                        }
                        error={errors.APIURL && touched.APIURL && errors.APIURL}
                      />
                    </div>
                  </div>
                  <div className="flex flex-wrap space-x-6 pt-6">
                    <div className="flex h-full min-w-[216px] max-w-[400px] flex-col">
                      <span className="mb-[5px] font-sans text-sm font-semibold text-gray-500">
                        Select Mapping Templates
                      </span>
                      <ListBox
                        items={mappingItems}
                        multiselect
                        name="datasets"
                        onSelectionChange={() => {}}
                        // onSelectionChange={(selectedValue) => {
                        //   props.onDataSetChange(selectedValue);
                        //   handleDataSetChange(selectedValue);
                        //   console.log(selectedValue);
                        // }}
                      />
                    </div>
                  </div>
                </div>
              </form>
              {/* )} */}
            </Formik>
          </div>
        </div>
      ) : (
        <div className="h-[82vh]  w-full rounded bg-white-200 p-4">
          <div className="py-4 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
            Collaboration Suite Details
          </div>
          <div className="flex flex-row space-x-11">
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Collaboration Name
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.collaboration_name}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Description
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.description}
              </span>
            </div>
          </div>
          <div className="pb-4 pt-10 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
            Collaboration Parties
          </div>
          <div className="flex flex-row space-x-11">
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Owner Name
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.owner_name}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Partner Name
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.partner_name}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Partner Category
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.partner_category}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Partner Mail ID
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.partner_mail_id}
              </span>
            </div>
          </div>
          <div className="pb-4 pt-10 font-sans text-base font-semibold leading-6 text-gray-500 disabled:opacity-50">
            Collaboration Configuration
          </div>
          <div className="flex flex-row space-x-11">
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Analytics to Monitor
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.analytics_to_monitor}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Share data via
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.share_data_via}
              </span>
            </div>
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                API
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.api_url}
              </span>
            </div>
          </div>
          <div className="flex flex-row space-x-11">
            <div className="flex flex-col space-y-2 pt-4">
              <span className="font-sans text-xs font-medium text-gray-400">
                Templates
              </span>{' '}
              <span className="flex font-sans text-sm font-semibold text-gray-500">
                {data.values[0]?.templates}
                <span className="pl-2">
                  <Link content={downloadMenuTitle} onClick={() => {}} />
                </span>
              </span>
            </div>
          </div>
        </div>
      )}
      {mode !== 'View' && (
        <div className="flex h-[40px] w-full flex-row items-center justify-between">
          <div />
          <div className="flex flex-row space-x-4">
            {' '}
            <Button
              className="flex h-[40px] items-center"
              type="button"
              onClick={handelCancel}
              intent="secondary"
            >
              Cancel
            </Button>
            <Button
              className="flex h-[40px] items-center"
              type="submit"
              form="myform"
              // disabled
            >
              {mode === 'Edit' ? 'Update' : 'Create'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddNewCollab;
