/* Power BI Style V2 Data Grid */
.rdg {
  border: 1px solid #e1e5e9;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 12px;
  background: white;
  width: 100%;
  min-width: 100%;
  table-layout: auto !important;
}

/* Ensure table container takes full width */
.max-w-screen {
  width: 100% !important;
  max-width: none !important;
}

/* Header styling */
.rdg-header-row {
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  font-weight: 600;
  color: #3c4043;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
}

.rdg-cell {
  border-right: 1px solid #f1f3f4;
  border-bottom: 1px solid #f1f3f4;
  padding: 8px 12px;
  line-height: 1.2;
}

.rdg-cell:last-child {
  border-right: none;
}

/* Row styling */
.rdg-row {
  background: white;
  transition: background-color 0.1s ease;
}

.rdg-row:nth-child(even) {
  background: #fafbfc;
}

.rdg-row:hover {
  background: #f8f9fa !important;
}

/* Header cell styling */
.rdg-header-row .rdg-cell {
  background: #f8f9fa;
  color: #3c4043;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 11px;
  border-right: 1px solid #e1e5e9;
  border-bottom: 1px solid #e1e5e9;
  height: 40px !important;
  min-height: 40px !important;
  display: flex;
  align-items: center;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: clip !important;
  padding: 8px 12px !important;
  box-sizing: border-box;
  min-width: fit-content !important;
  position: relative;
}

/* Ensure header text is fully visible within container bounds */
.rdg-header-row .rdg-cell span,
.rdg-header-row .rdg-cell div {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: clip !important;
  max-width: 100% !important;
  width: auto !important;
  flex: 1;
}

/* Override any global power-bi styles that might conflict */
.rdg .rdg-header-row .rdg-cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: clip !important;
  min-width: fit-content !important;
}

/* Ensure columns automatically size to fit header content */
.rdg-header-row {
  table-layout: auto !important;
}

.rdg-cell {
  min-width: auto !important;
  width: auto !important;
}

/* Specific column width adjustments for Entity Management table */
.rdg-header-row .rdg-cell:nth-child(1) {
  min-width: 80px !important; /* SNO */
}

.rdg-header-row .rdg-cell:nth-child(2) {
  min-width: 120px !important; /* ENTITY CODE */
}

.rdg-header-row .rdg-cell:nth-child(3) {
  min-width: 150px !important; /* VESSEL NAME */
}

.rdg-header-row .rdg-cell:nth-child(4) {
  min-width: 120px !important; /* SMC NAME */
}

.rdg-header-row .rdg-cell:nth-child(5) {
  min-width: 100px !important; /* STATUS */
}

.rdg-header-row .rdg-cell:nth-child(6) {
  min-width: 140px !important; /* CREATED DATE */
}

.rdg-header-row .rdg-cell:nth-child(7) {
  min-width: 100px !important; /* ACTIONS */
}

/* Force override any global truncation styles that might conflict */
.rdg-header-row .rdg-cell,
.rdg-header-row .rdg-cell *,
.rdg-header-row .rdg-cell .header-text {
  text-overflow: clip !important;
  overflow: hidden !important;
  white-space: nowrap !important;
}

/* Prevent global truncation classes from affecting headers */
.rdg-header-row .rdg-cell.truncateData,
.rdg-header-row .rdg-cell .truncateData,
.rdg-header-row .rdg-cell.truncateTablename,
.rdg-header-row .rdg-cell .truncateTablename {
  text-overflow: clip !important;
  overflow: hidden !important;
  max-width: none !important;
}

/* Ensure proper rendering after hydration */
.rdg-header-row .rdg-cell {
  contain: layout style !important;
  will-change: auto !important;
}

/* Filter container */
.filter-cell {
  padding: 4px 8px;
  background: white;
}

/* Filter input styling - Power BI style */
.filter-cell input[type="text"] {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #dadce0;
  border-radius: 2px;
  font-size: 12px;
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  outline: none;
}

.filter-cell input[type="text"]:hover {
  border-color: #bdc1c6;
}

.filter-cell input[type="text"]:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
  background: white;
}

/* Filter select styling */
.filter-cell select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #dadce0;
  border-radius: 2px;
  font-size: 12px;
  background: white;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  outline: none;
  cursor: pointer;
}

.filter-cell select:hover {
  border-color: #bdc1c6;
}

.filter-cell select:focus {
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* Frozen column styling */
.rdg-cell.rdg-cell-frozen {
  position: sticky;
  left: 0;
  z-index: 5;
  background: inherit;
  box-shadow: 2px 0 4px rgba(0,0,0,0.1);
}

.rdg-header-row .rdg-cell.rdg-cell-frozen {
  z-index: 15;
  background: #f8f9fa;
}

/* Sort indicator */
.rdg-sort-arrow {
  color: #5f6368;
  font-size: 10px;
  margin-left: 4px;
}

/* Selection styles */
.rdg-cell .rdg-checkbox {
  width: 14px;
  height: 14px;
}

.rdg-row.rdg-row-selected {
  background: #e3f2fd !important;
}

.rdg-row.rdg-row-selected:hover {
  background: #bbdefb !important;
}

/* Compact styling */
.rdg.compact .rdg-cell {
  padding: 4px 8px;
  font-size: 11px;
  height: 28px;
}

.rdg.compact .rdg-header-row .rdg-cell {
  padding: 6px 8px;
  height: 32px;
  font-size: 10px;
}

/* Badge styling in cells */
.rdg-cell .inline-flex {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.rdg-cell .bg-green-100 {
  background-color: #d4f7d4;
  color: #2e7d2e;
}

.rdg-cell .bg-red-100 {
  background-color: #fce8e6;
  color: #d93025;
}

/* Scrollbar styling */
.rdg::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.rdg::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.rdg::-webkit-scrollbar-thumb {
  background: #dadce0;
  border-radius: 4px;
}

.rdg::-webkit-scrollbar-thumb:hover {
  background: #bdc1c6;
}

/* Loading and empty states */
.rdg-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #5f6368;
  font-size: 14px;
}

.rdg-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #5f6368;
  font-size: 14px;
}

/* Action buttons in cells */
.rdg-cell button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.15s ease;
  color: inherit;
}

.rdg-cell button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Dropdown styling inside cells */
.rdg-cell .relative {
  position: relative;
}

.rdg-cell .absolute {
  position: absolute;
  z-index: 50;
  right: 0;
  top: 100%;
  margin-top: 4px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.rdg-cell .absolute button {
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  font-size: 12px;
  border: none;
  background: transparent;
  transition: background-color 0.15s ease;
}

.rdg-cell .absolute button:hover {
  background-color: #f8f9fa;
}