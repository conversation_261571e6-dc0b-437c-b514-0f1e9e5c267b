/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable import/no-cycle */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-no-useless-fragment */

import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';

import { setSort } from '@/slices/appSlice';
import { setIsWarningAlertOpen, setWarningAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

import Avatar from './Avatar';
import { Badge } from './Badge';
import { Checkbox, CheckBoxStates } from './Checkbox';
import { WarningDialog } from './Dialog';
import { Dropdown } from './Dropdown';
import { Input } from './Input';
import { Link } from './Link';
import ListBoxTable from './ListBoxTable';
import Pagination from './Pagination';
import { TableInput } from './TableInput';
import { Toggle } from './Toggle';

const Table: React.FC<{
  header: any[];
  hasPagination?: boolean;
  data: any;
  isDashTable: boolean;
  enableOptions: boolean;
  menuData?: any;
  isEdit?: boolean;
  enabledCross?: boolean;
  isView?: boolean;
  isEyeVisible?: boolean;
  EyeFunction?: any;
  isDownloadVisible?: boolean;
  isCondensedTable?: boolean;
  onRowSelection?: any;
  onRowChange?: any;
  onActionChange?: any;
  onEdit?: any;
  isUploadTable?: boolean;
  scrollPosition?: any;
  openTableModal?: any;
  canMarkFav?: boolean;
  onMarkFav?: any;
  onLockChange?: any;
  isStatusTable?: boolean;
  hasLock?: boolean;
}> = ({
  header,
  data,
  hasPagination,
  isDashTable,
  enableOptions,
  menuData,
  enabledCross,
  isUploadTable,
  onActionChange,
  isEdit,
  isEyeVisible,
  EyeFunction,
  isDownloadVisible,
  isView,
  isCondensedTable,
  onRowSelection,
  onRowChange,
  onEdit,
  scrollPosition,
  openTableModal,
  canMarkFav,
  onMarkFav,
  isStatusTable,
}) => {
  // Essentials
  const dispatch = useDispatch();
  const truncateDivRef = useRef<any>(null);

  // Selectors
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const sort = useSelector((state: RootState) => state.appConfig.sort);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [currentItems, setCurrentItem] = useState<any>([]);
  const paginate = (pageNumber: any) => setCurrentPage(pageNumber);

  // States
  const [shouldTruncate, setShouldTruncate] = useState(false);
  const [headerCheckBoxState, setHeaderCheckBoxState] = useState(
    CheckBoxStates.Empty,
  );
  const [rows, setRows] = useState(data || []);
  const [selectedCellIndex, setSelectedCellIndex] = useState(-1);
  const [selectedRowIndex, setSelectedRowIndex] = useState(-1);
  const [InputInternalValue, setInputInternalValue] = useState<any>({});
  const [sorting, setSorting] = useState<{
    column: number;
    order: 'asc' | 'desc';
  } | null>(null);

  useEffect(() => {
    setInputInternalValue({});
  }, [data]);

  // Methods
  const toggleSorting = (columnIndex: number) => {
    if (!sorting) {
      setSorting({ column: columnIndex, order: 'asc' });
      dispatch(setSort({ column: columnIndex, order: 'asc' }));
    } else if (sorting.column === columnIndex) {
      setSorting({
        ...sorting,
        order: sorting.order === 'asc' ? 'desc' : 'asc',
      });
      dispatch(
        setSort({
          ...sorting,
          order: sorting.order === 'asc' ? 'desc' : 'asc',
        }),
      );
    } else {
      setSorting({ column: columnIndex, order: 'asc' });
      dispatch(setSort({ column: columnIndex, order: 'asc' }));
    }
  };

  const sortValues = (a: any, b: any) => {
    if (!sorting) return 0;
    const { column, order } = sorting;
    const valueA = a.components[column]?.value;
    const valueB = b.components[column]?.value;
    if (order === 'asc') {
      if (valueA < valueB) return -1;
      if (valueA > valueB) return 1;
    } else {
      if (valueA > valueB) return -1;
      if (valueA < valueB) return 1;
    }
    return 0;
  };

  function formatDateString(dateString: string) {
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(dateString)) {
      const date = new Date(dateString);
      const day = String(date.getUTCDate()).padStart(2, '0');
      const month = String(date.getUTCMonth() + 1).padStart(2, '0');
      const year = date.getUTCFullYear();
      let hours = date.getUTCHours();
      const minutes = String(date.getUTCMinutes()).padStart(2, '0');
      const seconds = String(date.getUTCSeconds()).padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours %= 12;
      hours = hours || 12;
      if (isStatusTable) {
        return `${day}-${month}-${year} ${hours}:${minutes}:${seconds} ${ampm}`;
      }
      return `${day}-${month}-${year}`;
    }
    return dateString;
  }

  const handleRowCheckboxChange = (_cellIndex: number, rowIndex: number) => {
    const newRows = rows?.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.selected = !row.selected;
      }
      return row;
    });
    const selectedRows = rows?.filter((row: any) => row.selected);
    const isSelectionChange: boolean = true;
    if (onRowSelection) {
      onRowSelection(selectedRows, isSelectionChange);
    }
    setRows(newRows);
  };

  const handleRowStatusChange = (cellIndex: number, rowIndex: number) => {
    setSelectedCellIndex(cellIndex);
    setSelectedRowIndex(rowIndex);
    dispatch(
      setWarningAlert({
        isWarningOpen: true,
        headerTitle: 'Inactive Connection',
        message:
          'Are you sure? Do you want to make the Connection Inactive Please note this action can cause some interruptions in the flow',
        actionbuttonText: 'Yes, make it Inactive',
      }),
    );
  };

  const openModal = () => {
    if (openTableModal) {
      openTableModal();
    }
  };

  const handleRowInputStatusChange = (
    cellIndex: number,
    rowIndex: number,
    value: any,
  ) => {
    const updatedRows = structuredClone(rows);
    const newRows = updatedRows?.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.components[cellIndex].value = value;
      }
      return row;
    });
    setRows(newRows);
    if (onRowChange) {
      onRowChange(newRows);
    }
  };

  const handleInputValueInternal = (e: any, index: any) => {
    const Values: any = { ...InputInternalValue };
    Values[index] = e.target.value;
    setInputInternalValue(Values);
  };

  const handleRowSelectStatusChange = (
    cellIndex: number,
    rowIndex: number,
    value: any,
    selectedValue: any,
  ) => {
    const updatedRows = structuredClone(rows);
    const newRows = updatedRows?.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.components[cellIndex].value = value;
        row.components[cellIndex].selectedValue = selectedValue;
      }
      return row;
    });
    setRows(newRows);
    if (onRowChange) {
      onRowChange(newRows);
    }
  };

  const handleRowActionChange = (
    cellIndex: number,
    rowIndex: number,
    selectedValue: any,
  ) => {
    const updatedRows = structuredClone(rows);
    const newRows = updatedRows?.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.components[cellIndex].value = selectedValue.name;
        row.components[cellIndex].selectedValue = selectedValue;
      }
      return row;
    });
    if (onActionChange) {
      onActionChange(newRows, cellIndex, rowIndex, selectedValue.name);
    }
    setRows(newRows);
  };

  const handleConfirm = () => {
    dispatch(setIsWarningAlertOpen(false));
    const newRows = rows?.map((row: any, index: any) => {
      if (index === selectedRowIndex) {
        row.components[selectedCellIndex].value =
          !row.components[selectedCellIndex].value;
      }
      return row;
    });
    setRows(newRows);
    if (onRowChange) {
      onRowChange(newRows);
    }
  };

  const handleAllCheckboxChange = () => {
    const newRows = rows?.map((row: any) => {
      row.selected =
        headerCheckBoxState === CheckBoxStates.Empty ||
        headerCheckBoxState === CheckBoxStates.Indeterminate;
      return row;
    });
    setRows(newRows);
    const isSelectionChange: boolean = true;
    if (onRowSelection) {
      onRowSelection(newRows, isSelectionChange);
    }
  };

  const removeRow = (rowIndex: number) => {
    const newData = [...rows];
    newData.splice(rowIndex, 1);
    setRows(newData);
    if (onRowChange) {
      onRowChange(newData);
    }
  };

  const renderHeader = () => {
    return (
      <tr className="z-20 w-full">
        {header.map((item: any, index: number) => (
          <th
            className={`px-3 ${isCondensedTable ? 'h-[36px]' : 'py-4'}`}
            key={index}
          >
            <div
              className={`flex flex-row items-center justify-start space-x-3 ${
                item.width ? item.width : ''
              }`}
            >
              {item.inputType === 'checkbox' && (
                <Checkbox
                  value={headerCheckBoxState}
                  onChange={() => handleAllCheckboxChange()}
                  disabled={item.disable || isView}
                />
              )}
              {!(item.optionsEnabled && item.options.includes('sort')) && (
                <span className="font-sans text-sm  font-normal capitalize leading-4 text-gray-400">
                  {item.title}
                </span>
              )}
              {item.optionsEnabled && item.options.includes('sort') && (
                <button
                  type="button"
                  onClick={() => toggleSorting(index)}
                  className="flex flex-row items-center justify-between"
                >
                  <span className="mr-4 font-sans text-sm  font-normal capitalize leading-4 text-gray-400">
                    {item.title}
                  </span>
                  {sorting?.order === 'asc' && sorting.column === index && (
                    <img
                      className="cursor-pointer"
                      src="/assets/images/sort.svg"
                      alt="sort"
                    />
                  )}
                  {sorting?.order === 'desc' && sorting.column === index && (
                    <img
                      className="rotate-180 cursor-pointer"
                      src="/assets/images/sort.svg"
                      alt="sort"
                    />
                  )}
                  {sorting?.column !== index && (
                    <img
                      className="cursor-pointer"
                      src="/assets/images/sort.svg"
                      alt="sort"
                    />
                  )}
                </button>
              )}
            </div>
          </th>
        ))}
        {canMarkFav && <th />}
        {isEdit && <th />}
        {isView && <th />}
        {enableOptions && <th />}
        {enabledCross && <th />}
        {isUploadTable && (
          <th className={`px-3 ${isCondensedTable ? 'h-[36px]' : 'py-4'}`}>
            <div className="flex flex-row items-center justify-start space-x-3">
              <div className="flex flex-row items-center justify-between">
                <span className="mr-4 font-sans text-sm  font-normal capitalize leading-4 text-gray-400">
                  Action
                </span>
              </div>
            </div>
          </th>
        )}
      </tr>
    );
  };

  const handleRowFavChange = (rowIndex: number, rowId: any, isfav: boolean) => {
    const newRows = rows?.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.isFavourite = !row.isFavourite;
      }
      return row;
    });
    if (onMarkFav) {
      onMarkFav(rowId, !isfav);
    }
    setRows(newRows);
  };

  const renderValueCell = (
    isSelected: boolean,
    cellData: any,
    index: number,
    rowIndex: number,
  ) => {
    return (
      <td key={index} className={`${isCondensedTable ? 'h-[34px]' : 'py-4'}`}>
        <div
          ref={truncateDivRef}
          className={`flex flex-row items-center justify-start space-x-3 px-3 ${
            cellData.width ? cellData.width : ''
          }`}
        >
          {cellData.type === 'checkbox' && (
            <Checkbox
              value={isSelected ? CheckBoxStates.Checked : CheckBoxStates.Empty}
              onChange={() => handleRowCheckboxChange(index, rowIndex)}
              disabled={cellData.disable || isView}
            />
          )}
          {cellData.type === 'avatar' && (
            <Avatar name={cellData.value} className="size-8" />
            // <img src={cellData.userImageLink} alt="Avatar" />
          )}
          {cellData.type === 'badges' && (
            <>
              {cellData.badges.map((badge: any) => {
                return (
                  <>
                    {badge.intent === 'counter' ? (
                      <Badge
                        data-tooltip-id="table-badge"
                        data-tooltip-content={badge.content}
                        className={` ${
                          badge.intent === 'counter'
                            ? ' w-[80px] cursor-pointer overflow-hidden px-3 text-xs'
                            : ''
                        } `}
                        key={badge.id}
                        intent={badge.intent}
                        content={
                          badge.content.length > 6 && badge.intent === 'counter'
                            ? `${badge.content.substring(0, 5)}...`
                            : badge.content
                        }
                      />
                    ) : (
                      <Badge
                        data-tooltip-id="table-badge"
                        data-tooltip-content={badge.reasonOfFailure}
                        className={`whitespace-nowrap ${
                          badge.reasonOfFailure !== '' ? 'cursor-pointer' : ''
                        }`}
                        key={badge.id}
                        intent={badge.intent}
                        content={badge.content}
                      />
                    )}
                  </>
                );
              })}
              <Tooltip
                className="custom-tooltip"
                id="table-badge"
                place="top"
                variant="info"
                positionStrategy="fixed"
              />
            </>
          )}
          {cellData.type === 'toggle' && (
            <Toggle
              checked={cellData.value}
              disabled={cellData.disabled}
              onChange={() =>
                !cellData.disabled && handleRowStatusChange(index, rowIndex)
              }
            />
          )}
          {cellData.type === 'input' && rows[rowIndex].selected && !isView && (
            <div className="w-full min-w-[216px]">
              {isCondensedTable ? (
                <TableInput
                  className="w-full"
                  name={cellData.placeholder}
                  type="text"
                  placeholder={cellData.placeholder}
                  value={InputInternalValue?.[rowIndex] ?? cellData.value}
                  onChange={(e) => {
                    if (!cellData.disabled) {
                      handleInputValueInternal(e, rowIndex);
                    }
                  }}
                  onBlur={(e) => {
                    if (!cellData.disabled) {
                      handleRowInputStatusChange(
                        index,
                        rowIndex,
                        e.target.value,
                      );
                    }
                  }}
                />
              ) : (
                <Input
                  className="w-full"
                  name={cellData.placeholder}
                  type="text"
                  placeholder={cellData.placeholder}
                  value={InputInternalValue?.[rowIndex] ?? cellData.value}
                  onChange={(e) => {
                    if (!cellData.disabled) {
                      handleInputValueInternal(e, rowIndex);
                      handleRowInputStatusChange(
                        index,
                        rowIndex,
                        e.target.value,
                      );
                    }
                  }}
                />
              )}
            </div>
          )}
          {cellData.type === 'select' && !isView && (
            <div className="w-full min-w-[70px] max-w-[160px] overflow-x-clip">
              <ListBoxTable
                scrollPosition={scrollPosition}
                refId={rowIndex}
                placeholder={cellData.placeholder}
                isInTable={isCondensedTable}
                items={cellData.selectList}
                selectedValue={cellData.value}
                hasValue={cellData.value !== null}
                onSelectionChange={(selectedValue) => {
                  handleRowSelectStatusChange(
                    index,
                    rowIndex,
                    selectedValue.name,
                    selectedValue,
                  );
                }}
                name="select"
              />
            </div>
          )}
          {cellData.value !== '' &&
            cellData?.valueType !== 'multiple' &&
            cellData.type !== 'action' &&
            cellData.type !== 'image' &&
            cellData.type !== 'badges' && (
              <span
                data-tooltip-id="table-data"
                data-tooltip-content={formatDateString(cellData.value)}
                className={`font-sans text-sm font-semibold leading-4 tracking-[0.2px] text-gray-500 ${
                  formatDateString(cellData.value)?.length > 50 &&
                  shouldTruncate
                    ? 'truncateData'
                    : 'pointer-events-none text-left'
                }`}
              >
                {cellData.type === 'formula'
                  ? formatDateString(cellData.value)
                  : !isView &&
                      ![
                        'badges',
                        'image',
                        'input',
                        'select',
                        'action',
                        'file',
                        'modal',
                        'formula',
                        'validate',
                      ].includes(cellData.type)
                    ? formatDateString(cellData.value)
                    : isView &&
                        ![
                          'badges',
                          'file',
                          'modal',
                          'formula',
                          'validate',
                          'image',
                          'action',
                        ].includes(cellData.type)
                      ? formatDateString(cellData.value)
                      : null}
              </span>
            )}

          {cellData.valueType === 'multiple' && (
            <div className="flex flex-col space-y-1">
              {cellData.value?.map((value: any, i: any) => (
                <div key={i}>
                  <span
                    className={`font-sans text-sm font-semibold leading-4 tracking-[0.2px] text-gray-500 ${
                      formatDateString(value)?.length > 50
                        ? 'truncateData'
                        : 'pointer-events-none'
                    }`}
                  >
                    {value}
                  </span>
                </div>
              ))}
            </div>
          )}

          {formatDateString(cellData.value)?.length > 50 && (
            <Tooltip
              className="custom-tooltip"
              id="table-data"
              place="top"
              variant="info"
              positionStrategy="fixed"
            />
          )}
          {cellData.type === 'file' && cellData.value !== '' && (
            <Link
              className="px-1 text-blue-200"
              href={cellData.link}
              content={cellData.value}
            />
          )}
          {cellData.type === 'modal' && cellData.value !== '' && (
            <button
              type="button"
              className="cursor-pointer"
              onClick={() => {
                openModal();
              }}
            >
              <span className=" font-sans text-sm font-semibold capitalize leading-4 tracking-[0.2px] text-blue-200">
                {cellData.value}
              </span>
            </button>
          )}
          {cellData.value === null && isView && (
            <div className="w-full font-sans text-sm font-semibold capitalize text-gray-500">
              -
            </div>
          )}

          {cellData.value === '' && cellData?.type === 'text' && (
            <span className="font-sans text-sm font-semibold leading-4 tracking-[0.2px] text-gray-500">
              -
            </span>
          )}

          {cellData.type === 'image' && (
            <div className="w-[30px]">
              <img
                className="size-[25px]"
                src={cellData.url}
                alt={cellData.value}
              />
            </div>
          )}

          {isUploadTable && cellData.type === 'action' && (
            <div
              className={` mx-0 flex w-[180px] flex-row items-center justify-center space-x-2 ${
                cellData.visible ? 'flex ' : 'hidden'
              }`}
            >
              <img
                className=" size-[35px]"
                src="/assets/images/warning-table.svg"
                alt="warning"
              />
              <div className="w-[130px] overflow-x-clip">
                <ListBoxTable
                  scrollPosition={scrollPosition}
                  refId={rowIndex}
                  placeholder="Take Action"
                  isInTable={isCondensedTable}
                  items={cellData.selectList}
                  selectedValue={cellData.value}
                  hasValue={cellData.value !== null}
                  onSelectionChange={(selectedValue) => {
                    handleRowActionChange(index, rowIndex, selectedValue);
                  }}
                  name="select"
                />
              </div>
            </div>
          )}
        </div>
      </td>
    );
  };

  // Effects

  useEffect(() => {
    if (truncateDivRef.current) {
      const divWidth: any = truncateDivRef.current.offsetWidth;
      const needsTruncate = divWidth < 300;
      setShouldTruncate(needsTruncate);
    }
  }, [truncateDivRef]);

  useEffect(() => {
    if (sorting) {
      setRows([...data].sort(sortValues));
    } else {
      setRows([...data]);
    }
  }, [sorting]);

  useEffect(() => {
    if (sort.column !== null) {
      setSorting({ column: sort.column, order: sort.order });
    } else {
      setRows(data);
    }
  }, [data]);

  useEffect(() => {
    let newCheckBoxState;
    let selected = 0;
    let unSelected = rows?.length;
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    rows?.length > 0 &&
      rows?.forEach((row: any) => {
        if (row?.selected) selected += 1;
        else unSelected -= 1;
      });
    if (selected === rows?.length) {
      newCheckBoxState = CheckBoxStates.Checked;
    } else if (unSelected === 0) {
      newCheckBoxState = CheckBoxStates.Empty;
    } else {
      newCheckBoxState = CheckBoxStates.Indeterminate;
    }
    setHeaderCheckBoxState(newCheckBoxState);
  }, [rows]);

  useEffect(() => {
    if (hasPagination) {
      if (rows.length > 0) {
        const totalItems = rows.length;
        const maxPage = Math.ceil(totalItems / itemsPerPage);
        if (currentPage > maxPage) {
          setCurrentPage(maxPage);
          return;
        }
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(currentPage * itemsPerPage, rows.length);
        setCurrentItem(rows.slice(startIndex, endIndex));
      } else {
        setCurrentItem([]);
      }
    } else {
      setCurrentItem(rows);
    }
  }, [rows, currentPage]);

  useEffect(() => {
    if (hasPagination) {
      if (rows.length > 0) {
        setCurrentPage(1);
      }
    }
  }, [data]);

  return (
    <div className="relative size-full min-w-fit">
      {/* {isDashTable && (
        <div className="absolute z-10 h-14 w-[88vw] border-2 border-red-100 bg-white-200" />
      )} */}
      <div className="min-h-[90%]">
        <table className="w-full table-auto divide-y divide-lightgray-100">
          <thead
            className={`sticky top-0 z-10 w-full ${
              isDashTable ? 'bg-white-200' : 'bg-gray-300'
            }`}
          >
            {renderHeader()}
          </thead>
          <tbody className="divide-y divide-lightgray-100">
            {currentItems?.length > 0 &&
              currentItems?.map((rowData: any, rowIndex: any) => (
                <tr key={rowIndex}>
                  {rowData?.components.map((cellData: any, cellIndex: number) =>
                    renderValueCell(
                      rowData.selected,
                      cellData,
                      cellIndex,
                      rowIndex,
                    ),
                  )}
                  {canMarkFav && (
                    <td>
                      <button
                        type="button"
                        className="mx-[2px] size-[24px] cursor-pointer"
                        onClick={() => {
                          handleRowFavChange(
                            rowIndex,
                            rowData.id,
                            rowData?.isFavourite,
                          );
                        }}
                      >
                        {rowData?.isFavourite ? (
                          <img
                            src="/assets/images/star-active.svg"
                            alt="clear"
                          />
                        ) : (
                          <img src="/assets/images/star.svg" alt="clear" />
                        )}
                      </button>
                    </td>
                  )}
                  {isEdit && (
                    <td>
                      <button
                        type="button"
                        className="size-[25px] cursor-pointer"
                        onClick={() => {
                          if (onEdit) {
                            onEdit(rowData.id);
                          }
                        }}
                      >
                        <img src="/assets/images/edit.svg" alt="clear" />
                      </button>
                    </td>
                  )}
                  {isView && isEyeVisible && (
                    <td>
                      <button
                        type="button"
                        className="mx-2 size-[25px] cursor-pointer"
                        onClick={() => {
                          if (EyeFunction) {
                            EyeFunction(rowData.id);
                          }
                        }}
                      >
                        <img src="/assets/images/eye.svg" alt="clear" />
                      </button>
                    </td>
                  )}
                  {isView && isDownloadVisible && (
                    <td>
                      <button
                        type="button"
                        className="mx-2 size-[25px] cursor-pointer"
                        onClick={() => {
                          console.log('download');
                        }}
                      >
                        <img src="/assets/images/download.svg" alt="clear" />
                      </button>
                    </td>
                  )}
                  {enableOptions && (
                    <td>
                      <div className="mx-2 h-[20px]">
                        <Dropdown
                          showMenu={
                            isTourOpen && rowIndex === 0 ? true : undefined
                          }
                          menuTitle={`<img src='/assets/images/menu.svg'>`}
                          data={menuData}
                          refId={rowData.id}
                          scrollPosition={scrollPosition}
                        />
                      </div>
                    </td>
                  )}
                  {enabledCross && (
                    <td>
                      <button
                        type="button"
                        className="mx-2 size-[25px] cursor-pointer"
                        onClick={() => removeRow(rowIndex)}
                      >
                        <img src="/assets/images/redcross.svg" alt="clear" />
                      </button>
                    </td>
                  )}
                </tr>
              ))}
          </tbody>
        </table>
        {currentItems?.length === 0 && (
          <div className="relative top-[7vh] ml-[45%] inline-block items-center justify-center">
            <span className="font-sans text-sm font-normal text-gray-400 ">
              No Data Available
            </span>
          </div>
        )}
      </div>
      {currentItems?.length !== 0 && hasPagination && (
        <div className="sticky bottom-0 flex w-full items-center justify-center bg-white-200 px-3 pb-2 pt-4">
          <Pagination
            itemsPerPage={itemsPerPage}
            totalItems={rows.length}
            currentPage={currentPage}
            paginate={paginate}
          />
        </div>
      )}
      <WarningDialog onConfirm={handleConfirm} />
    </div>
  );
};
export default Table;
