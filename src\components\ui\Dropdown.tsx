/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Menu, Transition } from '@headlessui/react';
import React, { useEffect, useRef } from 'react';

interface RegularData {
  option: string;
  clickFn(key: any): any;
  disabled: boolean;
}

interface SubsectionData {
  subSection: {
    data: RegularData[];
    title: string;
  };
}

interface DropdownProps {
  menuTitle: any;
  data: RegularData[] | SubsectionData[] | (RegularData[] & SubsectionData[]);
  refId?: any;
  scrollPosition?: any;
  showMenu?: boolean;
}

const MenuItem: React.FC<RegularData> = ({ option, clickFn, disabled }) => (
  <Menu.Item>
    {({ active }) => (
      <span
        onClick={clickFn}
        className={` ${
          active ? 'bg-blue-200/10 text-blue-200' : 'text-gray-500'
        } ${
          disabled ? 'pointer-events-none opacity-80' : ''
        }   disabled: group flex w-full cursor-pointer items-center px-4 py-2 text-sm`}
      >
        {option}
      </span>
    )}
  </Menu.Item>
);

const SubSection: React.FC<SubsectionData> = ({ subSection }) => {
  return (
    <>
      <div className="group flex w-full items-center px-4 py-2 text-sm text-gray-400">
        {subSection.title}
      </div>

      {subSection.data?.map((subObj) => {
        return (
          <MenuItem
            option={subObj.option}
            key={`menu-item-${subSection.title}-${subObj.option}`}
            clickFn={subObj.clickFn}
            disabled={subObj.disabled ? subObj.disabled : false}
          />
        );
      })}
    </>
  );
};

export const Dropdown: React.FC<DropdownProps> = ({
  menuTitle,
  data,
  refId,
  showMenu,
  scrollPosition,
}) => {
  // Essentials
  const menuButtonRef: any = useRef(null);
  const menuItemsRef: any = useRef(null);

  // States
  const [showDefault, setShowDefault] = React.useState(showMenu || undefined);
  const [openBottom, setOpenBottom] = React.useState(false);

  // Methods
  const calculatePosition = () => {
    const buttonPosition = menuButtonRef.current.getBoundingClientRect();
    if (buttonPosition && scrollPosition / 16 + 490 < buttonPosition.bottom) {
      setOpenBottom(true);
    } else {
      setOpenBottom(false);
    }
  };

  const handleWindowClick = (event: MouseEvent) => {
    if (menuItemsRef.current && !menuItemsRef.current.contains(event.target)) {
      setShowDefault(undefined);
    }
  };

  // Effects
  useEffect(() => {
    calculatePosition();
    // Recalculate the position when the window is resized
    window.addEventListener('resize', calculatePosition);
    window.addEventListener('click', handleWindowClick);
    return () => {
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('click', handleWindowClick);
    };
  }, [refId, data, menuButtonRef]);
  return (
    <div className=" z-20" ref={menuButtonRef}>
      <Menu>
        <Menu.Button
          className="flex min-w-[20px] justify-center"
          dangerouslySetInnerHTML={{ __html: menuTitle }}
        />
        <Transition
          show={showDefault}
          className="relative z-20"
          enter="transition duration-100 ease-out"
          enterFrom="transform scale-95 opacity-0"
          enterTo="transform scale-100 opacity-100"
          leave="transition duration-75 ease-out"
          leaveFrom="transform scale-100 opacity-100"
          leaveTo="transform scale-95 opacity-0"
        >
          <Menu.Items
            id="menuItems"
            ref={menuItemsRef}
            className={`absolute ${
              openBottom ? 'bottom-8' : ''
            } right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white-200 shadow-lg ring-1 ring-black/5 focus:outline-none`}
          >
            <div className=" ">
              {data.map((obj, index) => {
                const { option, clickFn, disabled } = obj as RegularData;
                if ((obj as RegularData).option) {
                  return (
                    <MenuItem
                      option={option}
                      disabled={disabled || false}
                      key={`menu-item-${option}`}
                      clickFn={() => {
                        clickFn(refId);
                        setShowDefault(undefined);
                      }}
                    />
                  );
                }
                if ((obj as SubsectionData).subSection) {
                  const { subSection } = obj as SubsectionData;
                  return (
                    <div
                      key={`subSection-wrapper-${subSection.title}`}
                      className={`border-lightgray-100  text-xs ${
                        index > 0 ? 'border-t' : ''
                      }`}
                    >
                      <SubSection subSection={subSection} />
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </Menu.Items>
        </Transition>
      </Menu>
    </div>
  );
};
