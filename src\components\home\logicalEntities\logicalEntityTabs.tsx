'use client';

import React, { useState } from 'react';

import CustomHierarchy from '@/components/tables/logicalEntities/customHierarchy';
import CustomMapping from '@/components/tables/logicalEntities/customMapping';
import LogicalEntityTable from '@/components/tables/logicalEntities/logicalEntityTable';

import { Tabs } from '../../ui/Tabs';

const LogicalEntityTabs: React.FC = () => {
  // States
  const [selectedTab, setSelectedTab] = useState(0);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Constant
  const tabData: any = [
    {
      title: 'Logical Entities',
      component: <LogicalEntityTable scrollPosition={scrollPosition} />,
    },
    {
      title: 'Custom Mapping',
      component: <CustomMapping mode="Edit" />,
    },
    {
      title: 'Custom Hierarchy',
      component: <CustomHierarchy mode="Edit" />,
    },
  ];

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  return (
    <div
      className="flex h-fit min-h-[80vh] w-full flex-col overflow-hidden rounded  bg-white-200 p-4 pb-0"
      onScroll={handleScroll}
    >
      <Tabs
        data={tabData}
        selectedIndex={selectedTab}
        onChange={(e: number) => {
          setSelectedTab(e);
        }}
      />
    </div>
  );
};

export default LogicalEntityTabs;
