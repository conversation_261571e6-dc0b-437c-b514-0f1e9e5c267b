/* eslint-disable jsx-a11y/iframe-has-title */

'use client';

import React from 'react';

import ConfigurationCockpit from '@/components/landing/configurationCockpit';

const ConfigurationCockpitPage: React.FC = () => {
  return <ConfigurationCockpit />;
  // return (
  //   <div>
  //     <iframe
  //       className="h-screen w-full"
  //       src="https://apps.powerapps.com/play/e/default-1806032f-a719-453e-8e14-5a5385413df4/a/5e22130d-9300-4762-97a5-b83ca747faa9?tenantId=1806032f-a719-453e-8e14-5a5385413df4&hint=f0719f63-2535-4789-b135-c679f8b7e63c&sourcetime=1709285061602"
  //       allow="geolocation; microphone; camera"
  //     />
  //   </div>
  // );
};

export default ConfigurationCockpitPage;
