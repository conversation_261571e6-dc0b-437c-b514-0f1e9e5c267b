/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useEffect, useState } from 'react';
import { Tooltip } from 'react-tooltip';

import { formatDateString } from '@/utils/utilityHelper';

import Pagination from './Pagination';

const SimpleTableWithoutBorder: React.FC<{
  hasPagination?: boolean;
  isAutoTable?: boolean;
  headers: any[];
  tableRows: any[];
}> = ({ headers, isAutoTable, tableRows, hasPagination }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [currentItems, setCurrentItem] = useState<any>([]);
  const paginate = (pageNumber: any) => setCurrentPage(pageNumber);
  const [rows, setRows] = useState<any>(tableRows);
  const [sorting, setSorting] = useState<{
    header: string;
    order: 'asc' | 'desc';
  } | null>(null);
  const toggleSorting = (header: string) => {
    if (!sorting) {
      setSorting({ header, order: 'asc' });
    } else if (sorting.header === header) {
      setSorting({
        ...sorting,
        order: sorting.order === 'asc' ? 'desc' : 'asc',
      });
    } else {
      setSorting({ header, order: 'asc' });
    }
  };

  const sortValues = (a: any, b: any) => {
    if (!sorting) return 0;
    const { header, order } = sorting;
    const valueA = a[header]?.data;
    const valueB = b[header]?.data;
    if (order === 'asc') {
      if (valueA < valueB) return -1;
      if (valueA > valueB) return 1;
    } else {
      if (valueA > valueB) return -1;
      if (valueA < valueB) return 1;
    }
    return 0;
  };

  useEffect(() => {
    if (sorting) {
      setRows([...tableRows].sort(sortValues));
    } else {
      setRows([...tableRows]);
    }
  }, [sorting]);

  useEffect(() => {
    setRows(tableRows);
  }, [tableRows]);

  useEffect(() => {
    if (hasPagination) {
      if (rows.length > 0) {
        const totalItems = rows.length;
        const maxPage = Math.ceil(totalItems / itemsPerPage);
        if (currentPage > maxPage) {
          setCurrentPage(maxPage);
          return;
        }
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(currentPage * itemsPerPage, rows.length);
        setCurrentItem(rows.slice(startIndex, endIndex));
      } else {
        setCurrentItem([]);
      }
    } else {
      setCurrentItem(rows);
    }
  }, [rows, currentPage]);

  return (
    <div className="relative h-full w-full min-w-fit">
      <div className="min-h-[90%]">
        {currentItems?.length !== 0 && (
          <table
            className={` has-table-border w-full divide-y-[1px] divide-lightgray-100 ${
              isAutoTable ? 'table-auto' : 'table-fixed'
            }`}
          >
            <thead className={`sticky top-0 z-10 w-full bg-white-200 `}>
              <tr className="">
                {headers.map((header: any, index: any) => (
                  <th
                    key={index}
                    className={`${
                      isAutoTable && index === 0 ? 'w-[300px]' : 'min-w-[100px]'
                    } ${
                      index === 0 ? 'sticky left-0 z-20 bg-white-200' : ''
                    } p-3 py-4 text-left`}
                  >
                    {typeof header === 'object' ? (
                      <div className="flex flex-row space-x-3">
                        {!header.hasSorting && (
                          <span className="font-sans text-sm  font-normal capitalize leading-4 text-gray-400">
                            {header.data}
                          </span>
                        )}
                        {header.hasSorting && (
                          <button
                            type="button"
                            onClick={() => toggleSorting(header.data)}
                            className="flex flex-row items-center justify-between space-x-2"
                          >
                            <span className="font-sans text-sm  font-normal capitalize leading-4 text-gray-400">
                              {header.data}
                            </span>
                            {sorting?.order === 'asc' &&
                              sorting.header === header.data && (
                                <img
                                  className="cursor-pointer"
                                  src="/assets/images/sort.svg"
                                  alt="sort"
                                />
                              )}
                            {sorting?.order === 'desc' &&
                              sorting.header === header.data && (
                                <img
                                  className="rotate-180 cursor-pointer"
                                  src="/assets/images/sort.svg"
                                  alt="sort"
                                />
                              )}
                            {sorting?.header !== header.data && (
                              <img
                                className="cursor-pointer"
                                src="/assets/images/sort.svg"
                                alt="sort"
                              />
                            )}
                          </button>
                        )}
                      </div>
                    ) : (
                      <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                        {header}
                      </span>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y-[1px] divide-lightgray-100">
              {currentItems.map((row: any, rowIndex: number) => (
                <tr key={rowIndex}>
                  {headers.map((header, colIndex) => (
                    <td
                      key={colIndex}
                      className={` px-3 py-4 text-left font-sans text-sm font-semibold  capitalize text-gray-500`}
                    >
                      {typeof row[header.data] === 'object' ? (
                        <div className="flex flex-row items-center justify-between">
                          {row[header.data].isLink ? (
                            <span className=" cursor-pointer font-sans text-sm font-semibold leading-4 tracking-[0.2px] text-blue-200">
                              {row[header.data].data}
                            </span>
                          ) : (
                            <span
                              className={`font-sans text-sm font-semibold leading-4 tracking-[0.2px] text-gray-500 ${
                                formatDateString(row[header.data].data)
                                  ?.length > 50
                                  ? 'truncateData'
                                  : 'pointer-events-none text-left'
                              }`}
                            >
                              {row[header.data].data
                                ? row[header.data].data
                                : '-'}
                            </span>
                          )}
                          {row[header.data].tooltip && (
                            <div className="mx-1 h-[18px] w-[18px]">
                              <img
                                data-tooltip-id="info-tooltip"
                                data-tooltip-html={row[header.data].tooltip}
                                className="h-[18px] w-[18px] cursor-pointer"
                                src="/assets/images/information.svg"
                                alt="tooltip"
                              />
                            </div>
                          )}
                        </div>
                      ) : (
                        row[header]
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
        {currentItems?.length === 0 && (
          <div className="relative top-[10vh] ml-[45%] inline-block items-center justify-center">
            <span className="font-sans text-sm font-normal text-gray-400 ">
              No Data Available
            </span>
          </div>
        )}
        <Tooltip
          className="info-tooltip"
          id="info-tooltip"
          place="top"
          variant="info"
          positionStrategy="fixed"
        />
      </div>
      {currentItems?.length !== 0 && hasPagination && (
        <div className="sticky bottom-0 z-50 flex w-full items-center justify-center bg-white-200 px-3 pb-2 pt-4">
          <Pagination
            itemsPerPage={itemsPerPage}
            totalItems={rows.length}
            currentPage={currentPage}
            paginate={paginate}
          />
        </div>
      )}
    </div>
  );
};

export default SimpleTableWithoutBorder;
