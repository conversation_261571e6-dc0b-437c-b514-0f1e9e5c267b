/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-nested-ternary */
import { useRouter } from 'next/navigation';
import monthlyData from 'public/testdata/dataQuality/dataAvailabilityMonthly.json';
import quarterlyData from 'public/testdata/dataQuality/dataAvailabilityQuarterly.json';
import yearlyData from 'public/testdata/dataQuality/dataAvailabilityYearly.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import SimpleTable from '@/components/ui/SimpleTable';
import { monthStringList, quarterStringList } from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

const DataAvailability: React.FC<{ selectedYear?: any }> = ({
  selectedYear,
}) => {
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const [selectedYearForView, setSelectedYearForView] =
    useState<any>(selectedYear);
  const [selectedTab] = useState(
    Number(localService.getItem('selectedDataQualityTab')) || 0,
  );
  const [tableData, setTableData] = useState<any>(null);

  const calculateScore = (expected: any, received: any) => {
    if (expected === 0) {
      // No Due
      return 1;
    }
    if (expected > 0 && received === 0) {
      // Unavailable
      return 2;
    }
    if (received < expected) {
      // Partial
      return 3;
    }
    if (expected > 0 && (received === expected || received > expected)) {
      // Available
      return 4;
    }
    return 1;
  };

  const processData = (data: any, headers: any) => {
    const result: any = [];
    data.forEach((entityData: any, entityIndex: number) => {
      const entityName: any = headers[entityIndex];
      entityData.forEach((dataset: any) => {
        const datasetName = dataset.dataset;
        const { expectedFiles } = dataset;
        const receivedFiles = dataset.filesReceived.length;
        const score = calculateScore(expectedFiles, receivedFiles);

        // find if this dataset already exists in the result list
        const existingEntry = result.find(
          (item: any) => item.Dataset.data === datasetName,
        );
        if (existingEntry) {
          existingEntry[entityName] = {
            score,
            data:
              score === 4
                ? 'Available'
                : score === 3
                ? 'Partial'
                : score === 2
                ? 'Unavailable'
                : 'Not Due',
            isLink: false,
            tooltip:
              score === 3
                ? `Expected ${expectedFiles} Received ${receivedFiles}`
                : '',
            fileData: {
              receivedFiles: dataset.filesReceived,
              expectedFiles: dataset.expectedFiles,
              year: dataset.year,
            },
          };
        } else {
          const newEntry = {
            Dataset: { data: datasetName },
            [entityName]: {
              score,
              data:
                score === 4
                  ? 'Available'
                  : score === 3
                  ? 'Partial'
                  : score === 2
                  ? 'Unavailable'
                  : 'Not Due',
              isLink: false,
              tooltip:
                score === 3
                  ? `Expected ${expectedFiles} Received ${receivedFiles}`
                  : '',
              fileData: {
                receivedFiles: dataset.filesReceived,
                expectedFiles: dataset.expectedFiles,
                year: dataset.year,
              },
            },
          };
          // Add default values for other headers
          headers.forEach((m: any) => {
            if (m !== entityName) {
              newEntry[m] = {
                score: 1,
                data: 'Not Due',
                isLink: false,
                tooltip: '',
                fileData: {
                  receivedFiles: [],
                  expectedFiles: 0,
                  year: dataset.year,
                },
              };
            }
          });
          result.push(newEntry);
        }
      });
    });
    return result;
  };

  const formatMonthTable = (data: any) => {
    const months = monthStringList;
    const tempTableData: any = {
      headers: monthlyData.headers,
      rows: processData(data, months),
    };
    setTableData(tempTableData);
  };

  const formatQuarterlyTable = (data: any) => {
    const quarter = quarterStringList;
    const tempTableData: any = {
      headers: quarterlyData.headers,
      rows: processData(data, quarter),
    };
    setTableData(tempTableData);
  };

  const formatYearlyTable = (data: any) => {
    const year = [String(selectedYearForView)];
    const tempTableData: any = {
      headers: [{ data: 'Dataset' }, { data: year[0] }],
      rows: processData(data, year),
    };
    setTableData(tempTableData);
  };

  const fetchData = async (route: any) => {
    try {
      const url = `
        ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getBsmDataAvailabilityMonthly.url}${route} 
        `;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            if (selectedTab === 4) {
              formatMonthTable(res.data);
            }
            if (selectedTab === 5) {
              formatQuarterlyTable(res.data);
            }
            if (selectedTab === 6) {
              formatYearlyTable([res.data]);
            }
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const getTableData = (selectedTabIndex: any) => {
    if (Number(selectedTabIndex) === 4) {
      if (!isTourOpen) {
        fetchData(`${selectedYearForView}/months`);
      } else {
        const formattedData: any = monthlyData;
        setTableData(formattedData);
      }
    }
    if (Number(selectedTabIndex) === 5) {
      if (!isTourOpen) {
        fetchData(`${selectedYearForView}/quarters`);
      } else {
        const formattedData: any = quarterlyData;
        setTableData(formattedData);
      }
    }
    if (Number(selectedTabIndex) === 6) {
      if (!isTourOpen) {
        fetchData(`${selectedYearForView}`);
      } else {
        const formattedData: any = yearlyData;
        setTableData(formattedData);
      }
    }
  };

  useEffect(() => {
    setSelectedYearForView(selectedYear);
  }, [selectedYear]);

  useEffect(() => {
    getTableData(selectedTab);
  }, [selectedYearForView]);
  return (
    <div className="flex h-[calc(100vh-250px)] w-full flex-col space-y-4 overflow-auto px-2 py-4 xl:h-[calc(100vh-210px)]">
      <div className="flex h-full w-full flex-row space-x-4">
        <div className="h-full w-full">
          {tableData && (
            <div className="h-full w-full overflow-auto">
              <SimpleTable
                selectedTab={selectedTab}
                isAvailabilityTable={selectedTab === 4}
                isMonthTable={selectedTab === 4}
                headers={tableData.headers}
                tableRows={tableData.rows}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataAvailability;
