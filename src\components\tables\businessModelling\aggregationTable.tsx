/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable import/no-cycle */
import aggregateData from 'public/testdata/businessModelling/viewBusinessSchema.json';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Link } from '@/components/ui/Link';
import ListBox from '@/components/ui/ListBox';
import Table from '@/components/ui/Table';
import { createMenuName } from '@/constants/menuSvg';
import {
  setAggregateDataType,
  setAggregateTo,
  setAggregationTable,
} from '@/slices/businessModelCrudSlice';

const AggregateTable: React.FC<{
  attributesList: any;
  mode: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const dispatch = useDispatch();

  // Constants
  const dataTypeDropdownItems = [
    { name: 'INT', id: 'INT' },
    { name: 'LON<PERSON>', id: 'INT' },
  ];
  const header: any = [
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Data Type',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Description',
      optionsEnabled: false,
      options: [],
    },
  ];

  // States
  const [data, setData]: any = useState([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Selectors
  const aggregateTo = useSelector(
    (state: any) => state.businessModelCrud.aggregateTo,
  );
  const aggregateDataType = useSelector(
    (state: any) => state.businessModelCrud.aggregateDataType,
  );
  // const aggregationTable = useSelector(
  //   (state: any) => state.businessModelCrud.aggregationTable,
  // );

  // Methods
  const handleRowChange = (rows: any) => {
    setData(rows);
    dispatch(setAggregationTable(rows));
  };

  const addRow = () => {
    const newRow = {
      id: `New Entry ${data.length + 1}`,
      selected: true,
      components: [
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Select Attribute',
          selectList: props.attributesList,
        },
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: ' Select Datatype',
          selectList: [{ name: 'INT' }, { name: 'LONG' }],
        },
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'input',
          placeholder: 'Date when the payment is completed',
        },
      ],
    };
    const updatedData = [newRow, ...data];
    setData(updatedData);
    dispatch(setAggregationTable(updatedData));
    handleRowChange(updatedData);
  };

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  // Effects
  useEffect(() => {
    setData(data);
  }, [data]);
  return (
    <div className="flex h-full flex-col">
      <div className=" flex w-full flex-row items-center justify-between">
        {props.mode !== 'View' ? (
          <span className="font-sans text-base font-semibold text-blueGray-300">
            Select Attributes to Aggregate
          </span>
        ) : (
          <span className="font-sans text-base font-semibold text-blueGray-300">
            Attributes to Aggregate
          </span>
        )}{' '}
        {props.mode !== 'View' && (
          <Link content={createMenuName('Add Attribute')} onClick={addRow} />
        )}
      </div>
      <div
        className="mt-4 h-[75%] w-full overflow-auto bg-gray-300"
        onScroll={handleScroll}
      >
        {data.length === 0 ? (
          <div className="flex h-full w-full items-center justify-center">
            <span className="font-sans text-sm font-medium text-gray-400">
              No Attributes Available
            </span>
          </div>
        ) : (
          <Table
            isCondensedTable
            isDashTable={false}
            header={header}
            data={data}
            scrollPosition={scrollPosition}
            enableOptions={false}
            isView={props.mode === 'View'}
            enabledCross={props.mode !== 'View'}
            onRowChange={handleRowChange}
          />
        )}
      </div>
      <div className="mt-2 rounded border-[1px] border-lightgray-100 bg-white-200">
        {props.mode !== 'View' ? (
          <div className="flex w-full flex-row items-center space-x-6 p-2">
            <span className="font-sans text-sm font-medium uppercase text-gray-400">
              Aggregate To
            </span>
            <div className="w-[216px]">
              <ListBox
                customStyle="min-h-[38px] py-[6px]"
                scrollPosition={scrollPosition}
                items={props.attributesList}
                name="aggregationTo"
                isPreselected={false}
                placeholder="Select Attribute"
                onSelectionChange={(selectedValue) => {
                  dispatch(setAggregateTo(selectedValue));
                }}
                selectedItems={[aggregateTo]}
              />
            </div>
            <div className="flex w-[150px] flex-col">
              {/* <span className="font-sans text-sm font-medium uppercase text-gray-400">
                  Data Type
                </span> */}
              <ListBox
                customStyle="min-h-[38px] py-[6px]"
                scrollPosition={scrollPosition}
                items={dataTypeDropdownItems}
                onSelectionChange={(selectedValue) => {
                  dispatch(setAggregateDataType(selectedValue));
                }}
                isPreselected={false}
                placeholder="Select DataType"
                name="DataType"
                selectedItems={[aggregateDataType]}
              />
            </div>
          </div>
        ) : (
          <div className="flex flex-row items-center space-x-11 p-2">
            <span className="font-sans text-sm font-medium uppercase text-gray-400">
              Aggregating To
            </span>
            <div className="flex flex-col space-y-2">
              <span className="font-sans text-xs font-medium text-gray-400">
                Attribute Name
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {aggregateData.aggregation.aggregatingTo.name}
              </span>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="font-sans text-xs font-medium text-gray-400">
                Data Type
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {aggregateData.aggregation.aggregatingTo.type}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AggregateTable;
