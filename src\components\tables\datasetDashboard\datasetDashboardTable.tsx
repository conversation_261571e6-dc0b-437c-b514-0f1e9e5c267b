'use client';

import moment from 'moment';
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/dataset/datasetDashboard.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@/components/ui/Button';
import ListBoxTable from '@/components/ui/ListBoxTable';
import Table from '@/components/ui/Table';
import { frequencylist } from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import { setDatasets } from '@/slices/datasetCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface DatasetDashboardTableProps {
  onDatasetCountChange: (AllData: any) => void;
}

const DatasetDashboardTable: React.FC<DatasetDashboardTableProps> = ({
  onDatasetCountChange,
}) => {
  // essentials
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // States
  const [filteredData, setFilteredData] = useState([]);
  const [allData, setAllData] = useState<any>([]);
  const enableOptions: boolean = true;
  const filters: any = localService.getData('datasetDashboardFilters') || '';
  // const [RowID, setRowID] = useState(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [selectedFrequency, setSelectedFrequency] = useState<any>(
    filters?.selectedFrequency ? filters.selectedFrequency : 'All',
  );
  const [selectedDataset, setSelectedDataset] = useState<any>(
    filters?.selectedDataset ? filters.selectedDataset : 'All',
  );
  const [datasetListForFilter, setDatasetListForFilter] = useState<any>([]);
  const [frequencyList] = useState<any>([{ name: 'All' }, ...frequencylist]);

  // Constants
  const header: any = [
    {
      title: 'Dataset Name',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Frequency',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'No. of Files',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Last Upload',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];

  // Methods

  const menuData: any = [
    {
      option: 'View Files',
      clickFn: (key: any) => {
        const selectedData: any = filteredData.filter(
          (item: any) => item.id === key,
        );
        const parameter: any = {
          id: key,
        };
        const filter: any = {
          selectedYear: 'All',
          selectedMonth: 'All',
          selectedQuarter: 'All',
          selectedSource: selectedData[0].components[0].value,
          selectedFileIsReplaced: 'All',
        };
        localStorage.setItem('fileUploadFilters', JSON.stringify(filter));
        const queryString = new URLSearchParams(parameter).toString();
        localStorage.setItem('fileUploadTab', '1');
        router.push(`/fileUpload?&${queryString}`);
      },
    },
    {
      option: 'Upload File',
      clickFn: (key: any) => {
        const parameter: any = {
          id: key,
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/fileUpload?${queryString}`);
      },
    },
  ];

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const applyFilter = (tableData: any) => {
    const tempFilter: any = {
      selectedDataset,
      selectedFrequency,
    };
    localStorage.setItem('datasetDashboardFilters', JSON.stringify(tempFilter));
    if (tableData.length !== 0) {
      const tempSelectedSource: any =
        selectedDataset === 'All' ? '' : selectedDataset;
      const tempSelectedFrequency: any =
        selectedFrequency === 'All' ? '' : selectedFrequency;
      const filter: { [key: number]: any } = {};
      if (tempSelectedSource !== '')
        filter[0] = tempSelectedSource === ' ' ? '' : tempSelectedSource;
      if (tempSelectedFrequency !== '') filter[1] = tempSelectedFrequency;
      const tempFilteredData = tableData.filter((item: any) =>
        Object.entries(filter).every(
          ([key, value]) => item.components[key].value === value,
        ),
      );
      setFilteredData(tempFilteredData);
    } else {
      setFilteredData([]);
    }
  };

  const resetFilter = () => {
    const tempFilter: any = {
      selectedDataset: 'All',
      selectedFrequency: 'All',
    };
    localStorage.setItem('datasetDashboardFilters', JSON.stringify(tempFilter));
    setSelectedDataset('All');
    setSelectedFrequency('All');
    setFilteredData(allData);
  };

  const resolveData = (data: any) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data.map((row) => {
      const components = [
        {
          value: row.datasetName,
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.frequency ? row.frequency : '',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        },
        {
          value: row.noOfFiles,
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        },
        {
          value: row.lastUpload
            ? moment(row.lastUpload).format('YYYY-MM-DD')
            : 'N/A',
          userAvatarLink: null,
          disabled: false,
          type: 'text',
        },
      ];
      return {
        id: row.id,
        components,
      };
    });
  };

  useEffect(() => {
    const fetchSource = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsm +
          ApiUtilities.apiPath.getBsmDataSets.url
        }`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const uniqueDatasets = Array.from(
                new Set(res.data.map((obj: any) => obj.datasetName)),
              ).map((datasetName) => ({ name: datasetName }));

              setDatasetListForFilter([{ name: 'All' }, ...uniqueDatasets]);
              const formattedData: any = resolveData(res.data);
              setAllData(formattedData);
              applyFilter(formattedData);
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (!isTourOpen) {
      fetchSource();
    } else {
      const formattedData: any = eData;
      setFilteredData(formattedData);
      onDatasetCountChange(String(filteredData.length));
      setDatasets(formattedData);
    }
  }, []);

  useEffect(() => {
    if (filteredData.length > 0) {
      setFilteredData(filteredData);
      onDatasetCountChange(String(filteredData.length));
    }
  }, [filteredData]);

  return (
    <div className=" dataset-table flex size-full flex-col">
      <div className="flex w-full items-end justify-between px-2 py-3 ">
        <div className="flex flex-row space-x-4">
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Select Dataset
            </span>
            <div className="mt-3 w-[25vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                isInTable
                hasValue
                items={datasetListForFilter}
                placeholder="Select"
                selectedValue={selectedDataset}
                onSelectionChange={(selectedValue) => {
                  setSelectedDataset(selectedValue.name);
                }}
                name="dataset"
              />
            </div>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Select Frequency
            </span>
            <div className="mt-3 w-[15vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                isInTable
                hasValue
                items={frequencyList}
                placeholder="Select"
                selectedValue={selectedFrequency}
                onSelectionChange={(selectedValue) => {
                  setSelectedFrequency(selectedValue.name);
                }}
                name="frequency"
              />
            </div>
          </div>
        </div>
        <div className=" flex flex-row space-x-2 ">
          <Button
            intent="primary"
            disabled={selectedFrequency === '' && selectedDataset === ''}
            onClick={() => applyFilter(allData)}
          >
            Apply
          </Button>
          <Button intent="secondary" onClick={resetFilter}>
            Reset
          </Button>
        </div>
      </div>
      <div className="size-full overflow-auto" onScroll={handleScroll}>
        <Table
          hasPagination
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          scrollPosition={scrollPosition}
          enableOptions={enableOptions}
        />
      </div>
    </div>
  );
};

export default DatasetDashboardTable;
