'use client';

import router from 'next/router';
import eData from 'public/testdata/insight/dashboard.json';
import { useEffect } from 'react';

import InsightDashboard from '@/components/home/<USER>/insightDashboard';

export default function Page({ params }: { params: { slug: string } }) {
  const dashboardsAvailable = [
    'enterprise',
    'customerFocus',
    'supplierFocus',
    'financialFocus',
  ];

  const dashboardMap: any = eData;

  useEffect(() => {
    if (!dashboardsAvailable.includes(params.slug)) {
      router.push('/home');
    }
  });

  return (
    <InsightDashboard
      dashboards={dashboardMap[params.slug]}
      dashboardName={params.slug}
    />
  );
}
