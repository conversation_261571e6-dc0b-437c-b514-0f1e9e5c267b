'use client';

import React, { useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON><PERSON>,
  BarChart3,
  DollarSign,
  Calculator,
  TrendingUp,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

import { Button } from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface DistributionProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onDistributionComplete?: (data: any) => void;
}

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const Distribution: React.FC<DistributionProps> = ({ onNext, uploadedFiles, onDistributionComplete }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);

  const [selectedView, setSelectedView] = useState('summary');
  const [distributionResult, setDistributionResult] = useState<any>(null);
  const [pivotResults, setPivotResults] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  const generatePivotTables = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproDistributionPivot?.url || '/distribution/pivot'}`;
    return await apiService.genproPostRequest(url, { workflow_id: workflowId });
  };

  const calculateDistribution = async (workflowId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproDistributionCalculate?.url || '/distribution/calculate'}`;
    return await apiService.genproPostRequest(url, { workflow_id: workflowId });
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  const showInfo = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'info', title: 'Info', content: message }));
  };

  // Mock data for demonstration
  const distributionData = [
    {
      finalSMC: 'BSM HEL',
      finalVessel: 'Vessel X',
      totalBF: 33333,
      totalBFIncluding50: 35000,
      bfPercentage: 26.7,
      distributionAmount: 33375,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM CYP',
      finalVessel: 'Vessel Y',
      totalBF: 50000,
      totalBFIncluding50: 52500,
      bfPercentage: 40.0,
      distributionAmount: 50000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'SEACHEF',
      finalVessel: 'SEACHEF',
      totalBF: 16667,
      totalBFIncluding50: 18750,
      bfPercentage: 13.3,
      distributionAmount: 16625,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM SG',
      finalVessel: 'Vessel C',
      totalBF: 15000,
      totalBFIncluding50: 15750,
      bfPercentage: 12.0,
      distributionAmount: 15000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'Frontline',
      finalVessel: 'Frontline',
      totalBF: 10000,
      totalBFIncluding50: 10000,
      bfPercentage: 0,
      distributionAmount: 0,
      excludeFromDistribution: true
    }
  ];

  const seachefSplitData = [
    {
      smc: 'BSM CHI',
      vessel: 'SEACHEF A',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SMC'
    },
    {
      smc: 'SEACHEF',
      vessel: 'SEACHEF',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SEACHEF'
    }
  ];

  const totalBFToDistribute = 125000;
  const totalDistributed = distributionData.reduce((sum, item) => sum + item.distributionAmount, 0);
  const distributionSummary = {
    totalBF: totalBFToDistribute,
    totalDistributed: totalDistributed,
    remaining: totalBFToDistribute - totalDistributed,
    entities: distributionData.length,
    excludedEntities: distributionData.filter(d => d.excludeFromDistribution).length
  };

  const handleGeneratePivotTables = async () => {
    setIsCalculating(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await generatePivotTables(workflowId);
      setPivotResults(response.data);
      
      showSuccess(`Generated pivot tables with totals: $${response.data.pivot1_total_amount?.toLocaleString()} and $${response.data.pivot2_total_amount?.toLocaleString()}`);
    } catch (error: any) {
      console.error('Pivot generation failed:', error);
      showError('Failed to generate pivot tables', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleCalculateDistribution = async () => {
    setIsCalculating(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await calculateDistribution(workflowId);
      setDistributionResult(response.data);
      
      showSuccess(`Distributed $${response.data.total_distributed?.toLocaleString()} across ${response.data.distribution_records} records`);
    } catch (error: any) {
      console.error('Distribution calculation failed:', error);
      showError('Failed to calculate distribution', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleProceed = async () => {
    setSaveLoading(true);
    
    try {
      onDistributionComplete?.({
        ...uploadedFiles,
        distributionResult,
        pivotResults
      });
      showSuccess('Distribution completed successfully.');
      onNext?.();
    } catch (error: any) {
      console.error('Distribution completion failed:', error);
      showError('Failed to complete distribution', error);
    } finally {
      setSaveLoading(false);
    }
  };

  const handleExportResults = () => {
    showInfo('Distribution results will be available in the Export tab');
  };

  const summaryHeader = [
    { title: 'Final SMC', align: 'left' },
    { title: 'Final Vessel', align: 'left' },
    { title: 'Total BF', align: 'right' },
    { title: 'Total BF (Including 50%)', align: 'right' },
    { title: 'BF %', align: 'right' },
    { title: 'Distribution Amount', align: 'right' },
  ];

  const summaryData = distributionData.map((item, index) => ({
    id: index,
    components: [
      {
        type: 'text',
        value: (
          <div className="flex items-center space-x-2">
            <PieChart className="w-4 h-4 text-gray-500" />
            <span className="font-medium">{item.finalSMC}</span>
          </div>
        )
      },
      { type: 'text', value: item.finalVessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBF.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBFIncluding50.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-blue-600'
          }`}>
            {item.bfPercentage.toFixed(1)}%
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-mono font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-green-600'
          }`}>
            ${item.distributionAmount.toLocaleString()}
            {item.excludeFromDistribution && ' (Excluded)'}
          </span>
        )
      },
    ],
  }));

  const seachefHeader = [
    { title: 'SMC', align: 'left' },
    { title: 'Vessel', align: 'left' },
    { title: 'Original Amount', align: 'right' },
    { title: '50% Split Amount', align: 'right' },
    { title: 'Allocation Type', align: 'left' },
  ];

  const seachefData = seachefSplitData.map((item, index) => ({
    id: index,
    components: [
      { type: 'text', value: item.smc },
      { type: 'text', value: item.vessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.originalAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono font-semibold text-right text-orange-600">
            ${item.splitAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'badges',
        badges: [{
          intent: item.allocation === 'SEACHEF' ? 'warning' : 'info',
          content: item.allocation
        }]
      },
    ],
  }));

  return (
    <div className="flex flex-col h-full p-6">
      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto pr-2">
        {/* Always show content for demo */}
        <>
            {/* Export Result */}
            {distributionResult && (
              <div className="bg-green-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-green-800 flex items-center mb-3">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Distribution Complete
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Distribution Records</p>
                    <p className="text-xl font-bold text-green-700">
                      {distributionResult.distribution_records?.toLocaleString() || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Distributed</p>
                    <p className="text-xl font-bold text-green-700">
                      ${distributionResult.total_distributed?.toLocaleString() || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Pivot Results */}
            {pivotResults && (
              <div className="bg-green-50 rounded-lg p-4 mb-6">
                <h3 className="font-semibold text-green-800 flex items-center mb-3">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Pivot Tables Generated
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Pivot Table 1 Total</p>
                    <p className="text-xl font-bold text-green-700">
                      ${pivotResults.pivot1_total_amount?.toLocaleString() || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Pivot Table 2 Total</p>
                    <p className="text-xl font-bold text-green-700">
                      ${pivotResults.pivot2_total_amount?.toLocaleString() || 'N/A'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Distribution Controls */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-800 flex items-center">
                    <PieChart className="w-5 h-5 mr-2" />
                    Distribution & Pivot Tables
                  </h3>
                  <p className="text-sm text-gray-600">
                    Generate pivot tables and calculate final BF distribution
                  </p>
                </div>
                <div className="flex space-x-3">
                  <Button
                    onClick={handleGeneratePivotTables}
                    intent="secondary"
                    className="flex items-center"
                    disabled={isCalculating}
                  >
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Generate Pivots
                  </Button>
                  <Button
                    onClick={handleCalculateDistribution}
                    intent="primary"
                    className="flex items-center"
                    disabled={isCalculating}
                  >
                    {isCalculating ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Calculator className="w-4 h-4 mr-2" />
                    )}
                    Calculate Distribution
                  </Button>
                </div>
              </div>
            </div>

            {/* Distribution Summary */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
              <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total BF Pool</p>
                    <p className="text-2xl font-bold text-blue-600">
                      ${distributionSummary.totalBF.toLocaleString()}
                    </p>
                  </div>
                  <DollarSign className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Distributed</p>
                    <p className="text-2xl font-bold text-green-600">
                      ${distributionSummary.totalDistributed.toLocaleString()}
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Remaining</p>
                    <p className="text-2xl font-bold text-orange-600">
                      ${distributionSummary.remaining.toLocaleString()}
                    </p>
                  </div>
                  <Calculator className="w-8 h-8 text-orange-500" />
                </div>
              </div>
              <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Entities</p>
                    <p className="text-2xl font-bold text-gray-800">{distributionSummary.entities}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-gray-500" />
                </div>
              </div>
              <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Excluded</p>
                    <p className="text-2xl font-bold text-red-600">{distributionSummary.excludedEntities}</p>
                  </div>
                  <AlertCircle className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {[
                    { id: 'summary', label: 'Distribution Summary', icon: BarChart3 },
                    { id: 'seachef', label: 'SEACHEF 50% Split', icon: PieChart }
                  ].map((tab) => {
                    const IconComponent = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setSelectedView(tab.id)}
                        className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                          selectedView === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <IconComponent className="w-4 h-4" />
                        <span>{tab.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            {selectedView === 'summary' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Final Distribution Summary</h3>
                  <div className="flex space-x-2">
                    <Button intent="secondary" onClick={handleCalculateDistribution} className='whitespace-nowrap flex justify-center items-center'>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Recalculate
                    </Button>
                    <Button onClick={handleExportResults} className='whitespace-nowrap flex justify-center items-center'>
                      <Download className="w-4 h-4 mr-2" />
                      Export Results
                    </Button>
                  </div>
                </div>
                <div className="bg-white-200 rounded-lg border border-lightgray-100 max-h-96 overflow-y-auto">
                  <Table header={summaryHeader} data={summaryData} isDashTable={false} enableOptions={false} />
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> Frontline entities are excluded from percentage-based distribution calculations
                    as per business rules. SEACHEF allocations include 50% split logic where applicable.
                  </p>
                </div>
              </div>
            )}

            {selectedView === 'seachef' && (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">SEACHEF 50% Allocation Split</h3>
                </div>
                <div className="bg-white-200 rounded-lg border border-lightgray-100 max-h-96 overflow-y-auto">
                  <Table header={seachefHeader} data={seachefData} isDashTable={false} enableOptions={false} />
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <h4 className="font-semibold text-orange-800 mb-2">SEACHEF Split Logic:</h4>
                  <ul className="text-sm text-orange-700 space-y-1">
                    <li>• For entities where BF Allocation = 0.5, split the amount 50/50</li>
                    <li>• 50% goes to the original SMC/Vessel combination</li>
                    <li>• 50% goes to SEACHEF allocation pool</li>
                    <li>• This applies to specific SMCs: BSM CHI, BSM GER, SEACHEF, BSM HEL, BSM SG, BSM India, BSM CY</li>
                  </ul>
                </div>
              </div>
            )}
        </>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">
              Distribution calculated
            </span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">
              Ready to proceed
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              onClick={handleProceed}
              disabled={saveLoading}
            >
              {saveLoading ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : (
                'Proceed to Export →'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Distribution;