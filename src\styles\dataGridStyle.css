/* GENERAL GRID */
.rdg {
  border: 1px solid #c8c6c4;
  border-radius: 6px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 11px;
  background-color: #ffffff;
  overflow: auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* HEADER CELLS */
.rdg-cell[role='columnheader'] {
  width: 100%;
  background-color: #e1e1e1;
  color: #323130;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.4px;
  border-right: 1px solid #c8c6c4;
  border-bottom: 2px solid #c8c6c4;
  text-align: left;
  padding: 4px 4px;
  display: flex;
  align-items: center;
}

.rdg-cell[role='columnheader']:hover {
  background-color: #d5d5d5;
}

/* DATA CELLS */
.rdg-cell[role='gridcell'] {
  background-color: #ffffff;
  color: #323130;
  border-right: 1px solid #edebe9;
  border-bottom: 1px solid #edebe9;
  font-size: 11px;
  padding: 4px 4px;
  display: flex;
  align-items: center;
}

.rdg-cell[role='gridcell']:hover {
  background-color: #f3f2f1;
}

/* FROZEN COLUMN STYLE */
.rdg-cell.frozen {
  background-color: #f3f2f1;
  font-weight: 600;
  border-right: 2px solid #c8c6c4;
}

/* ROW STYLES */
.rdg-row:nth-child(even) {
  background-color: #faf9f8;
}
.rdg-row:nth-child(odd) {
  background-color: #ffffff;
}
.rdg-row:hover {
  background-color: #f3f2f1 !important;
}

/* SELECTED ROW */
.rdg-row[aria-selected='true'] {
  background-color: #deecf9 !important;
}
.rdg-row[aria-selected='true']:hover {
  background-color: #c7e0f4 !important;
}

/* CHECKBOX */
.rdg-checkbox-input {
  border-radius: 4px;
  border: 2px solid #d1d5db;
  width: 16px;
  height: 16px;
}

.rdg-checkbox-input:checked {
  background-color: #0078d4 !important;
  border-color: #0078d4 !important;
}

/* TEXT EDITOR */
.rdg-text-editor {
  border: 1px solid #c8c6c4;
  border-radius: 4px;
  height: 32px;
  padding: 6px 10px;
  font-size: 12px;
  color: #323130;
}

/* FILTERS */
.filter-cell input {
  height: 32px;
  border: 1px solid #c8c6c4;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 12px;
  color: #323130;
}

.filter-cell input:focus {
  border-color: #0078d4;
  box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.2);
  outline: none;
}

/* SCROLLBAR */
.rdg::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.rdg::-webkit-scrollbar-track {
  background-color: #f3f2f1;
}
.rdg::-webkit-scrollbar-thumb {
  background: #c8c6c4;
  border-radius: 4px;
}
.rdg::-webkit-scrollbar-thumb:hover {
  background: #a19f9d;
}

/* Z-INDEX for dropdowns */
.rdg-cell .relative {
  position: relative;
  z-index: 10;
}

.rdg-cell .absolute {
  z-index: 9999 !important;
  position: absolute !important;
  background: white !important;
  box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.1);
}

/* LAST COLUMN (actions) */
.rdg-cell:last-child {
  overflow: visible;
  display: flex;
  justify-content: center;
  align-items: center;
}
