/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable react/no-danger */
import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const search = cva(
  ' mb-1 w-full rounded border-[1px] bg-white-200 pl-8 pr-3 font-sans text-sm font-normal  leading-4  ',
  {
    variants: {
      intent: {
        enabled: [
          'border-lightgray-100',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'text-blueGray-300',
          'placeholder:text-gray-50',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:border-purple-200',
          'focus:outline-none',
        ],
        disabled: [
          'border-lightgray-100 ',
          'pointer-events-none',
          'opacity-50',
          'text-gray-400',
          'placeholder:text-gray-50',
        ],
        hasError: [
          'border-red-200',
          'focus:outline-none',
          'text-blueGray-300',
          'placeholder:text-gray-50',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'focus:border-purple-200',
        ],
      },
    },
    compoundVariants: [
      {
        intent: 'enabled',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'enabled',
    },
  },
);

export interface SearchProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof search> {
  error?: string;
  isCondensed?: boolean;
}

export const Searchbar: React.FC<SearchProps> = ({
  className,
  value,
  placeholder,
  intent,
  error,
  name,
  onChange,
  isCondensed = false,
  ...props
}) => (
  <>
    <div className="relative">
      <input
        type="text"
        placeholder={placeholder}
        name={name}
        value={value}
        onChange={onChange}
        className={search({ intent, className })}
        {...props}
      />
      <div
        className=" absolute inset-y-0 left-0 flex items-center pl-3 pr-2 "
        dangerouslySetInnerHTML={{
          __html: `<img src='/assets/images/search.svg'>`,
        }}
      />
    </div>
    {error && (
      <div className="pt-1 font-sans text-xs font-normal leading-4 text-red-200">
        {error}
      </div>
    )}{' '}
  </>
);
