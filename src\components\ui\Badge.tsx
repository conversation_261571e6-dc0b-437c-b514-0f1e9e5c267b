import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import React from 'react';

const badge = cva(
  'min-h-6 inline-flex items-center  rounded-lg px-[9px]  py-1',
  {
    variants: {
      intent: {
        success: ['bg-green-100/10', 'text-green-100'],
        error: ['bg-red-100/10', 'text-red-100'],
        warning: ['bg-yellow-100/10', 'text-yellow-100'],
        info: ['bg-blue-100/10', 'text-blue-100'],
        neutral: ['bg-gray-500/10 text-gray-500'],
        counter: ['bg-blue-200/10 text-blue-200'],
      },
    },
    compoundVariants: [
      {
        intent: 'success',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'success',
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badge> {}

export const Badge: React.FC<BadgeProps> = ({
  className,
  content,
  intent,
  ...props
}) => (
  <div className={badge({ intent, className })} {...props}>
    <span className="font-sans text-sm font-semibold  capitalize  leading-4 tracking-[0.2px]">
      <span>{content}</span>
    </span>
  </div>
);
