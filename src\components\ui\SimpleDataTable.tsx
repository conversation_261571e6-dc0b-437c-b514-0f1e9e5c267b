"use client";

import React from "react";

interface Column {
  name: string;
  key: string;
  width?: number;
  isSortable?: boolean;
  isFrozen?: boolean;
  renderCell?: ({ row, rowIdx }: { row: any; rowIdx: number }) => React.ReactNode;
}

interface SimpleDataTableProps {
  data: any[];
  headerList: Column[];
  handleRowSelected?: (row: any, type?: string, rowIdx?: number) => void;
  className?: string;
}

const SimpleDataTable: React.FC<SimpleDataTableProps> = ({
  data,
  headerList,
  handleRowSelected,
  className = "",
}) => {
  const handleRowClick = (row: any, rowIdx: number) => {
    if (handleRowSelected) {
      handleRowSelected(row, undefined, rowIdx);
    }
  };

  return (
    <div className={`border border-gray-200 rounded-lg overflow-hidden ${className}`} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Fixed Header */}
      <div style={{ 
        flexShrink: 0, 
        backgroundColor: '#f8f9fa', 
        borderBottom: '1px solid #e1e5e9',
        overflow: 'hidden'
      }}>
        <table className="w-full" style={{ tableLayout: 'fixed' }}>
          <thead>
            <tr>
              {headerList.map((header, index) => (
                <th
                  key={index}
                  className="text-left font-semibold uppercase tracking-wider"
                  style={{
                    minWidth: header.width ? `${header.width}px` : 'auto',
                    width: header.width ? `${header.width}px` : 'auto',
                    height: '40px',
                    padding: '8px 12px',
                    fontSize: '11px',
                    fontWeight: '600',
                    color: '#3c4043',
                    borderRight: index < headerList.length - 1 ? '1px solid #e1e5e9' : 'none',
                    letterSpacing: '0.5px',
                    textTransform: 'uppercase',
                  }}
                >
                  <div className="flex items-center space-x-1">
                    <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'clip' }}>
                      {header.name}
                    </span>
                    {header.isSortable && (
                      <svg className="w-3 h-3" style={{ color: '#5f6368' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                      </svg>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
        </table>
      </div>

      {/* Scrollable Body */}
      <div 
        style={{ 
          flex: 1,
          overflow: 'auto',
          backgroundColor: 'white'
        }}
      >
        <table className="w-full" style={{ tableLayout: 'fixed' }}>
          <tbody>
            {data.length === 0 ? (
              <tr>
                <td
                  colSpan={headerList.length}
                  className="text-center"
                  style={{ 
                    padding: '32px',
                    color: '#5f6368',
                    fontSize: '14px'
                  }}
                >
                  No data available
                </td>
              </tr>
            ) : (
              data.map((row, rowIdx) => (
                <tr
                  key={rowIdx}
                  onClick={() => handleRowClick(row, rowIdx)}
                  className="cursor-pointer transition-colors duration-100"
                  style={{
                    backgroundColor: rowIdx % 2 === 0 ? 'white' : '#fafbfc',
                    borderBottom: '1px solid #f1f3f4',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = rowIdx % 2 === 0 ? 'white' : '#fafbfc';
                  }}
                >
                  {headerList.map((header, colIdx) => (
                    <td
                      key={colIdx}
                      style={{
                        minWidth: header.width ? `${header.width}px` : 'auto',
                        width: header.width ? `${header.width}px` : 'auto',
                        height: '32px',
                        padding: '6px 12px',
                        fontSize: '12px',
                        color: '#3c4043',
                        borderRight: colIdx < headerList.length - 1 ? '1px solid #f1f3f4' : 'none',
                        lineHeight: '1.2',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {header.renderCell ? (
                        header.renderCell({ row, rowIdx })
                      ) : (
                        <span style={{ display: 'block', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {row[header.key]?.toString() || '-'}
                        </span>
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Custom Scrollbar Styles */}
      <style jsx>{`
        div::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        div::-webkit-scrollbar-track {
          background: #f8f9fa;
        }
        div::-webkit-scrollbar-thumb {
          background: #dadce0;
          border-radius: 4px;
        }
        div::-webkit-scrollbar-thumb:hover {
          background: #bdc1c6;
        }
      `}</style>
    </div>
  );
};

export default SimpleDataTable;