/* eslint-disable import/no-extraneous-dependencies */

'use client';

import { models } from 'powerbi-client';
import { PowerBIEmbed } from 'powerbi-client-react';
import React from 'react';

const EmbeddedDashboard: React.FC<{ url: string }> = ({ url }) => {
  return (
    <PowerBIEmbed
      embedConfig={{
        type: 'report', // Supported types: report, dashboard, tile, visual, qna and paginated report
        id: undefined,
        embedUrl: url,
        accessToken: undefined, // Keep as empty string, null or undefined
        tokenType: models.TokenType.Embed,
      }}
    />
  );
};

export default EmbeddedDashboard;
