'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Searchbar } from '@/components/ui/Searchbar';
import Table from '@/components/ui/Table';
import { TestDataService } from '@/service/testdata.service';

import ViewDataLogDetails from './viewDataLogDetails';

// import SelectData from './addLogicalEntitiesTabs/selectData';

const ViewDataLog: React.FC = () => {
  // Essentials
  const router = useRouter();
  const searchParams = useSearchParams();
  const testDataService = new TestDataService();

  // Derivatives
  const view: any = searchParams?.get('view');

  // Constants
  const header: any = [
    {
      title: 'Name',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'ENTRY ID',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Template Category',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Uploaded On',
      optionsEnabled: true,
      options: [],
    },
  ];

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Methods
  const goBack = () => {
    router.push('/dataCollaboration');
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    // const filteredValues = filteredData.values.filter((row: any) =>
    //   row.components.some((cell: any) => {
    //     if (typeof cell.value === 'string') {
    //       return cell.value.toLowerCase().includes(value.toLowerCase());
    //     }
    //     return false;
    //   }),
    // );
    // setFilteredData(filteredValues);
  };

  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  // Effects
  useEffect(() => {
    // dispatch(setIsLoading(true));
    const fetchData = async () => {
      // const url = `${
      //   ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
      // }/${selectedDataset.id}/tables`;
      // apiService
      //   .getRequest(url)
      //   .then((res) => {
      //     dispatch(setIsLoading(false));
      //     setTableData(res.data);
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //     dispatch(setIsLoading(false));
      //   });
      const response = await testDataService.getAllTestDataFromFile(
        'dataCollaboration/dataset',
      );
      setFilteredData(response);
    };
    fetchData();
  }, []);

  if (view === 'details') {
    return <ViewDataLogDetails />;
  }

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Data Log
        </span>
      </div>
      <div className="flex h-full w-full flex-col bg-white-200 p-4 py-6">
        <div className="w-[20vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>

        <div className="h-full w-full overflow-auto" onScroll={handleScroll}>
          <Table
            isDashTable
            // menuData={menuData}
            header={header}
            data={filteredData}
            scrollPosition={scrollPosition}
            enableOptions={false}
            isEyeVisible
            isDownloadVisible
            EyeFunction={() =>
              router.push('/dataCollaboration/dataLog?view=details')
            }
            isView
          />
        </div>
      </div>
    </div>
  );
};

export default ViewDataLog;
