/* eslint-disable radix */
/* eslint-disable react-hooks/exhaustive-deps */
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DynamicForm from '@/components/dynamicForm/dynamicForm';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setSourceSystemForms } from '@/slices/metaDataSlice';
import { setSourceSystemConfigData } from '@/slices/sourceSystemCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

export default function ConnectionDetailsForm() {
  // essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const selectedSourceSystem = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedSourceSystem,
  );
  const sourceSysteForms = useSelector(
    (state: RootState) => state.metadata.sourceSystemForms,
  );
  const isTestingConnection = useSelector(
    (state: RootState) => state.sourceSystemsCrud.isTestingConnection,
  );
  const sourceSystemConfigData = useSelector(
    (state: RootState) => state.sourceSystemsCrud.sourceSystemConfigData,
  );

  // States
  const [sourceSystemFields, setSourceSystemFields] = useState([]);

  // Props
  const getSourceSystemForm = () => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getSourceSystemFormFields.url +
      selectedSourceSystem;
    apiService
      .getRequest(url)
      .then((res) => {
        dispatch(setIsLoading(false));
        setSourceSystemFields(res.data);
        const ssf = structuredClone(sourceSysteForms);
        const ss = parseInt(selectedSourceSystem);
        let ssForm: any = {};
        ssForm[ss] = structuredClone(res.data);
        ssForm = { ...ssf, ...ssForm };
        dispatch(setSourceSystemForms(ssForm));
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  const onSubmitFn = (values: any) => {
    dispatch(setSourceSystemConfigData(values));
  };

  // Effects
  useEffect(() => {
    if (sourceSysteForms[selectedSourceSystem]) {
      setSourceSystemFields(
        structuredClone(sourceSysteForms[selectedSourceSystem]),
      );
    } else {
      getSourceSystemForm();
    }
  }, [selectedSourceSystem]);

  return (
    <div className="mt-4 flex w-full flex-col overflow-auto  ">
      <div className="mt-4 h-full w-full">
        <div className="mt-3 flex h-full flex-row">
          <DynamicForm
            fields={sourceSystemFields}
            fieldData={sourceSystemConfigData}
            onSubmit={onSubmitFn}
            isSubmit={isTestingConnection}
          />
        </div>
      </div>
    </div>
  );
}
