/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-nested-ternary */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import UploadFileTable from '@/components/tables/fileUpload/fileUploadTable';
import HistoryTable from '@/components/tables/fileUpload/historyTable';
import { Button } from '@/components/ui/Button';
import {
  alreadyExistsList,
  conflictedFilesList,
  countryListItems,
  monthList,
  quarterList,
  selectListForUploadDatasets,
  yearList,
} from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  resetState,
  setProcessedFile,
  setProcessedFileTableData,
} from '@/slices/fileUploadSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';
import { formatSelectListData } from '@/utils/utilityHelper';

import { Badge } from '../../ui/Badge';
import { Tabs } from '../../ui/Tabs';
import UploadFile from './uploadNewFile';
import { WarningAlertForFile } from './warningAlertForFile';

const FileUpload: React.FC = () => {
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const searchParams = useSearchParams();
  const screen: any = searchParams?.get('history');
  const isDirectNavigation: any = searchParams?.get('home');
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const processedFileList = useSelector(
    (state: any) => state.fileUploadCred.processedFileList,
  );

  const tableData = useSelector(
    (state: any) => state.fileUploadCred.processedFileTableData,
  );

  const statusmapper: any = {
    success: 'success',
    failed: 'error',
    'in-progress': 'warning',
  };
  // States
  const [warningOpen, setWarningOpen] = useState(false);
  const [isProceedButtonVisible, setIsProceedButtonVisible] = useState(true);
  const [isUploadCancled, setIsUploadCancled] = useState(false);
  const [filteredCadenceFiles, setFilteredCadenceFiles] = useState([]);
  const [remainingCadenceFiles, setRemainingCadenceFiles] = useState([]);
  const [historyCount, setHistoryCount] = useState('48');
  const [selectedTab, setSelectedTab] = useState(
    Number(localService.getItem('fileUploadTab')) || 0,
  );
  const [scrollPosition, setScrollPosition] = useState(0);
  const [fileUploaded, setFileUploaded] = useState(false);
  const [droppedFiles, setDroppedFiles] = useState<any>([]);
  const [countryList, setCountryList] = useState<any>([]);
  const [source, setSource] = useState<any>([]);
  const [quarter] = useState<any>(quarterList);
  const [months] = useState<any>(monthList);
  const [years] = useState<any>(yearList);
  // Methods
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const handleHistoryCountUpdate = (count: any) => {
    setHistoryCount(count);
  };

  const handleFiles = (files: any) => {
    setDroppedFiles(files);
  };

  const isButtonDisabled = (tableRows: any) => {
    for (let i = 0; i < tableRows.length; i += 1) {
      const row = tableRows[i];
      const actionComponent = row.components.find(
        (component: any) => component.type === 'action',
      );
      if (
        actionComponent &&
        actionComponent.visible === true &&
        actionComponent.value === ''
      ) {
        return true;
      }
    }
    return false;
  };

  const handleProcessedFileRowChange = (rows: any) => {
    dispatch(setProcessedFileTableData(rows));
  };

  const removeDiscardedData = (rowData: any) => {
    return rowData.filter((item: any) => {
      return item.components[8].value !== 'Discard';
    });
  };

  const navigateToHistory = (newTableData: any, newProcessedData: any) => {
    dispatch(setProcessedFileTableData(newTableData));
    dispatch(setProcessedFile(newProcessedData));
    const allFileDetails = modifyProcessFileData(newProcessedData);
    submitFileDetails(allFileDetails);
    // console.log("all files", allFileDetails);
    // const modifiedFileDetails = getDifferentFiles(
    //   allFileDetails,
    //   newProcessedData,
    // );
    // if (modifiedFileDetails.length > 0) {
    //   modifyFileDetailsAndSubmit(modifiedFileDetails, allFileDetails);
    // } else {
    //   submitFileDetails(allFileDetails);
    // }
  };

  const removeDiscardedDataFromProcessedFiles = (newTableDataArray: any) => {
    const namesInB: any = newTableDataArray.map(
      (itemB: any) => itemB.components[0].value,
    );
    const newProcessedArray: any = processedFileList.filter((itemA: any) =>
      namesInB.includes(itemA.originalFileName),
    );
    navigateToHistory(newTableDataArray, newProcessedArray);
  };

  const handleActionChange = (
    rows: any,
    cellIndex: any,
    rowIndex: any,
    selectedValue: any,
  ) => {
    const updatedRows: any = structuredClone(rows);
    if (rows[rowIndex].metaData.alreadyExists === true) {
      if (rows[rowIndex].metaData.payloadConflict === true) {
        if (selectedValue === 'Replace') {
          updatedRows.map((row: any) => {
            if (
              rows[rowIndex].metaData.conflictedFiles.includes(
                row.components[0].value,
              )
            ) {
              row.components[cellIndex].value = 'Discard';
              row.components[cellIndex].selectedValue = {
                name: 'Discard',
              };
            }
          });
        } else {
          updatedRows.map((row: any) => {
            if (
              rows[rowIndex].metaData.conflictedFiles.includes(
                row.components[0].value,
              )
            ) {
              if (
                row.components[cellIndex].value === 'Replace' ||
                row.components[cellIndex].value === 'Accept'
              ) {
                row.components[cellIndex].value = 'Discard';
                row.components[cellIndex].selectedValue = {
                  name: 'Discard',
                };
              }
            }
          });
        }
      }
    } else if (rows[rowIndex].metaData.payloadConflict === true) {
      if (selectedValue === 'Accept') {
        updatedRows.map((row: any) => {
          if (
            rows[rowIndex].metaData.conflictedFiles.includes(
              row.components[0].value,
            )
          ) {
            row.components[cellIndex].value = 'Discard';
            row.components[cellIndex].selectedValue = {
              name: 'Discard',
            };
          }
        });
      } else {
        updatedRows.map((row: any) => {
          if (
            rows[rowIndex].metaData.conflictedFiles.includes(
              row.components[0].value,
            )
          ) {
            if (
              row.components[cellIndex].value === 'Replace' ||
              row.components[cellIndex].value === 'Accept'
            ) {
              row.components[cellIndex].value = 'Discard';
              row.components[cellIndex].selectedValue = {
                name: 'Discard',
              };
            }
          }
        });
      }
    }
    dispatch(setProcessedFileTableData(updatedRows));
  };

  const tabData: any = [
    {
      title: 'Upload New File',
      component: fileUploaded ? (
        <UploadFileTable
          scrollPosition={scrollPosition}
          onRowChange={handleProcessedFileRowChange}
          onActionChange={handleActionChange}
        />
      ) : (
        <UploadFile
          isUploadCancled={isUploadCancled}
          onFileUpload={handleFiles}
        />
      ),
    },
    {
      icon: (
        <Badge
          intent={selectedTab === 1 ? 'counter' : 'neutral'}
          content={historyCount}
        />
      ),
      title: 'History',
      component: (
        <HistoryTable
          scrollPosition={scrollPosition}
          onHistoryCountChange={handleHistoryCountUpdate}
        />
      ),
    },
  ];

  const handelCancel = () => {
    setIsUploadCancled(true);
    setFileUploaded(false);
    setDroppedFiles([]);
  };

  const getSelectList = (rowData: any) => {
    if (rowData.alreadyExists && rowData.alreadyExists === true) {
      return alreadyExistsList;
    }
    if (rowData.payloadConflict && rowData.payloadConflict === true) {
      return conflictedFilesList;
    }
    if (
      rowData.alreadyExists &&
      rowData.alreadyExists === true &&
      rowData.payloadConflict &&
      rowData.payloadConflict === true
    ) {
      return alreadyExistsList;
    }
    return [];
  };

  const getVisibilityOfWarning = (rowData: any) => {
    return (
      rowData.alreadyExists ||
      rowData.payloadConflict ||
      (rowData.alreadyExists && rowData.payloadConflict)
    );
  };

  const generateTableData = (data: any) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data.map((row) => {
      const components = [
        {
          value: row.originalFileName ? row.originalFileName : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.country ? row.country : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Quarter',
          selectList: countryList,
        },
        {
          value: row.datasetName ? row.datasetName : '',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Datatype',
          selectList: source,
        },
        {
          value: 'Valid',
          type: 'image',
          url: '/assets/images/tick-circle.svg',
        },
        {
          value: Number(row.year) > 0 ? Number(row.year) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Quarter',
          selectList: years,
        },
        {
          value: Number(row.quarter) > 0 ? Number(row.quarter) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Quarter',
          selectList: quarter,
        },
        {
          value: Number(row.month) > 0 ? Number(row.month) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Month',
          selectList: months,
        },
        {
          value: '',
          badges: [{ intent: statusmapper[row?.status], content: row?.status }],
          disabled: false,
          userAvatarLink: null,
          type: 'badges',
        },
        {
          value: '',
          selectList: getSelectList(row),
          visible: getVisibilityOfWarning(row),
          type: 'action',
        },
      ];
      return {
        id: row.fileUploadDetailId,
        metaData: {
          alreadyExists: row.alreadyExists ? row.alreadyExists : false,
          payloadConflict: row.payloadConflict ? row.payloadConflict : false,
          conflictedFiles: row.conflictedFiles ? row.conflictedFiles : [],
        },
        components,
      };
    });
  };

  const filterAndRemoveMatchingCadenceFiles = (files: any) => {
    const filteredFiles = files.filter(
      (file: any) => file.matchesDatasetCadence === false,
    );
    const remainingFiles = files.filter(
      (file: any) => file.matchesDatasetCadence !== false,
    );
    return { filteredFiles, remainingFiles };
  };

  const fetchFileDetailsTableData = (files: any) => {
    const url =
      ApiUtilities.getApiServerUrlBsm +
      ApiUtilities.apiPath.postUploadedFiles.url;
    const formData: any = new FormData();
    files.forEach((file: File) => {
      formData.append('files', file);
    });
    apiService
      .postRequestBsmFile(url, formData)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          const tempData = filterAndRemoveMatchingCadenceFiles(res.data);
          console.log('tempData', tempData);
          if (tempData.filteredFiles.length > 0) {
            setFilteredCadenceFiles(tempData.filteredFiles);
            setRemainingCadenceFiles(tempData.remainingFiles);
            setWarningOpen(true);
            if (tempData.filteredFiles.length === res.data.length) {
              setIsProceedButtonVisible(false);
            } else {
              setIsProceedButtonVisible(true);
            }
          } else {
            setFileUploaded(true);
            dispatch(setProcessedFile(res.data));
            dispatch(setProcessedFileTableData(generateTableData(res.data)));
            dispatch(
              setToastAlert({
                isToastOpen: true,
                intent: 'success',
                title: 'File Upload Successful',
                content: 'Files has been uploaded successfully',
              }),
            );
          }
        } else {
          setFileUploaded(false);
          alert('something went wrong');
        }
      })
      .catch((err) => {
        if (err.response.status === 413) {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: 'Upload failed. Maximum upload size allowed is 20MB.',
            }),
          );
        } else {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: 'File Upload Failed!!',
            }),
          );
        }
        console.log(err);
        setFileUploaded(false);
        dispatch(setIsLoading(false));
      });
  };

  const navigateToFiles = () => {
    setIsUploadCancled(false);
    if (!fileUploaded) {
      fetchFileDetailsTableData(droppedFiles);
    }
  };

  const modifyProcessFileData = (objectsArray: any) => {
    return objectsArray.map((dataRow: any) => {
      const dataRowForProcessedFile: any = structuredClone(dataRow);
      const tableRowData = tableData.find(
        (tableRow: any) =>
          tableRow.components[0].value === dataRow.originalFileName &&
          tableRow !== dataRow,
      );
      if (tableRowData) {
        dataRowForProcessedFile.id = dataRow.fileUploadDetailId;
        dataRowForProcessedFile.country =
          tableRowData.components[1].value === 'N/A'
            ? null
            : tableRowData.components[1].value;
        dataRowForProcessedFile.datasetName = tableRowData.components[2].value;
        dataRowForProcessedFile.year =
          tableRowData.components[4].value === 'N/A'
            ? Number(-1)
            : Number(tableRowData.components[4].value);
        dataRowForProcessedFile.month =
          tableRowData.components[6].value === 'N/A'
            ? Number(-1)
            : Number(tableRowData.components[6].value);
        dataRowForProcessedFile.quarter =
          tableRowData.components[5].value === 'N/A'
            ? String(-1)
            : String(tableRowData.components[5].value);
        dataRowForProcessedFile.appendOrReplace =
          tableRowData.components[8].value === 'Replace' ? 'R' : 'A';
        delete dataRowForProcessedFile.fileUploadDetailId;
        delete dataRowForProcessedFile.payloadConflict;
        delete dataRowForProcessedFile.alreadyExists;
        delete dataRowForProcessedFile.conflictedFiles;
        return dataRowForProcessedFile;
      }
      return dataRowForProcessedFile;
    });
  };

  const handelUploadCancel = () => {
    setIsUploadCancled(true);
    setWarningOpen(false);
    setFileUploaded(false);
    setDroppedFiles([]);
  };

  const submitFileDetails = (files: any) => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrlBsm +
      ApiUtilities.apiPath.submitUploadedFileDetails.url;
    apiService
      .postRequest(url, files)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'Details Updated successfully',
              content: 'File Details has been updated successfully',
            }),
          );
          const filter: any = {
            selectedYear: 'All',
            selectedMonth: 'All',
            selectedQuarter: 'All',
            selectedSource: 'All',
            selectedFileIsReplaced: 'All',
          };
          localStorage.setItem('fileUploadFilters', JSON.stringify(filter));
          setSelectedTab(1);
          localService.setItem('fileUploadTab', '1');
        } else {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: 'Something went wrong',
            }),
          );
        }
      })
      .catch((err) => {
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Failed !!',
            content: 'Something went wrong!',
          }),
        );
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  // const modifyFileDetailsAndSubmit = (
  //   modifiedFileDetails: any,
  //   allFileDetails: any
  // ) => {
  //   const url =
  //     ApiUtilities.getApiServerUrlBsm +
  //     ApiUtilities.apiPath.modifyUploadedFileDetails.url;
  //   dispatch(setIsLoading(true));
  //   apiService
  //     .putRequest(url, modifiedFileDetails)
  //     .then((res) => {
  //       dispatch(setIsLoading(false));
  //       if (res.status === 200) {
  //         submitFileDetails(allFileDetails);
  //       } else {
  //         dispatch(setIsLoading(false));
  //         alert("something went wrong");
  //       }
  //     })
  //     .catch((err) => {
  //       dispatch(
  //         setToastAlert({
  //           isToastOpen: true,
  //           intent: "error",
  //           title: "Error",
  //           content: "Something Went Wrong!!",
  //         })
  //       );
  //       console.log(err);
  //       dispatch(setIsLoading(false));
  //     });
  // };

  // const getDifferentFiles = (modifiedList: any, processedList: any) => {
  //   return modifiedList.filter((modifiedFile: any) => {
  //     const found = processedList.some(
  //       (processedFile: any) =>
  //         JSON.stringify(processedFile) === JSON.stringify(modifiedFile)
  //     );
  //     return !found;
  //   });
  // };

  const proceedWithRemainingFiles = () => {
    setWarningOpen(false);
    if (remainingCadenceFiles.length > 0) {
      setFileUploaded(true);
      dispatch(setProcessedFile(remainingCadenceFiles));
      dispatch(
        setProcessedFileTableData(generateTableData(remainingCadenceFiles)),
      );
      dispatch(
        setToastAlert({
          isToastOpen: true,
          intent: 'success',
          title: 'File Upload Successful',
          content: 'Files has been uploaded successfully',
        }),
      );
    }
  };

  const extractIdAndName = (sourceData: any) => {
    return sourceData.map((item: any) => {
      return {
        id: item.id,
        name: item.datasetName,
      };
    });
  };

  const fetchSource = async () => {
    try {
      const url = `${
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getBsmDataSets.url
      }`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const transformedData: any = extractIdAndName(res.data);
            setSource(transformedData);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const fetchHistory = async () => {
    try {
      const url = `${
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getFileHistory.url
      }`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            setHistoryCount(res.data.length);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const submitClicked = () => {
    const newTableDataArray = removeDiscardedData(tableData);
    removeDiscardedDataFromProcessedFiles(newTableDataArray);
  };

  const fetchCountryList = () => {
    try {
      const url = `${
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getCountryList.url
      }`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const formattedData: any = formatSelectListData(res.data);
            setCountryList([{ id: 0, name: 'N/A' }, ...formattedData]);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const goBack = () => {
    router.back();
  };

  useEffect(() => {
    setDroppedFiles([]);
  }, [selectedTab]);

  useEffect(() => {
    if (!isTourOpen) {
      fetchCountryList();
      fetchSource();
      fetchHistory();
    } else {
      setCountryList(countryListItems);
      setSource(selectListForUploadDatasets);
    }
  }, []);

  useEffect(() => {
    if (screen) {
      if (screen === '1') {
        setSelectedTab(1);
        localService.setItem('fileUploadTab', String(1));
      } else if (screen === '0') {
        setSelectedTab(0);
        localService.setItem('fileUploadTab', String(0));
      }
    }
  }, [screen]);

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full flex-row items-center space-x-2 ">
        {!isDirectNavigation && (
          <img
            onClick={goBack}
            className="size-[24px] cursor-pointer"
            src="/assets/images/arrow-left.svg"
            alt="back"
          />
        )}
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Files
        </span>
      </div>
      <div
        className="history add-new-file flex h-[calc(100vh-140px)] w-full flex-col overflow-hidden rounded border border-lightgray-100 bg-white-200 p-4 pb-0"
        onScroll={handleScroll}
      >
        <Tabs
          data={tabData}
          selectedIndex={selectedTab}
          onChange={(e: number) => {
            setSelectedTab(e);
            localService.setItem('fileUploadTab', String(e));
            setFileUploaded(false);
            setDroppedFiles([]);
          }}
        />
      </div>
      <div className="flex h-[40px] w-full flex-row items-center justify-between">
        <div />
        <div
          className={` space-x-4 ${
            selectedTab === 1 ? 'hidden' : 'flex flex-row'
          } `}
        >
          {' '}
          {fileUploaded && (
            <Button
              className="flex h-[40px] items-center"
              type="button"
              onClick={handelCancel}
              intent="secondary"
            >
              Cancel
            </Button>
          )}
          {fileUploaded ? (
            <Button
              className="flex h-[40px] items-center"
              type="submit"
              form="myform"
              onClick={submitClicked}
              disabled={
                (droppedFiles.length === 0 && !fileUploaded) ||
                isButtonDisabled(tableData)
              }
            >
              Submit
            </Button>
          ) : (
            <Button
              className="flex h-[40px] items-center"
              type="submit"
              form="myform"
              onClick={navigateToFiles}
              disabled={droppedFiles.length === 0 && !fileUploaded}
            >
              Process Files
            </Button>
          )}
        </div>
      </div>
      <WarningAlertForFile
        isProceedButtonVisible={isProceedButtonVisible}
        isOpen={warningOpen}
        onClose={handelUploadCancel}
        onConfirm={proceedWithRemainingFiles}
        message={
          isProceedButtonVisible
            ? "does not matches the Dataset Cadence criteria Would you like to proceed with processing the remaining files? If you wish to reupload the files, please click 'Discard.'"
            : 'does not matches the Dataset Cadence criteria. Please reupload the files.'
        }
        header="Dataset Cadence Warning"
        data={filteredCadenceFiles}
        cancelButtonText={isProceedButtonVisible ? ' No, Discard' : 'Close'}
      />
    </div>
  );
};

export default FileUpload;
