/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable unused-imports/no-unused-vars */

'use client';

/* eslint-disable react-hooks/exhaustive-deps */
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import DatasetDetailsTable from '@/components/tables/dataset/datasetDetailsTable';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Link } from '../../ui/Link';
import { Searchbar } from '../../ui/Searchbar';

const DataSetDetails: React.FC = () => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const searchParams = useSearchParams();

  // Derivatives
  const datasetName: any = searchParams?.get('name');
  const id: any = searchParams?.get('id');

  // States
  const [tableData, setTableData] = useState<any>([]);
  const [selectedTableIndex, setSelectedTableIndex] = useState<any>(null);
  const [filterTableData, setFilterTableData] = useState({
    header: [],
    data: [],
  });
  const [selectedTableData, setSelectedTableData] = useState({
    header: [],
    data: [],
  });
  const [searchTableItem, setSearchTableItem] = useState('');
  const [searchTableDataset, setSearchTableDataset] = useState('');
  const [searchResults, setSearchResults] = useState(tableData);

  // Methods
  const handleTableSearch = (value: string) => {
    setSearchTableItem(value);
    const filteredResults = tableData.filter((item: any) =>
      item.table_name.toLowerCase().includes(value.toLowerCase()),
    );

    setSearchResults(filteredResults);
  };

  const handleTableDatasetSearch = (query: string) => {
    setSearchTableDataset(query);
    const filteredData = selectedTableData.data.filter((row: any) =>
      row.some(
        (value: any) =>
          typeof value !== 'object' &&
          value.toLowerCase().includes(query.toLowerCase()),
      ),
    );
    if (filteredData) {
      setFilterTableData({
        ...selectedTableData,
        data: filteredData,
      });
    }
    if (query === '') {
      setFilterTableData(selectedTableData);
    }
  };

  const setSelectedIndexAndData = (index: number) => {
    setSelectedTableIndex(index);
    setSearchTableDataset('');
  };

  const getTableContent = (tableId: string) => {
    dispatch(setIsLoading(true));
    const data = {
      token: localStorage.getItem('id_token'),
    };
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
    }/${id}/tables/${tableId}/data`;
    apiService
      .postRequest(url, data)
      .then((res) => {
        dispatch(setIsLoading(false));
        const tableResult: any = {
          header: res.data.columns,
          data: res.data.data,
        };
        setSelectedTableData(tableResult);
        setFilterTableData(tableResult);
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  // Effects
  useEffect(() => {
    dispatch(setIsLoading(true));
    const fetchData = async () => {
      const url = `${
        ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
      }/${id}/tables`;
      apiService
        .getRequest(url)
        .then((res) => {
          dispatch(setIsLoading(false));
          setTableData(res.data);
        })
        .catch((err) => {
          console.log(err);
          dispatch(setIsLoading(false));
        });
    };
    fetchData();
  }, [id]);

  useEffect(() => {
    setSearchResults(tableData);
  }, [tableData]);

  useEffect(() => {
    setFilterTableData(filterTableData);
  }, [filterTableData]);

  return (
    <div className="flex w-full flex-col space-y-4 overflow-hidden">
      <div className="flex w-full flex-row items-center space-x-2">
        <Link
          className="px-1"
          href="/datasets"
          content={`<img src='/assets/images/backIcon.svg'>`}
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {datasetName}
        </span>
      </div>
      <div className="view-dataSet-table flex h-[88vh] w-full flex-row overflow-hidden rounded border-[1px] border-lightgray-100  bg-white-200">
        <div className="flex h-full min-w-[218px] flex-col border-b-[1px] border-r-[1px] border-lightgray-200 ">
          <div className="w-full px-4 py-6">
            <span className=" font-sans text-base font-semibold leading-8 text-blueGray-300">
              Tables
            </span>
          </div>
          <div className="flex h-full w-full flex-col space-y-2 px-2 ">
            {' '}
            <Searchbar
              value={searchTableItem}
              placeholder="Search Table"
              onChange={(e) => handleTableSearch(e.target.value)}
            />
            <div className="h-[65vh] pb-2">
              <div className="flex h-[63vh] w-full flex-col space-y-1 overflow-y-auto ">
                {searchResults.map((item: any, index: number) => (
                  <button
                    key={item.id}
                    type="button"
                    onClick={() => {
                      setSelectedIndexAndData(index);
                      // get table data
                      getTableContent(item.id);
                    }}
                    className={`flex  w-full cursor-pointer items-center px-[13px] py-3 text-left font-sans text-sm font-semibold  ${
                      selectedTableIndex === index
                        ? 'bg-lightblue-100 text-lightblue-200'
                        : 'text-gray-500 hover:bg-lightblue-100 hover:text-lightblue-200 focus:bg-lightblue-100 focus:text-lightblue-200 active:bg-lightblue-100 active:text-lightblue-200'
                    }`}
                  >
                    {item.table_name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="flex h-full w-full flex-col">
          <div className="w-full p-4">
            <div className="w-[40vw]">
              {' '}
              <Searchbar
                value={searchTableDataset}
                placeholder="Search"
                onChange={(e) => handleTableDatasetSearch(e.target.value)}
              />
            </div>
          </div>
          <div className="min-h-full min-w-full">
            {filterTableData.data.length > 0 &&
            filterTableData.header.length > 0 ? (
              <DatasetDetailsTable data={filterTableData} />
            ) : (
              <div className="flex h-[80%] items-center justify-center ">
                {selectedTableIndex === null ? (
                  <span className="font-sans text-sm font-normal text-gray-400 ">
                    No Data Available. Please Select a table to View Data.
                  </span>
                ) : (
                  <span className="font-sans text-sm font-normal text-gray-400 ">
                    No Data Available
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataSetDetails;
