/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import type { Configuration } from '@azure/msal-browser';
import {
  InteractionRequiredAuthError,
  PublicClientApplication,
} from '@azure/msal-browser';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { Tooltip } from 'react-tooltip';

import Loading from '@/app/(home)/loading';
import { Button } from '@/components/ui/Button';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';
import { AppConfig } from '@/utils/AppConfig';

const RefreshDashboards: React.FC = () => {
  const [dashboards, setDashboards] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  const refreshDashboardFromServer = (dashboardId: any) => {
    let url = ApiUtilities.getApiServerUrlBsm;
    if (dashboardId) {
      url += ApiUtilities.apiPath.refreshDashboard.url;
    } else {
      url += ApiUtilities.apiPath.refreshAllDashboard.url;
    }
    let msToken: any = localStorage.getItem('msToken') || '{}';
    msToken = JSON.parse(msToken);
    const data: any = {
      accessToken: msToken.accessToken,
    };
    if (dashboardId) {
      data.id = dashboardId;
    }
    setLoading(true);
    apiService
      .postRequest(url, data)
      .then((res) => {
        if (res.data.error) {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: res.data.message,
            }),
          );
          setLoading(false);
          return;
        }
        setLoading(false);
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'success',
            title: 'Success',
            content: dashboardId
              ? 'We have triggered refresh for your Dashboard, it should be refreshed in around 10 minutes!'
              : 'We have triggered refresh for your Dashboard, it should be refreshed in around 5 minutes!',
          }),
        );
      })
      .catch((error) => {
        setLoading(false);
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Error',
            content: error.message,
          }),
        );
      });
  };

  const getMsToken = async (dashboardId: any = null) => {
    const tenantId = '1806032f-a719-453e-8e14-5a5385413df4';
    const clientId = '6be5d16c-02ff-4645-ba60-19093582ebd9';
    const redirectUri = `${AppConfig.network.webServer.protocol}/refreshDashboards`;

    let pca: PublicClientApplication;
    const msalConfig: Configuration = {
      auth: {
        clientId,
        authority: `https://login.microsoftonline.com/${tenantId}`,
        redirectUri,
      },
    };

    const msalInstance = new PublicClientApplication(msalConfig);
    await msalInstance.initialize();
    const request = {
      scopes: ['https://analysis.windows.net/powerbi/api/Tenant.Read.All'],
    };

    return msalInstance
      .acquireTokenPopup(request)
      .then((tokenResponse) => {
        localStorage.setItem('msToken', JSON.stringify(tokenResponse));
        refreshDashboardFromServer(dashboardId);
        return tokenResponse;
      })
      .catch(async (error) => {
        setToastAlert({
          isToastOpen: true,
          intent: 'error',
          title: 'Permission Denied',
          content: 'You do not have permission to refresh the Dashboard',
        });
        if (error instanceof InteractionRequiredAuthError) {
          return pca.acquireTokenPopup(request);
        }
        return null;
      });
  };

  const refreshDashboards = async (dashboardId = null) => {
    let msToken: any = localStorage.getItem('msToken') || '{}';
    msToken = JSON.parse(msToken);
    if (msToken.accessToken && new Date(msToken.expiresOn) > new Date()) {
      refreshDashboardFromServer(dashboardId);
    } else {
      msToken = await getMsToken(dashboardId);
    }
  };

  useEffect(() => {
    async function getResolvedData() {
      const url =
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getDashboards.url;
      const apiData = apiService.getRequest(url);
      apiData.then((resp) => {
        setDashboards(resp.data);
      });
    }
    getResolvedData();
    const requestedDashboardId: any = localStorage.getItem(
      'requestedDashboardId',
    );
    localStorage.removeItem('requestedDashboardId');
    if (requestedDashboardId) {
      requestedDashboardId === 'All'
        ? refreshDashboards()
        : refreshDashboards(requestedDashboardId);
    }
  }, []);

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Refresh Dashboards
        </span>
      </div>
      <div className="dataSet-table flex h-[88vh] w-full flex-col rounded border border-lightgray-100 bg-white-200 p-4">
        <div className="flex w-full flex-row items-center space-x-4">
          <Button intent="primary" onClick={() => refreshDashboards()}>
            <span className="flex flex-row space-x-2">
              <img
                className="w-[16px]"
                alt="refresh"
                src="/assets/images/refresh.svg"
              />
              <span>Refresh All</span>
            </span>
          </Button>
        </div>
        <div className="mr-6 mt-5 grid h-fit grid-cols-3 overflow-y-scroll pb-2 md:grid-cols-3 lg:grid-cols-3">
          {dashboards.map((cardData: any, index: any) => (
            <div
              className={` ${
                index === 0 ? 'insight-card' : ''
              } insight-enterprise-card col-span-1 mb-4 mr-4 h-fit  rounded-md border bg-white-200`}
              key={cardData.id}
            >
              <div className="flex justify-between rounded-t-md border-b-[0.5px] border-lightgray-100 bg-blue-200 py-2">
                <span className="px-4 font-sans text-sm font-semibold text-white-200">
                  {cardData.dashboardName}
                </span>
                <span
                  className="flex cursor-pointer flex-row"
                  onClick={() => refreshDashboards(cardData.id)}
                >
                  <img
                    className="w-[16px]"
                    alt="refresh"
                    src="/assets/images/refresh.svg"
                  />
                  <span className="mx-2 text-xs font-light text-white-200">
                    Refresh
                  </span>
                </span>
              </div>
              <div className="flex w-full flex-col px-4 pb-4">
                <div className="grid h-[65%] grid-cols-2 overflow-auto pt-4">
                  {cardData.datasets.map((datasetName: any, rowIndex: any) => (
                    <div
                      key={datasetName}
                      className={`col-span-1 m-1 mb-2 flex min-h-[24px] w-full flex-row items-center justify-between rounded-sm bg-slate-100 px-2 ${
                        rowIndex % 2 === 0 ? 'border-r-2 border-white-200' : ''
                      }`}
                    >
                      <button
                        type="button"
                        className="flex text-left font-sans text-xxs font-thin text-blueGray-300"
                      >
                        <div
                          data-tooltip-id="info-tooltip"
                          data-tooltip-html={datasetName}
                          className="truncateData"
                          style={{ maxWidth: '10vw' }}
                        >
                          {datasetName}
                        </div>
                      </button>
                    </div>
                  ))}
                </div>
                <Tooltip
                  className="info-tooltip"
                  id="info-tooltip"
                  place="top"
                  variant="info"
                  positionStrategy="fixed"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      {loading && <Loading />}
    </div>
  );
};

export default RefreshDashboards;
