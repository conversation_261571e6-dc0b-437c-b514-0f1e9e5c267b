/* eslint-disable jsx-a11y/anchor-is-valid */
import eData from 'public/testdata/logicalEntities/validateAttribute.json';
import React, { useState } from 'react';

import { Link } from '@/components/ui/Link';
import Table from '@/components/ui/Table';
import { createMenuName } from '@/constants/menuSvg';

import { Searchbar } from '../../ui/Searchbar';

const ValidateAttributeTable: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Constants
  const header: any = [
    {
      title: 'DataSET',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Table Name',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },

    {
      title: 'DataType',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Length',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Mode DataType',
      optionsEnabled: false,
      options: [],
      inputType: 'select',
    },
    {
      title: 'Mode DEscription',
      optionsEnabled: false,
      options: [],
      inputType: 'input',
    },
  ];

  // States
  const [searchItem, setSearchItem] = useState('');
  const [data, setData]: any = useState(eData);
  const [disableAttributeButton] = useState(false);
  const [filteredData, setFilteredData] = useState(data);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const goToAddNewAttribute = () => {
    // setDisableAttributeButton(true);
    const newRow = {
      id: `New Dataset ${data.length + 1}`,
      selected: true,
      components: [
        {
          value: 'NA',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: 'NA',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'input',
          inputValue: '',
          placeholder: 'Attribute Name',
        },
        {
          value: 'NA',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: 'NA',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'select',
          placeholder: 'Datatype',
          selectList: [
            { name: 'Int' },
            { name: 'String' },
            { name: 'boolean' },
          ],
        },
        {
          value: '',
          disabled: false,
          userAvatarLink: null,
          type: 'input',
          placeholder: 'Test Description',
        },
      ],
    };
    const updatedData = [newRow, ...data];
    setData(updatedData);
    setFilteredData(updatedData);
  };

  return (
    <div className="flex  h-[66vh] w-full flex-col space-y-6 pt-6">
      <div className="flex w-full flex-row items-center justify-between">
        <div className="w-[40vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        {props.mode !== 'View' && (
          <Link
            content={createMenuName('Add Attribute')}
            onClick={goToAddNewAttribute}
            intent={disableAttributeButton === true ? 'disabled' : 'enabled'}
          />
        )}
      </div>

      <div className=" w-full overflow-auto">
        <Table
          isCondensedTable
          isDashTable
          header={header}
          data={filteredData}
          enableOptions={false}
          enabledCross={props.mode !== 'View'}
          isView={props.mode === 'View'}
        />
      </div>
    </div>
  );
};

export default ValidateAttributeTable;
