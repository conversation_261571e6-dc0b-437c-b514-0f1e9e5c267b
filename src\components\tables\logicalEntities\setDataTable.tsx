import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Searchbar } from '@/components/ui/Searchbar';
import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setTableAttributeMap } from '@/slices/logicalEntityCrudSlice';

const SetDataTable: React.FC<{
  selectedTableData: any;
  mode: string | string[] | undefined;
  onRowSelection?: any;
  activatedAttributes?: any;
}> = ({
  selectedTableData,
  mode,
  onRowSelection,
  activatedAttributes = {},
}) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const tableAttributeMap = useSelector(
    (state: any) => state.logicalEntityCrud.tableAttributeMap,
  );

  // Constants
  const header: any = [
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'checkbox',
    },
    {
      title: 'DataType',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Length',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Source Description',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Modify Attribute Name',
      optionsEnabled: false,
      options: [],
      inputType: 'input',
      width: 'min-w-[200px]',
    },
  ];

  // States
  const [data, setData]: any = useState();
  const [searchItem, setSearchItem] = useState('');
  // const data = useState<any>(props.mode === 'Edit' ? eData : []);
  const [filteredData, setFilteredData] = useState<any>();

  // Methods
  const handleRowSelection = (rows: any) => {
    if (rows) {
      const filteredRows = rows.filter((row: any) => row.selected);
      onRowSelection(filteredRows);
    }
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
    if (value === '') {
      handleRowSelection(filteredValues);
    }
  };

  const resolveComponent = (value: any, type: any, placeholder: any) => {
    const resolvedComponent: any = {
      value,
      disabled: false,
      userAvatarLink: null,
      type,
    };
    if (placeholder) {
      resolvedComponent.placeholder = placeholder;
    }
    return resolvedComponent;
  };

  // const onRowDataChange = (rows: any) => {
  //   const selectedRows = rows.filter((row: any) => row.selected);
  //   const storedActivatedAttributes = structuredClone(activatedAttributes);
  //   // selectedRows.foreach((row)=> {
  //   //   row

  //   // })
  // }
  const resolveTableData = (dataToResolve: any) => {
    const transformedData: any = [];
    const selectedAttributes =
      activatedAttributes[selectedTableData?.datasetName]?.tables[
        selectedTableData?.tableName
      ] || {};
    dataToResolve?.forEach((row: any, index: number) => {
      const value = selectedAttributes[row.attribute_name]?.alias || '';
      transformedData.push({
        id: row?.attribute_name,
        selected: !!selectedAttributes[row.attribute_name],
        components: [],
      });
      transformedData[index].components.push(
        resolveComponent(row?.attribute_name, 'checkbox', ''),
      );
      transformedData[index].components.push(
        resolveComponent(row?.attribute_datatype, 'text', ''),
      );
      transformedData[index].components.push(
        resolveComponent(row?.length, 'text', ''),
      );
      transformedData[index].components.push(
        resolveComponent(row?.description, 'text', ''),
      );
      transformedData[index].components.push(
        resolveComponent(value || row?.description, 'input', 'Attribute name'),
      );
    });
    return transformedData;
  };

  // Effects
  useEffect(() => {
    function getResolvedData() {
      if (
        tableAttributeMap[selectedTableData?.dataSetId]?.[
          selectedTableData?.tableId
        ]
      ) {
        setData(
          resolveTableData(
            tableAttributeMap[selectedTableData?.dataSetId]?.[
              selectedTableData?.tableId
            ],
          ),
        );
        setFilteredData(
          resolveTableData(
            tableAttributeMap[selectedTableData?.dataSetId]?.[
              selectedTableData?.tableId
            ],
          ),
        );
        dispatch(setIsLoading(false));
      } else {
        const apiData = apiService.getRequest(
          `https://4fxepgdat0.execute-api.us-east-2.amazonaws.com/dev/v1/datasets/${selectedTableData?.dataSetId}/tables/${selectedTableData?.tableId}/attributes`,
        );
        apiData
          .then((resp) => {
            const tam = structuredClone(tableAttributeMap);
            tam[selectedTableData?.dataSetId] =
              tam[selectedTableData?.dataSetId] || {};
            tam[selectedTableData?.dataSetId][selectedTableData?.tableId] =
              resp.data.data;
            dispatch(setTableAttributeMap(tam));
            setData(resolveTableData(resp.data.data));
            setFilteredData(resolveTableData(resp.data.data));
            dispatch(setIsLoading(false));
          })
          .catch((err) => {
            console.log(err);
            dispatch(setIsLoading(false));
          });
      }
    }
    if (
      // !filteredData &&
      selectedTableData?.dataSetId &&
      selectedTableData?.tableId
    ) {
      dispatch(setIsLoading(true));
      getResolvedData();
    }
  }, [selectedTableData]);

  if (!filteredData) {
    return null;
  }

  return (
    <div className="h-full w-full">
      {selectedTableData?.tableName !== '' && (
        <div className="flex flex-col p-4 py-0">
          <div className="flex w-full flex-col">
            <span className=" font-sans text-base font-semibold leading-8 text-blueGray-300">
              {selectedTableData?.tableName} Details
            </span>
          </div>
          <div className="h-full w-full">
            {' '}
            <div className="my-1 w-[35vw]">
              <Searchbar
                value={searchItem}
                placeholder="Search Connection"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <div
              className={`w-full overflow-auto ${
                mode !== 'View' ? 'h-[48vh]' : 'h-[37vh]'
              }`}
            >
              <Table
                isCondensedTable
                isDashTable={false}
                header={header}
                data={filteredData}
                enableOptions={false}
                enabledCross={false}
                isView={mode === 'View'}
                onRowSelection={onRowSelection}
                onRowChange={onRowSelection}
              />
            </div>
          </div>
        </div>
      )}
      {selectedTableData?.tableName === '' && (
        <div className="flex h-full w-full items-center justify-center">
          <span className="font-sans text-sm font-medium text-gray-400">
            Please Select a Table to see the Details
          </span>
        </div>
      )}
    </div>
  );
};

export default SetDataTable;
