/* eslint-disable import/no-extraneous-dependencies */
import 'react-js-cron/dist/styles.css';

import { Cron } from 'react-js-cron';
import { useDispatch } from 'react-redux';

import { setSchedulingConfig } from '@/slices/sourceSystemCrudSlice';

import { Paragraph } from '../../../../ui/Paragraph';

export function JobSchedules({ servedData }: any) {
  // Essentials
  const dispatch = useDispatch();
  return (
    <div className="my-3 h-[calc(100vh-250px)] overflow-auto pt-3">
      <Paragraph
        content="Setup the schedule to initiate the integration"
        intent="p300"
      />
      <div className="mt-5 flex flex-row items-center justify-start space-x-3">
        <span className="flex font-sans text-sm leading-4 tracking-[0.2px] text-gray-500">
          <div className="inline w-full">
            <Cron
              readOnly
              value={servedData.connection_details.schedule || '* * * * *'}
              setValue={(v: any) => {
                dispatch(setSchedulingConfig(v));
              }}
              humanizeLabels
              humanizeValue
              clearButton
            />
          </div>
        </span>
      </div>
    </div>
  );
}
