'use client';

import React, { createContext, useContext, useState, ReactNode, useMemo, useCallback } from 'react';
import { flushSync } from 'react-dom';

import { GenProService } from '@/service/genpro.service';
import { APIService } from '@/service/api.service';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';

interface WorkflowData {
  id?: number;
  workflow_name?: string;
  current_stage?: string;
  automation_enabled?: boolean;
  file_uploads?: any[];
  created_at?: string;
  has_required_files?: boolean;
  missing_file_types?: string[];
  stages_completed?: string[];
  next_stage?: string;
}

interface WorkflowContextType {
  currentWorkflow: WorkflowData | null;
  uploadedFiles: any[];
  currentStage: string;
  isAutomated: boolean;
  isAutomatedProcessing: boolean;
  automatedProgress: number;
  genproService: GenProService;
  
  // Actions
  setUploadedFiles: (files: any[]) => void;
  setCurrentStage: (stage: string) => void;
  setIsAutomated: (automated: boolean) => void;
  createWorkflow: (workflowName: string, fileIds: number[]) => Promise<any>;
  startWorkflow: (workflowId: number) => Promise<any>;
  getWorkflowStatus: (workflowId: number) => Promise<any>;
  nextStage: (workflowId: number) => Promise<any>;
  updateWorkflow: (workflow: WorkflowData) => void;
  startAutomatedPipeline: () => Promise<void>;
  resetWorkflow: () => void;
}

const GenProWorkflowContext = createContext<WorkflowContextType | undefined>(undefined);

export const useGenProWorkflow = () => {
  const context = useContext(GenProWorkflowContext);
  if (!context) {
    throw new Error('useGenProWorkflow must be used within a GenProWorkflowProvider');
  }
  return context;
};

interface GenProWorkflowProviderProps {
  children: ReactNode;
}

export const GenProWorkflowProvider: React.FC<GenProWorkflowProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const genproService = useMemo(() => new GenProService(apiService), [apiService]);

  const [currentWorkflow, setCurrentWorkflow] = useState<WorkflowData | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [currentStage, setCurrentStage] = useState<string>('ingestion');
  const [isAutomated, setIsAutomated] = useState<boolean>(false);
  const [isAutomatedProcessing, setIsAutomatedProcessing] = useState<boolean>(false);
  const [automatedProgress, setAutomatedProgress] = useState<number>(0);

  const createWorkflow = async (workflowName: string, fileIds: number[]) => {
    try {
      const response = await genproService.createWorkflow(workflowName, isAutomated, fileIds);
      const workflow = response.data;
      setCurrentWorkflow(workflow);
      setCurrentStage(workflow.current_stage || 'ingestion');
      return workflow;
    } catch (error) {
      console.error('Failed to create workflow:', error);
      throw error;
    }
  };

  const startWorkflow = async (workflowId: number) => {
    try {
      const response = await genproService.startWorkflow(workflowId, isAutomated);
      if (response.data.current_stage) {
        setCurrentStage(response.data.current_stage);
      }
      return response.data;
    } catch (error) {
      console.error('Failed to start workflow:', error);
      throw error;
    }
  };

  const getWorkflowStatus = async (workflowId: number) => {
    try {
      const response = await genproService.getWorkflowStatus(workflowId);
      const workflow = response.data;
      setCurrentWorkflow(workflow);
      setCurrentStage(workflow.current_stage || 'ingestion');
      return workflow;
    } catch (error) {
      console.error('Failed to get workflow status:', error);
      throw error;
    }
  };

  const nextStage = async (workflowId: number) => {
    try {
      const response = await genproService.nextStage(workflowId);
      if (response.data.current_stage) {
        setCurrentStage(response.data.current_stage);
      }
      return response.data;
    } catch (error) {
      console.error('Failed to proceed to next stage:', error);
      throw error;
    }
  };

  const updateWorkflow = (workflow: WorkflowData) => {
    setCurrentWorkflow(workflow);
    if (workflow.current_stage) {
      setCurrentStage(workflow.current_stage);
    }
  };

  const resetWorkflow = useCallback(() => {
    console.log('🔄 Resetting workflow state to initial values');
    setCurrentWorkflow(null);
    setUploadedFiles([]);
    setCurrentStage('ingestion');
    setIsAutomated(false);
    setIsAutomatedProcessing(false);
    setAutomatedProgress(0);
  }, []); // Empty dependency array since it only uses setters

  const startAutomatedPipeline = async () => {
    console.log('🚀 startAutomatedPipeline called', { 
      isAutomated, 
      uploadedFilesLength: uploadedFiles.length 
    });
    
    if (!isAutomated || uploadedFiles.length === 0) {
      console.log('❌ Pipeline conditions not met:', { isAutomated, uploadedFilesLength: uploadedFiles.length });
      return;
    }

    console.log('✅ Starting automated processing...');
    setIsAutomatedProcessing(true);
    setAutomatedProgress(0);

    try {
      // Stage progression sequence
      const stages = ['ingestion', 'validation', 'transformation', 'distribution', 'export'];
      const stageDurations = [3000, 4000, 5000, 4000, 3000]; // Longer durations for better visibility
      
      console.log('📋 Starting stage loop for stages:', stages);
      
      for (let i = 0; i < stages.length; i++) {
        const stage = stages[i];
        
        console.log(`🔄 Processing stage ${i + 1}/${stages.length}: ${stage}`);
        
        // Update current stage
        flushSync(() => {
          if (stage) {
            setCurrentStage(stage);
          }
        });
        
        // Allow UI to update
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // Skip workflow creation during automated pipeline
        // Workflows should only be created when explicitly requested by user
        // if (i === 0 && !currentWorkflow?.id) {
        //   const workflowName = `Automated Workflow ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`;
        //   const fileIds = uploadedFiles.map(file => file.id);
        //   const workflow = await createWorkflow(workflowName, fileIds);
        //   await startWorkflow(workflow.id);
        // }
        
        // Simulate stage processing with incremental progress updates
        const stageStartProgress = (i / stages.length) * 100;
        const stageEndProgress = ((i + 1) / stages.length) * 100;
        const progressSteps = 20; // Number of progress updates per stage
        
        for (let step = 0; step <= progressSteps; step++) {
          const stepProgress = (step / progressSteps);
          const currentProgress = stageStartProgress + (stepProgress * (stageEndProgress - stageStartProgress));
          
          if (step % 10 === 0) {
            console.log(`📊 ${stage} progress: ${currentProgress.toFixed(1)}%`);
          }
          
          flushSync(() => {
            setAutomatedProgress(currentProgress);
          });
          
          // Wait between progress updates
          const stageDuration = stageDurations[i] || 1000; // Default 1 second if undefined
          await new Promise(resolve => setTimeout(resolve, stageDuration / progressSteps));
        }
        
        console.log(`✅ Completed stage: ${stage}`);
      }
      
      // Final completion
      console.log('🎉 All stages completed! Finalizing...');
      setAutomatedProgress(100);
      setCurrentStage('export');
      
      // Small delay before finishing to show completion
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('🏁 Automation completed successfully');
      
    } catch (error) {
      console.error('❌ Automated pipeline failed:', error);
      throw error;
    } finally {
      console.log('🧹 Cleaning up...');
      setIsAutomatedProcessing(false);
    }
  };

  const value: WorkflowContextType = {
    currentWorkflow,
    uploadedFiles,
    currentStage,
    isAutomated,
    isAutomatedProcessing,
    automatedProgress,
    genproService,
    
    setUploadedFiles,
    setCurrentStage,
    setIsAutomated,
    createWorkflow,
    startWorkflow,
    getWorkflowStatus,
    nextStage,
    updateWorkflow,
    startAutomatedPipeline,
    resetWorkflow,
  };

  return (
    <GenProWorkflowContext.Provider value={value}>
      {children}
    </GenProWorkflowContext.Provider>
  );
};