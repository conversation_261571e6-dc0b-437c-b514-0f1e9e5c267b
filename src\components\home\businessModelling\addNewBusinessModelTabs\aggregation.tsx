/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-array-index-key */

import { useRouter } from 'next/navigation';
import eData from 'public/testdata/businessModelling/viewBusinessSchema.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import AggregateTable from '@/components/tables/businessModelling/aggregationTable';
import ListBox from '@/components/ui/ListBox';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setSelectedBusinessSchema } from '@/slices/businessModelCrudSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const Aggregation: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // States
  const [result, setResult] = useState<any>();
  const [attributes, setAttributes] = useState<{ name: string }[]>([]);
  const [businessSchemaListItem, setBusinessSchemaListItem] =
    useState<any>(null);

  // Selectors
  const selectedBusinessSchema = useSelector(
    (state: any) => state.businessModelCrud.selectedBusinessSchema,
  );

  // Methods
  const getAttributes: any = () => {
    const attributesList: any[] = [];
    let businessEntity: any = null;
    if (result) {
      businessEntity = result.find(
        (entity: any) =>
          entity.business_entity_name === selectedBusinessSchema?.name,
      );
      if (businessEntity) {
        for (const attr of businessEntity.attributes) {
          const attrObj = {
            name: attr.attribute_name,
            id: attr.attribute_name,
          };
          if (attributesList.indexOf(attrObj) === -1) {
            attributesList.push(attrObj);
          }
        }
      }
    }
    setAttributes(attributesList);
  };

  const prepareBusinessSchemaList = (entities: any) => {
    const businessSchemaList: any[] = [];
    for (const entity of entities) {
      const entityObj = {
        name: entity.business_entity_name,
        id: entity.business_entity_name,
      };
      businessSchemaList.push(entityObj);
    }
    setBusinessSchemaListItem(businessSchemaList);
  };

  const getBusinessSchemaList = () => {
    dispatch(setIsLoading(true));
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getBusinessModelAtttributes.url;
    const apiData = apiService.getRequest(url);
    apiData
      .then((resp) => {
        setResult(resp?.data);
        prepareBusinessSchemaList(resp.data);
      })
      .finally(() => dispatch(setIsLoading(false)));
  };

  // Effects

  useEffect(() => {
    if (selectedBusinessSchema) {
      getAttributes();
    }
  }, [selectedBusinessSchema]);

  useEffect(() => {
    if (!businessSchemaListItem) {
      getBusinessSchemaList();
    }
  }, []);

  return (
    <div className="flex h-[74vh] w-full flex-col space-y-4 overflow-auto px-6 pb-4 pt-2">
      {businessSchemaListItem?.length > 0 && (
        <div className=" w-full">
          {props.mode !== 'View' ? (
            <div className="flex flex-col space-y-1">
              <span className="font-sans text-sm font-semibold text-gray-500">
                Select Business Schema to Aggregate
              </span>
              <div className="w-[280px]">
                <ListBox
                  customStyle="min-h-[38px] py-[6px]"
                  items={businessSchemaListItem}
                  isPreselected={false}
                  placeholder="Select Schema"
                  onSelectionChange={(selectedValue) => {
                    dispatch(setSelectedBusinessSchema(selectedValue));
                  }}
                  selectedItems={[selectedBusinessSchema]}
                  name="name"
                />
              </div>
            </div>
          ) : (
            <div className="flex flex-row space-x-11 ">
              <div className="flex flex-col space-y-2">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Aggregating with
                </span>{' '}
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {eData.aggregation.aggregatingWith}
                </span>
              </div>
              <div className="flex flex-col space-y-2">
                <span className="font-sans text-xs font-medium text-gray-400">
                  Description
                </span>{' '}
                <span className="font-sans text-sm font-semibold text-gray-500">
                  {eData.aggregation.description}
                </span>
              </div>
            </div>
          )}
        </div>
      )}
      {JSON.stringify(selectedBusinessSchema) !== JSON.stringify({}) && (
        <div className="h-[85%] w-full rounded bg-gray-300 px-4 py-2">
          <AggregateTable mode={props.mode} attributesList={attributes} />
        </div>
      )}
    </div>
  );
};

export default Aggregation;
