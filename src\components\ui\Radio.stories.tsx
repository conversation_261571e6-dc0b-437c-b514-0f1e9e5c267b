import type { Meta, StoryObj } from '@storybook/react';

import { Radio } from './Radio';

const meta = {
  title: 'UI/Radio',
  component: Radio,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

const Enabled: Story = {
  args: {
    intent: 'enabled',
    label: 'Label',
    name: 'name',
  },
};
const Disabled: Story = {
  args: {
    intent: 'disabled',
    label: 'Label',
    name: 'name',
  },
};

export { Disabled, Enabled };
