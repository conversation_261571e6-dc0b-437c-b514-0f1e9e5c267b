'use client';

import React, { useState } from 'react';
import { 
  Activity, 
  Filter, 
  Download, 
  RefreshCw, 
  Check<PERSON>ircle,
  XCircle,
  AlertTriangle,
  Clock
} from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import Table from '@/components/ui/Table';

const GenProLogs = () => {
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const logData = [
    {
      id: 1,
      timestamp: '2025-01-15 14:30:25',
      workflowId: 'WF-2025-001',
      stage: 'Ingestion',
      status: 'completed',
      message: 'Successfully uploaded Base Data (POs) - 1,247 records processed',
      duration: '2.3s',
      user: '<EMAIL>',
      fileSize: '2.4 MB'
    },
    {
      id: 2,
      timestamp: '2025-01-15 14:32:18',
      workflowId: 'WF-2025-001',
      stage: 'Validation',
      status: 'completed',
      message: 'Data validation passed - All entity codes mapped successfully',
      duration: '1.8s',
      user: '<EMAIL>',
      fileSize: null
    },
    {
      id: 3,
      timestamp: '2025-01-15 14:35:42',
      workflowId: 'WF-2025-001',
      stage: 'Transformation',
      status: 'completed',
      message: 'Applied 15 transformation rules - Final SMC and Vessel columns populated',
      duration: '4.2s',
      user: '<EMAIL>',
      fileSize: null
    },
    {
      id: 4,
      timestamp: '2025-01-15 14:38:15',
      workflowId: 'WF-2025-001',
      stage: 'Distribution',
      status: 'completed',
      message: 'Generated pivot tables and calculated BF allocations - Total: $25,000',
      duration: '3.1s',
      user: '<EMAIL>',
      fileSize: null
    },
    {
      id: 5,
      timestamp: '2025-01-15 14:40:33',
      workflowId: 'WF-2025-001',
      stage: 'Export',
      status: 'completed',
      message: 'Report finalized and exported - Version 1.0 created',
      duration: '1.5s',
      user: '<EMAIL>',
      fileSize: '1.8 MB'
    },
    {
      id: 6,
      timestamp: '2025-01-14 16:22:10',
      workflowId: 'WF-2025-002',
      stage: 'Validation',
      status: 'error',
      message: 'Unknown entity code "NEW-001" found - Manual mapping required',
      duration: '0.8s',
      user: '<EMAIL>',
      fileSize: null
    },
    {
      id: 7,
      timestamp: '2025-01-14 11:15:45',
      workflowId: 'WF-2025-003',
      stage: 'Ingestion',
      status: 'warning',
      message: 'File naming convention warning - Processed with default settings',
      duration: '1.2s',
      user: '<EMAIL>',
      fileSize: '3.1 MB'
    },
    {
      id: 8,
      timestamp: '2025-01-14 09:30:12',
      workflowId: 'WF-2025-004',
      stage: 'Transformation',
      status: 'running',
      message: 'Processing SEACHEF allocation rules...',
      duration: '45.2s',
      user: '<EMAIL>',
      fileSize: null
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { intent: 'success', content: 'Completed' },
      error: { intent: 'error', content: 'Error' },
      warning: { intent: 'warning', content: 'Warning' },
      running: { intent: 'info', content: 'Running' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { intent: 'neutral', content: 'Unknown' };
    
    return {
      type: 'badges',
      badges: [{ intent: config.intent, content: config.content }]
    };
  };

  const filteredLogs = logData.filter(log => {
    const matchesStatus = filterStatus === 'all' || log.status === filterStatus;
    const matchesSearch = searchTerm === '' || 
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.workflowId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.stage.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesSearch;
  });

  const header = [
    { title: 'Timestamp', align: 'left' },
    { title: 'Workflow ID', align: 'left' },
    { title: 'Stage', align: 'left' },
    { title: 'Status', align: 'left' },
    { title: 'Message', align: 'left' },
    { title: 'Duration', align: 'left' },
    { title: 'User', align: 'left' },
  ];

  const data = filteredLogs.map((log) => ({
    id: log.id,
    components: [
      { type: 'text', value: log.timestamp },
      { type: 'text', value: log.workflowId },
      { type: 'text', value: log.stage },
      getStatusBadge(log.status),
      { 
        type: 'text', 
        value: (
          <div className="flex items-start space-x-2 max-w-md">
            {getStatusIcon(log.status)}
            <span className="text-sm">{log.message}</span>
          </div>
        )
      },
      { type: 'text', value: log.duration },
      { type: 'text', value: log.user },
    ],
  }));

  const statusCounts = {
    all: logData.length,
    completed: logData.filter(log => log.status === 'completed').length,
    error: logData.filter(log => log.status === 'error').length,
    warning: logData.filter(log => log.status === 'warning').length,
    running: logData.filter(log => log.status === 'running').length,
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
          <Activity className="w-6 h-6 mr-2" />
          GenPro Process Logs
        </h1>
        <p className="text-gray-600">
          View detailed logs of all GenPro workflow executions and system activities
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Logs</p>
              <p className="text-2xl font-bold text-gray-800">{statusCounts.all}</p>
            </div>
            <Activity className="w-8 h-8 text-gray-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{statusCounts.completed}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Errors</p>
              <p className="text-2xl font-bold text-red-600">{statusCounts.error}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Warnings</p>
              <p className="text-2xl font-bold text-yellow-600">{statusCounts.warning}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Running</p>
              <p className="text-2xl font-bold text-blue-600">{statusCounts.running}</p>
            </div>
            <Clock className="w-8 h-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-white-200 rounded-lg border border-lightgray-100 p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filter by Status:</span>
            </div>
            <div className="flex space-x-2">
              {['all', 'completed', 'error', 'warning', 'running'].map((status) => (
                <Button
                  key={status}
                  intent={filterStatus === status ? 'primary' : 'secondary'}
                  className="text-xs px-3 py-1"
                  onClick={() => setFilterStatus(status)}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Button>
              ))}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Input
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />
            <Button intent="secondary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button intent="secondary">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* Logs Table */}
      <div className="bg-white-200 rounded-lg border border-lightgray-100">
        <Table 
          header={header} 
          data={data} 
          isDashTable={false} 
          enableOptions={false}
          hasPagination={true}
        />
      </div>
    </div>
  );
};

export default GenProLogs;
