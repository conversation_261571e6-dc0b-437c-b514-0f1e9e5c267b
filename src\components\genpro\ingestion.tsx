'use client';

import { AlertCircle, CheckCircle, Home, Loader2 } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';

import UploadFile from '@/components/home/<USER>/uploadNewFile';
import { Button } from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface IngestionProps {
  onNext?: () => void;
  onFileUpload?: (files: any) => void;
}

interface UploadedFileData {
  id: number;
  original_file_name: string;
  file_name: string;
  file_size: number;
  content_type: string;
  file_type: string;
  validation_status: string;
  uploaded_at: string;
  file_metadata: {
    records_count: number;
    columns_count: number;
    size_mb: string;
  };
  rawFile: File;
}

const Ingestion = ({ onNext, onFileUpload }: IngestionProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);

  const [uploadedFileData, setUploadedFileData] = useState<UploadedFileData[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isUploadCancelled, setIsUploadCancelled] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const MINIMUM_FILES_REQUIRED = 3;
  const MAXIMUM_FILES_ALLOWED = 3;

  // API Functions
  const uploadWorkflowFiles = async (files: File[]) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}/file-upload/upload_workflow_files/`;
    const formData = new FormData();
    files.forEach((file) => {
      formData.append(`files`, file);
    });
    formData.append('include_validation', 'false');
    return await apiService.genproPostRequest(url, formData);
  };

  const getFileTypeFromName = (fileName: string): string => {
    const lowerName = fileName.toLowerCase();
    if (lowerName.includes('po')) return 'PO';
    if (lowerName.includes('bf_overview') || lowerName.includes('overview')) return 'BF_OVERVIEW';
    if (lowerName.includes('vessel') || lowerName.includes('mapping')) return 'VESSEL_MAPPING';
    if (lowerName.includes('expense')) return 'EXPENSE';
    return 'UNKNOWN';
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  const showWarning = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'warning', title: 'Warning', content: message }));
  };

  const handleFiles = async (files: File[]) => {
    if (files.length > MAXIMUM_FILES_ALLOWED) {
      showError(`Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Cannot upload ${files.length} file(s).`);
      return;
    }

    setIsUploading(true);

    try {
      const fileData: UploadedFileData[] = files.map((file: File, index: number) => ({
        id: index + 1,
        original_file_name: file.name,
        file_name: file.name,
        file_size: file.size,
        content_type: file.type,
        file_type: getFileTypeFromName(file.name),
        validation_status: 'PENDING',
        uploaded_at: new Date().toISOString(),
        file_metadata: {
          records_count: 0,
          columns_count: 0,
          size_mb: (file.size / (1024 * 1024)).toFixed(2)
        },
        rawFile: file
      }));

      setUploadedFileData(fileData);
      onFileUpload?.(fileData);
    } catch (error: any) {
      console.error('File processing failed:', error);
      showError('Failed to process files', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setUploadedFileData([]);
    onFileUpload?.([]);
    setIsUploadCancelled(true);
    
    setTimeout(() => {
      setIsUploadCancelled(false);
    }, 100);
  };

  const handleBackToLanding = () => {
    handleCancel();
    router.push('/genpro');
  };

  const handleProceed = async () => {
    if (uploadedFileData.length < MINIMUM_FILES_REQUIRED) {
      showWarning(`Please upload at least ${MINIMUM_FILES_REQUIRED} files to proceed. Currently have ${uploadedFileData.length}/${MINIMUM_FILES_REQUIRED} files.`);
      return;
    }

    if (uploadedFileData.length > MAXIMUM_FILES_ALLOWED) {
      showError(`Maximum ${MAXIMUM_FILES_ALLOWED} files allowed. Please remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s) before proceeding.`);
      return;
    }
    
    setSaveLoading(true);
    
    try {
      const rawFiles = uploadedFileData
        .filter((fileData) => fileData.rawFile)
        .map((fileData) => fileData.rawFile);
      
      if (rawFiles.length > 0) {
        const response = await uploadWorkflowFiles(rawFiles);
        
        showSuccess('Files have been uploaded to the workflow system.');
        
        if (response.data) {
          onFileUpload?.(response.data);
        }
      }
      
      onNext?.();
    } catch (error: any) {
      console.error('Upload to workflow failed:', error);
      showError('Failed to upload files to workflow system', error);
    } finally {
      setSaveLoading(false);
    }
  };

  const fileUploaded = uploadedFileData.length >= MINIMUM_FILES_REQUIRED && uploadedFileData.length <= MAXIMUM_FILES_ALLOWED;

  return (
    <div className="flex flex-col h-full space-y-6 p-6">
      {/* Main Upload Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden flex-shrink-0">
          <div className="flex-1 p-4">
            <UploadFile onFileUpload={handleFiles} isUploadCancled={isUploadCancelled} />
            {isUploading && (
              <div className="mt-4">
                <LoadingSpinner text="Processing files..." />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          {/* Status Section */}
          <div className="flex items-center space-x-3">
            {fileUploaded ? (
              <CheckCircle className="size-4 text-green-500" />
            ) : (
              <AlertCircle className="size-4 text-orange-500" />
            )}
            <span className={`text-xs font-medium ${
              fileUploaded ? 'text-green-600' : 'text-orange-600'
            }`}>
              {fileUploaded
                ? `${uploadedFileData.length} files attached ✓` 
                : uploadedFileData.length > MAXIMUM_FILES_ALLOWED
                  ? `${uploadedFileData.length} files attached (Max ${MAXIMUM_FILES_ALLOWED})`
                  : `${uploadedFileData.length}/${MINIMUM_FILES_REQUIRED} files attached`
              }
            </span>
            {uploadedFileData.length < MINIMUM_FILES_REQUIRED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-orange-600">
                  Need {MINIMUM_FILES_REQUIRED - uploadedFileData.length} more file(s)
                </span>
              </>
            )}
            {uploadedFileData.length > MAXIMUM_FILES_ALLOWED && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-red-600">
                  Remove {uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s)
                </span>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              className="flex h-[32px] items-center px-3 text-xs"
              type="button"
              onClick={handleBackToLanding}
              intent="secondary"
            >
              <Home className="w-3 h-3 mr-1" />
              Back to Landing
            </Button>
            {uploadedFileData.length > 0 && (
              <Button
                className="flex h-[32px] items-center px-3 text-xs"
                type="button"
                onClick={handleCancel}
                intent="secondary"
              >
                Clear Files
              </Button>
            )}
            <Button
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              type="submit"
              disabled={!fileUploaded || saveLoading}
              onClick={handleProceed}
              aria-label={
                uploadedFileData.length < MINIMUM_FILES_REQUIRED
                  ? `Upload ${MINIMUM_FILES_REQUIRED - uploadedFileData.length} more file(s) to continue`
                  : uploadedFileData.length > MAXIMUM_FILES_ALLOWED
                    ? `Remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} file(s) to continue`
                    : 'Proceed to validation step'
              }
            >
              {saveLoading ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Uploading...
                </>
              ) : uploadedFileData.length < MINIMUM_FILES_REQUIRED ? (
                `Upload ${MINIMUM_FILES_REQUIRED - uploadedFileData.length} More File(s)`
              ) : uploadedFileData.length > MAXIMUM_FILES_ALLOWED ? (
                `Remove ${uploadedFileData.length - MAXIMUM_FILES_ALLOWED} File(s)`
              ) : (
                'Proceed to Validation →'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Ingestion;
