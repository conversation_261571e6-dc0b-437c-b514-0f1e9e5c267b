/* eslint-disable import/prefer-default-export */
export const surveyJson = {
  progressBarType: 'pages',
  showProgressBar: 'top',
  showQuestionNumbers: 'off',
  pages: [
    {
      title: 'General / Business',
      elements: [
        {
          type: 'checkbox',
          name: 'Select the Functional Areas',
          title: 'Select the Functional Areas',
          isRequired: true,
          choices: [
            { value: 'F&A', text: 'F&A' },
            { value: 'Procurement Ops', text: 'Procurement Ops' },
            {
              value: 'Customer Services Ops',
              text: 'Customer Services Ops',
            },
          ],
        },
      ],
    },
    {
      title: 'General / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'What is the Priority Functional Area',
          title: 'What is the Priority Functional Area?',
          isRequired: true,
          choices: [
            { value: 'F&A', text: 'F&A' },
            { value: 'Procurement Ops', text: 'Procurement Ops' },
            {
              value: 'Customer Services Ops',
              text: 'Customer Services Ops',
            },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'checkbox',
          name: 'Please Select the Sub Areas',
          title: 'Please Select the Sub Areas',
          isRequired: true,
          choices: [
            { value: 'AR', text: 'AR' },
            { value: 'AP', text: 'AP' },
            {
              value: 'GL/FP&A',
              text: 'GL/FP&A',
            },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'What is the Functional Currency',
          title: 'What is the Functional Currency?',
          isRequired: true,
          choices: [
            { value: 'USD', text: 'USD' },
            { value: 'GBP', text: 'GBP' },
            {
              value: 'Euro',
              text: 'Euro',
            },
            { value: 'INR', text: 'INR' },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'What is Currency Translation Method',
          title: 'What is Currency Translation Method?',
          isRequired: true,
          choices: [
            { value: 'Daily Rates', text: 'Daily Rates' },
            { value: 'Monthly Rates', text: 'Monthly Rates' },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'What is the Business Reporting Language',
          title: 'What is the Business Reporting Language?',
          isRequired: true,
          choices: [
            { value: 'English', text: 'English' },
            { value: 'German', text: 'German' },
            { value: 'French', text: 'French' },
            { value: 'Spanish', text: 'Spanish' },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'checkbox',
          name: 'Select System of Record',
          title: 'Select System of Record',
          isRequired: true,
          choices: [
            { value: 'ERP', text: 'ERP' },
            { value: 'EPM', text: 'EPM' },
            {
              value: 'Consolidation',
              text: 'Consolidation',
            },
            {
              value: 'Data Mart',
              text: 'Data Mart',
            },
          ],
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: [
            'SAP',
            'Oracle',
            'Microsoft Dynamics',
            'Netsuite',
            'Infor',
            'Hyperion',
            'Cognos',
            'Adaptive',
            'OneStream',
            'SAP BPC/SAC',
          ],
          name: 'Detail System of Record/s',
          title: 'Detail System of Record/s',
        },
        {
          name: 'SAP AR Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'SAP'",
          title: 'Enter SAP Version',
        },
        {
          name: 'Oracle AR Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Oracle'",
          title: 'Enter Oracle Version',
        },
        {
          name: 'Microsoft Dynamics Version',
          type: 'text',
          visibleIf:
            "{Detail System of Record/s} contains 'Microsoft Dynamics'",
          title: 'Enter Microsoft Dynamics Version',
        },
        {
          name: 'Infor Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Netsuite'",
          title: 'Enter Infor Version',
        },
        {
          name: 'Infor Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Infor'",
          title: 'Enter Infor Version',
        },
        {
          name: 'Hyperion Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Hyperion'",
          title: 'Enter Hyperion Version',
        },
        {
          name: 'Cognos Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Cognos'",
          title: 'Enter Cognos Version',
        },
        {
          name: 'Adaptive Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'Adaptive'",
          title: 'Enter Adaptive Version',
        },
        {
          name: 'OneStream Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'OneStream'",
          title: 'Enter OneStream Version',
        },
        {
          name: 'SAP BPC/SAC Version',
          type: 'text',
          visibleIf: "{Detail System of Record/s} contains 'SAP BPC/SAC'",
          title: 'Enter SAP BPC/SAC Version',
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'Entity Structure',
          title: 'Select entity structure mapping Type',
          isRequired: true,
          choices: [
            { value: 'Enter', text: 'Enter' },
            { value: 'Upload', text: 'Upload' },
          ],
        },
        {
          type: 'file',
          visibleIf: "{Entity Structure} = 'Upload'",
          title: 'Please upload your entity structure mapping File',
          name: 'entitystructuremappingFile',
          storeDataAsText: false,
          waitForUpload: true,
          allowMultiple: true,
          maxSize: 102400,
          hideNumber: true,
        },
        {
          type: 'matrixdynamic',
          name: 'entitystructuremappingTable',
          visibleIf: "{Entity Structure} = 'Enter'",
          title: 'Please enter entity structure mapping',
          colCount: 2,
          columns: [
            {
              name: 'Legal Entities',
              title: 'Legal Entities',
              cellType: 'text',
              isRequired: true,
            },
            {
              name: 'Group',
              title: 'Group',
              cellType: 'text',
              isRequired: true,
            },
          ],
          rowCount: 1,
          addRowText: 'Add More Rows',
          removeRowText: 'Remove Row',
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'radiogroup',
          name: 'Entity Segments',
          title: 'Select entity segments mapping Type',
          isRequired: true,
          choices: [
            { value: 'Enter', text: 'Enter' },
            { value: 'Upload', text: 'Upload' },
          ],
        },
        {
          type: 'file',
          visibleIf: "{Entity Segments} = 'Upload'",
          title: 'Please upload your entity segments mapping File',
          name: 'entitySegmentsMappingFile',
          storeDataAsText: false,
          waitForUpload: true,
          allowMultiple: true,
          maxSize: 102400,
          hideNumber: true,
        },
        {
          type: 'matrixdynamic',
          name: 'entitySegmentsMappingTable',
          visibleIf: "{Entity Segments} = 'Enter'",
          title: 'Please enter entity segments mapping',
          colCount: 2,
          columns: [
            {
              name: 'Legal Entities',
              title: 'Legal Entities',
              cellType: 'text',
              isRequired: true,
            },
            {
              name: 'Group',
              title: 'Group',
              cellType: 'text',
              isRequired: true,
            },
            {
              name: 'Segments',
              title: 'Segments',
              cellType: 'text',
              isRequired: true,
            },
          ],
          rowCount: 1,
          addRowText: 'Add More Rows',
          removeRowText: 'Remove Row',
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: ['Sender', 'Receiver'],
          name: 'intercompanyRow9',
          title: 'Intercompany Row 9',
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'boolean',
          name: 'Is trading partner field followed',
          title: 'Is trading partner field followed?',
          valueTrue: 'Yes',
          valueFalse: 'No',
          renderAs: 'radio',
          isRequired: true,
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'boolean',
          name: 'Is it comprehensive',
          title: 'Is it comprehensive?',
          valueTrue: 'Yes',
          valueFalse: 'No',
          renderAs: 'radio',
          isRequired: true,
        },
      ],
    },
    {
      title: 'F&A / Business',
      elements: [
        {
          type: 'checkbox',
          name: 'Org Structure',
          title: 'Org Structure',
          isRequired: true,
          choices: [
            { value: 'Segments', text: 'Segments' },
            { value: 'Profit Centers', text: 'Profit Centers' },
            {
              value: 'Cost Centers',
              text: 'Cost Centers',
            },
          ],
          otherText:
            'Others (In case of multiple value, separate it by commas)',
          showOtherItem: true,
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select System of Record for AR Transactions',
          title: 'Select System of Record for AR Transactions',
          isRequired: true,
          choices: [
            { value: 'ERP', text: 'ERP' },
            { value: 'CRM', text: 'CRM' },
            {
              value: 'MDM',
              text: 'MDM',
            },
          ],
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: ['SAP', 'Oracle', 'Infor M3', 'MSD', 'JDE', 'Quickbooks'],
          name: 'Select ERP for AR Transactions',
          visibleIf:
            "{Select System of Record for AR Transactions} contains 'ERP'",
          title: 'Select ERP for AR Transactions',
        },
        {
          name: 'SAP AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'SAP'",
          title: 'Enter SAP Version',
        },
        {
          name: 'Oracle AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'Oracle'",
          title: 'Enter Oracle Version',
        },
        {
          name: 'Infor M3 AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'Infor M3'",
          title: 'Enter Infor M3 Version',
        },
        {
          name: 'MSD AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'MSD'",
          title: 'Enter MSD Version',
        },
        {
          name: 'JDE AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'JDE'",
          title: 'Enter JDE Version',
        },
        {
          name: 'Quickbooks AR Version',
          type: 'text',
          visibleIf: "{Select ERP for AR Transactions} contains 'Quickbooks'",
          title: 'Enter Quickbooks Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select CRM system',
          title: 'Select CRM system',
          visibleIf:
            "{Select System of Record for AR Transactions} contains 'CRM'",
          isRequired: true,
          choices: ['SFDC', 'Dynamics', 'Hubspot', 'Zoho', 'Oracle'],
        },
        {
          name: 'SFDC Version',
          type: 'text',
          visibleIf: "{Select CRM system} contains 'SFDC'",
          title: 'Enter SFDC Version',
        },
        {
          name: 'Dynamics Version',
          type: 'text',
          visibleIf: "{Select CRM system} contains 'Dynamics'",
          title: 'Enter Dynamics Version',
        },
        {
          name: 'Hubspot Version',
          type: 'text',
          visibleIf: "{Select CRM system} contains 'Hubspot'",
          title: 'Enter Hubspot Version',
        },
        {
          name: 'Zoho Version',
          type: 'text',
          visibleIf: "{Select CRM system} contains 'Zoho'",
          title: 'Enter Zoho Version',
        },
        {
          name: 'Oracle2 Version',
          type: 'text',
          visibleIf: "{Select CRM system} contains 'Oracle'",
          title: 'Enter Oracle Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select Tax System',
          title: 'Select Tax System',
          isRequired: true,
          choices: ['Vertex', 'Taxware', 'ERP'],
        },
        {
          name: 'Vertex Version',
          type: 'text',
          visibleIf: "{Select Tax System} contains 'Vertex'",
          title: 'Enter Vertex Version',
        },
        {
          name: 'Taxware Version',
          type: 'text',
          visibleIf: "{Select Tax System} contains 'Taxware'",
          title: 'Enter Taxware Version',
        },
        {
          name: 'ERP Version',
          type: 'text',
          visibleIf: "{Select Tax System} contains 'ERP'",
          title: 'Enter ERP Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select System of Record for AP Transactions',
          title: 'Select System of Record for AP Transactions',
          isRequired: true,
          choices: ['ERP', 'SRM', 'MDM'],
        },
      ],
    },

    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: ['SAP', 'Oracle', 'Infor M3', 'MSD', 'JDE', 'Quickbooks'],
          visibleIf:
            "{Select System of Record for AP Transactions} contains 'ERP'",
          name: 'Select ERP for AP Transactions',
          title: 'Select ERP for AP Transactions',
        },
        {
          name: 'SAP AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'SAP'",
          title: 'Enter SAP Version',
        },
        {
          name: 'Oracle AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'Oracle'",
          title: 'Enter Oracle Version',
        },
        {
          name: 'Infor M3 AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'Infor M3'",
          title: 'Enter Infor M3 Version',
        },
        {
          name: 'MSD AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'MSD'",
          title: 'Enter MSD Version',
        },
        {
          name: 'JDE AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'JDE'",
          title: 'Enter JDE Version',
        },
        {
          name: 'Quickbooks AP Version',
          type: 'text',
          visibleIf: "{Select ERP for AP Transactions} contains 'Quickbooks'",
          title: 'Enter Quickbooks Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select Sourcing System',
          title: 'Select Sourcing System',
          visibleIf:
            "{Select System of Record for AP Transactions} contains 'SRM'",
          isRequired: true,
          choices: ['Ariba', 'OB10', 'Coupa'],
        },
        {
          name: 'Ariba Version',
          type: 'text',
          visibleIf: "{Select Sourcing System} contains 'Ariba'",
          title: 'Enter Ariba Version',
        },
        {
          name: 'OB10 Version',
          type: 'text',
          visibleIf: "{Select Sourcing System} contains 'OB10'",
          title: 'Enter OB10 Version',
        },
        {
          name: 'Coupa Version',
          type: 'text',
          visibleIf: "{Select Sourcing System} contains 'Coupa'",
          title: 'Enter Coupa Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Select System of Record for GL/FP&A Transactions',
          title: 'Select System of Record for GL/FP&A Transactions',
          isRequired: true,
          choices: ['ERP', 'Planning', 'Consolidation'],
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          visibleIf:
            "{Select System of Record for GL/FP&A Transactions} contains 'ERP'",
          choices: ['SAP', 'Oracle', 'Infor M3', 'MSD', 'JDE', 'Quickbooks'],
          name: 'Select ERP for GL Transactions',
          title: 'Select ERP for GL Transactions',
        },
        {
          name: 'SAP GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'SAP'",
          title: 'Enter SAP Version',
        },
        {
          name: 'Oracle GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'Oracle'",
          title: 'Enter Oracle Version',
        },
        {
          name: 'Infor M3 GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'Infor M3'",
          title: 'Enter Infor M3 Version',
        },
        {
          name: 'MSD GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'MSD'",
          title: 'Enter MSD Version',
        },
        {
          name: 'JDE GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'JDE'",
          title: 'Enter JDE Version',
        },
        {
          name: 'Quickbooks GL Version',
          type: 'text',
          visibleIf: "{Select ERP for GL Transactions} contains 'Quickbooks'",
          title: 'Enter Quickbooks Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: [
            'Hyperion',
            'PBCS',
            'Adaptive',
            'Cognos',
            'SAP BPC',
            'Board',
          ],
          name: 'Select Planning System used',
          visibleIf:
            "{Select System of Record for GL/FP&A Transactions} contains 'Planning'",
          title: 'Select Planning System used',
        },
        {
          name: 'Hyperion Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'Hyperion'",
          title: 'Enter Hyperion Version',
        },
        {
          name: 'PBCS Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'PBCS'",
          title: 'Enter PBCS Version',
        },
        {
          name: 'Adaptive Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'Adaptive'",
          title: 'Enter Adaptive Version',
        },
        {
          name: 'Cognos Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'Cognos'",
          title: 'Enter Cognos Version',
        },
        {
          name: 'SAP BPC Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'SAP BPC'",
          title: 'Enter SAP BPC Version',
        },
        {
          name: 'Board Planning Version',
          type: 'text',
          visibleIf: "{Select Planning System used} contains 'Board'",
          title: 'Enter Board Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'tagbox',
          isRequired: true,
          choices: [
            'Hyperion',
            'PBCS',
            'Adaptive',
            'Cognos',
            'SAP BPC',
            'Board',
          ],
          name: 'Select Consolidation System used',
          visibleIf:
            "{Select System of Record for GL/FP&A Transactions} contains 'Consolidation'",
          title: 'Select Consolidation System used',
        },
        {
          name: 'Hyperion Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'Hyperion'",
          title: 'Enter Hyperion Version',
        },
        {
          name: 'PBCS Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'PBCS'",
          title: 'Enter PBCS Version',
        },
        {
          name: 'Adaptive Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'Adaptive'",
          title: 'Enter Adaptive Version',
        },
        {
          name: 'Cognos Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'Cognos'",
          title: 'Enter Cognos Version',
        },
        {
          name: 'SAP BPC Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'SAP BPC'",
          title: 'Enter SAP BPC Version',
        },
        {
          name: 'Board Consolidation Version',
          type: 'text',
          visibleIf: "{Select Consolidation System used} contains 'Board'",
          title: 'Enter Board Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'boolean',
          name: 'Do you have Data Warehousing tool',
          title: 'Do you have Data Warehousing tool?',
          valueTrue: 'Yes',
          valueFalse: 'No',
          renderAs: 'radio',
          isRequired: true,
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Which Data Warehousing tool You have',
          title: 'Which Data Warehousing tool You have?',
          isRequired: true,
          visibleIf: "{Do you have Data Warehousing tool} = 'Yes'",
          otherText:
            'Others (In case of multiple value, separate it by commas)',
          showOtherItem: true,
          choices: ['SQL', 'SAP BW', 'Oracle DWH'],
        },
        {
          name: 'SQL Version',
          type: 'text',
          visibleIf: "{Which Data Warehousing tool You have} contains 'SQL'",
          title: 'Enter SQL Version',
        },
        {
          name: 'SAP BW Version',
          type: 'text',
          visibleIf: "{Which Data Warehousing tool You have} contains 'SAP BW'",
          title: 'Enter SAP BW Version',
        },
        {
          name: 'Oracle DWH Version',
          type: 'text',
          visibleIf:
            "{Which Data Warehousing tool You have} contains 'Oracle DWH'",
          title: 'Enter Oracle DWH Version',
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'boolean',
          name: 'Do you have Cloud Environment Set up for the Oraganization',
          title: 'Do you have Cloud Environment Set up for the Oraganization?',
          valueTrue: 'Yes',
          valueFalse: 'No',
          renderAs: 'radio',
          isRequired: true,
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Which Cloud Products do you use',
          title: 'Which Cloud Products do you use?',
          isRequired: true,
          visibleIf:
            "{Do you have Cloud Environment Set up for the Oraganization} = 'Yes'",
          otherText:
            'Others (In case of multiple value, separate it by commas)',
          showOtherItem: true,
          choices: ['AWS', 'Azure', 'Google Cloud', 'Snowflake', 'Databricks'],
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'boolean',
          name: 'Do you have a Data Warehousing platform',
          title: 'Do you have a Data Warehousing platform?',
          valueTrue: 'Yes',
          valueFalse: 'No',
          renderAs: 'radio',
          isRequired: true,
        },
      ],
    },
    {
      title: 'F&A / Tech',
      elements: [
        {
          type: 'checkbox',
          name: 'Which Data Warehousing platform you have',
          title: 'Which Data Warehousing platform you have?',
          isRequired: true,
          visibleIf: "{Do you have a Data Warehousing platform} = 'Yes'",
          otherText:
            'Others (In case of multiple value, separate it by commas)',
          showOtherItem: true,
          choices: ['Snowflake', 'Synapse', 'Databricks', 'BigQuery'],
        },
      ],
    },
  ],
};
