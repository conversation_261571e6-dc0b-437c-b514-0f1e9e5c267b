import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const iconButton = cva('rounded p-3', {
  variants: {
    intent: {
      primary: [
        'bg-blue-200',
        'active:bg-blue-200',
        'hover:bg-blue-300',
        'focus:bg-blue-400',
        'disabled:bg-blue-200/50',
      ],
      secondary: [
        'bg-gray-200',
        'active:bg-gray-200',
        'hover:bg-lightgray-400',
        'focus:bg-lightgray-200',
        'disabled:bg-gray-200/50',
      ],
      tertiary: ['text-gray-400'],
    },
  },
  compoundVariants: [
    {
      intent: 'primary',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'primary',
  },
});

export interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof iconButton> {}

export const IconButton: React.FC<IconButtonProps> = ({
  className,
  intent,
  ...props
}) => (
  <button
    type="button"
    className={iconButton({ intent, className })}
    {...props}
  />
);
