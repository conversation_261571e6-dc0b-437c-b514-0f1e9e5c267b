/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable import/no-cycle */

import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import CreateCustomAttribute from '@/components/home/<USER>/addLogicalEntitiesTabs/modal/createCustomAttribute';
import { Link } from '@/components/ui/Link';
import { Modal } from '@/components/ui/Modal';
import { Searchbar } from '@/components/ui/Searchbar';
import Table from '@/components/ui/Table';
import { createMenuName } from '@/constants/menuSvg';
import { setCustomAttributes } from '@/slices/businessModelCrudSlice';

const CustomAttribute: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const dispatch = useDispatch();

  // Constants
  const header: any = [
    {
      title: 'Attribute',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Data Type',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Description',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'FormulA',
      optionsEnabled: false,
      options: [],
    },
  ];

  // Selectors
  const customAttributes = useSelector(
    (state: any) => state.businessModelCrud.customAttributes,
  );

  // States
  const [searchItem, setSearchItem] = useState('');
  const [data, setData]: any = useState(customAttributes || []);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [newValueToAdd, setNewValues] = useState({
    attributeName: '',
    dataType: '',
    description: '',
    formula: '',
  });

  const [selectedCustomAttribute, setSelectedCustomAttribute] = useState<any>({
    attribute_name: '',
    attribute_datatype: '',
    formula: '',
    description: '',
  });

  const [filteredData, setFilteredData] = useState(data);
  const [isFormValid, setFormIsValid] = useState(false);
  const [keyID, setKeyID] = useState(null);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const handleFormSubmit = (values: any, errors: any) => {
    if (errors.formula === undefined && errors.attributeName === undefined) {
      setFormIsValid(true);
      setNewValues(values);
      setSelectedCustomAttribute(values);
    }
  };

  const addNewRow = () => {
    const newRow = {
      id: keyID ?? `Attribute ${data.length + 1}`,
      selected: true,
      components: [
        {
          value: newValueToAdd.attributeName,
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: newValueToAdd.dataType,
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: newValueToAdd.description,
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: newValueToAdd.formula,
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
      ],
    };
    if (keyID) {
      const KeyUpdatedData = [
        newRow,
        ...filteredData.filter((attr: any) => attr.id !== keyID),
      ];
      setData(KeyUpdatedData);
      dispatch(setCustomAttributes(KeyUpdatedData));
      setFilteredData(KeyUpdatedData);
      setKeyID(null);
    } else {
      const updatedData = [newRow, ...data];
      setData(updatedData);
      dispatch(setCustomAttributes(updatedData));
      setFilteredData(updatedData);
    }
    setIsPopupOpen(false);
  };

  const addCustomeAttribute = (key?: any) => {
    setIsPopupOpen(true);
    if (key) {
      setKeyID(key);
      setSelectedCustomAttribute({
        attribute_name: filteredData.filter((attr: any) => attr.id === key)[0]
          ?.components[0].value,
        attribute_datatype: filteredData.filter(
          (attr: any) => attr.id === key,
        )[0]?.components[1].value,
        formula: filteredData.filter((attr: any) => attr.id === key)[0]
          ?.components[3].value,
        description: filteredData.filter((attr: any) => attr.id === key)[0]
          ?.components[2].value,
      });
    } else {
      setSelectedCustomAttribute({
        attribute_name: '',
        attribute_datatype: '',
        formula: '',
        description: '',
      });
    }
  };

  const addNewCustomeAttribute = () => {
    addCustomeAttribute();
  };

  const closePopupModal = () => {
    setIsPopupOpen(false);
  };

  return (
    <div className="flex h-full flex-col">
      <div className=" flex w-full flex-row items-center justify-between">
        <span className="font-sans text-base font-semibold text-blueGray-300">
          Custom Attributes
        </span>
        {props.mode !== 'View' ? (
          <Link
            content={createMenuName('Add Custom Attribute')}
            onClick={addNewCustomeAttribute}
          />
        ) : (
          <div className="w-[30vw]">
            {' '}
            <Searchbar
              value={searchItem}
              placeholder="Search"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        )}
      </div>
      <div className="mt-4 h-[85%] w-full overflow-auto bg-gray-300">
        {data.length === 0 ? (
          <div className="flex h-full w-full items-center justify-center">
            <span className="font-sans text-sm font-medium text-gray-400">
              No Custom Attributes Available
            </span>
          </div>
        ) : (
          <Table
            isCondensedTable
            isDashTable={false}
            header={header}
            onRowChange={(data1: any) => dispatch(setCustomAttributes(data1))}
            data={props.mode !== 'View' ? data : filteredData}
            isEyeVisible={props.mode === 'View'}
            EyeFunction={addCustomeAttribute}
            enableOptions={false}
            isView={props.mode === 'View'}
            isEdit={props.mode !== 'View'}
            onEdit={(key: any) => addCustomeAttribute(key)}
            enabledCross={props.mode !== 'View'}
          />
        )}
      </div>
      <Modal
        isActionButtonVisible
        cancelbuttonText="Cancel"
        actionbuttonText="Proceed"
        isOpen={isPopupOpen}
        headerTitle="Create Custom Attribute"
        disablePrimaryFooterBtn={!isFormValid}
        isCancelVisible
        component={
          <CreateCustomAttribute
            data={selectedCustomAttribute}
            onFormSubmit={handleFormSubmit}
          />
        }
        footerPrimaryEventHandler={() => {
          addNewRow();
        }}
        closeModal={closePopupModal}
      />
    </div>
  );
};

export default CustomAttribute;
