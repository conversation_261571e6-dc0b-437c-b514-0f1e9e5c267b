import { Formik } from 'formik';
import React from 'react';

import { Input } from '@/components/ui/Input';
import { InputFile } from '@/components/ui/InputFile';
import { Radio } from '@/components/ui/Radio';
import { TextField } from '@/components/ui/TextField';

const UploadLogicalEntity: React.FC<{
  hierarchyModal?: boolean;
  setSelectedFile?: any;
  selectedFile?: any;
  setMappingDesc?: any;
  MappingEntityName?: any;
  setMappingEntityName?: any;
  MappingDesc?: any;
  setOrientation?: any;
}> = ({
  hierarchyModal,
  setSelectedFile,
  selectedFile,
  setMappingDesc,
  MappingEntityName,
  setMappingEntityName,
  MappingDesc,
  setOrientation,
}) => {
  return (
    <Formik
      initialValues={{ EName: '', file: '', description: '' }}
      validate={(values) => {
        const errors: any = {};
        if (!values.EName) {
          errors.EName = 'Required.';
        }
        if (!values.file) {
          errors.file = 'Required.';
        }
        if (!values.description) {
          errors.description = 'Required.';
        }
        return errors;
      }}
      onSubmit={(values, { setSubmitting }) => {
        setTimeout(() => {
          console.log(values);
          // can show loader if wanted
          setSubmitting(false);
          // router.push('/dashboard');
        }, 400);
      }}
    >
      {({ values, errors, touched, handleSubmit }) => (
        <form
          className=" flex h-full w-full flex-col space-y-6 px-4 pt-7"
          onSubmit={handleSubmit}
        >
          <div className="flex flex-row space-x-6">
            <div className="w-[20%]">
              {' '}
              <Input
                label={hierarchyModal ? 'Hierarchy Setup Name' : 'Entity Name'}
                name="EName"
                type="text"
                className=""
                placeholder={
                  hierarchyModal
                    ? 'Enter Hierarchy Setup Name'
                    : 'Enter Entity Name'
                }
                onChange={(e) => setMappingEntityName(e.target.value)}
                // onBlur={handleBlur}
                value={MappingEntityName}
                intent={errors.EName && touched.EName ? 'hasError' : 'enabled'}
                error={errors.EName && touched.EName && errors.EName}
              />
            </div>
            <div className=" flex w-[27%] flex-col space-y-1">
              <InputFile
                label="Select the file to upload"
                name="file"
                type="file"
                className=""
                accept=".csv"
                placeholder="Select File"
                onChange={(e: any) => {
                  setSelectedFile(e.target.files[0]);
                }}
                // onBlur={handleBlur}
                value={values.file}
                ValueName={selectedFile?.name}
                intent={errors.file && touched.file ? 'hasError' : 'enabled'}
                error={errors.file && touched.file && errors.file}
              />
              <span className="font-sans text-[10px] text-gray-400">
                .txt, .csv are accepted
              </span>
            </div>

            {hierarchyModal && (
              <div className="flex flex-col space-y-3">
                <div className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
                  How does your hierarchy represent in file
                </div>
                <div className="flex flex-row space-x-2">
                  <Radio
                    label="Horizontal Orientation"
                    name="Orientation"
                    value="Horizontal Orientation"
                    onChange={(e) => setOrientation(e.target.value)}
                    // onBlur={handleBlur}
                    intent="enabled"
                  />
                  <Radio
                    label="Vertical Orientation"
                    name="Orientation"
                    value="Vertical Orientation"
                    onChange={(e) => setOrientation(e.target.value)}
                    // onBlur={handleBlur}
                    intent="enabled"
                  />
                </div>
              </div>
            )}
          </div>
          <div className="w-[33vw]">
            <TextField
              label="Description"
              name="description"
              className=""
              placeholder="Enter Description"
              onChange={(e) => setMappingDesc(e.target.value)}
              // onBlur={handleBlur}
              value={MappingDesc}
              intent={
                errors.description && touched.description
                  ? 'hasError'
                  : 'enabled'
              }
              error={
                errors.description && touched.description && errors.description
              }
            />
          </div>
        </form>
      )}
    </Formik>
  );
};

export default UploadLogicalEntity;
