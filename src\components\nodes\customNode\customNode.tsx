import './customNode.scss';

import { <PERSON><PERSON>, Position } from 'reactflow';

const CustomNode = ({ data }: any) => {
  return (
    <div className="custom-node">
      <Handle className="left-handle" type="target" position={Position.Left} />
      <Handle
        id="right"
        className="right-handle"
        type="source"
        position={Position.Right}
      />
      <Handle
        id="bottom"
        className="bottom-handle"
        type="source"
        position={Position.Bottom}
      />
      <div>{data.label}</div>
    </div>
  );
};

export default CustomNode;
