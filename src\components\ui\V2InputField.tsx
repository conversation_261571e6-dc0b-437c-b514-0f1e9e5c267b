"use client";

import { cva, type VariantProps } from "class-variance-authority";
import React from "react";
// import requiredStar from "public/assets/images/platform-ops/required_star.svg";

const input = cva(
  "min-w-[270px] rounded border-[1px] p-3 font-sans text-xs font-normal leading-4 text-gray-500",
  {
    variants: {
      intent: {
        enabled: [
          "border-lightgray-100",
          "focus:ring-0",
          "focus:shadow-blue",
          "focus:border-none",
          "text-blueGray-300",
          "placeholder:text-gray-400",
          "active:text-blueGray-300",
          "hover:text-gray-400",
          "focus:text-gray-400",
          "focus:border-purple-200",
          "focus:outline-none",
        ],
        disabled: [
          "border-lightgray-100 ",
          "pointer-events-none",
          "opacity-50",
          "text-gray-400",
          "placeholder:text-gray-400",
        ],
        hasError: [
          "border-red-200",
          "focus:outline-none",
          "text-blueGray-300",
          "placeholder:text-gray-400",
          "active:text-blueGray-300",
          "hover:text-gray-400",
          "focus:text-gray-400",
          "focus:ring-0",
          "focus:shadow-blue",
          "focus:border-none",
          "focus:border-purple-200",
        ],
      },
    },
    compoundVariants: [
      {
        intent: "enabled",
        class: "smallCase",
      },
    ],
    defaultVariants: {
      intent: "enabled",
    },
  }
);

const containerDiv = cva("flex justify-between", {
  variants: {
    intent: {
      enabled: [],
      disabled: ["opacity-50", "text-gray-400"],
      hasError: [],
    },
  },
  compoundVariants: [
    {
      intent: "enabled",
      class: "smallCase",
    },
  ],
  defaultVariants: {
    intent: "enabled",
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof input> {
  error?: any;
  label?: string;
  isRequired?: boolean;
}

export const V2InputField: React.FC<InputProps> = ({
  className,
  content,
  placeholder,
  intent,
  name,
  type,
  label,
  onChange,
  error,
  isRequired,
  ...props
}) => (
  <div
    className={` ${containerDiv({
      className,
      intent,
    })} h-8`}
  >
    {label && (
      <>
        <div className="font-sans text-sm font-normal leading-6 text-gray-500 disabled:opacity-50 flex">
          {label}

          {error && (
            <div className="pl-2">
              <span className="italic text-xxxs text-red-100 flex flex-col pl-2">
                {error}
              </span>
            </div>
          )}
        </div>
      </>
    )}
    <input
      type={type}
      placeholder={placeholder}
      name={name}
      value={content}
      onChange={onChange}
      className={input({ intent, className })}
      {...props}
    />
  </div>
);
