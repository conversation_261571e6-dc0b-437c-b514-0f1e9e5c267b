/* eslint-disable no-param-reassign */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable import/no-named-as-default */
/* eslint-disable react/no-array-index-key */

'use client';

/* eslint-disable jsx-a11y/anchor-is-valid */
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/systemAdmin/adminCards.json';
import React, { useState } from 'react';

import { Searchbar } from '@/components/ui/Searchbar';

const SystemAdminComp: React.FC = () => {
  // Essentials
  const router = useRouter();

  // Constants
  const cardData: any = eData;

  // States
  const [searchCards, setSearchCards] = useState('');
  const [filteredCards, setFilteredCards] = useState(cardData);

  // Methods
  const navigateToCardRoute = (name: string, route: any) => {
    if (name.includes('Create')) {
      const parameter: any = {
        mode: 'Create',
      };
      const queryString = new URLSearchParams(parameter).toString();
      router.push(`${route}?${queryString}`);
    } else {
      router.push(route);
    }
  };

  const search = (query: any, data: any) => {
    if (!query.trim()) {
      return [];
    }
    const results = data.filter((item: any) =>
      item.name.toLowerCase().includes(query.toLowerCase()),
    );
    return results;
  };

  const handleCardSearch = (value: string) => {
    setSearchCards(value);
    if (value) {
      const filteredValues = search(value, cardData);
      setFilteredCards(filteredValues);
    } else {
      const filteredValues = cardData;
      setFilteredCards(filteredValues);
    }
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          System Admin
        </span>
        {/* <div className="create-data-collaboration">
          <Link
            content={createMenuName('Collaborate')}
            onClick={() => goToCollaborate('Create')}
          />
        </div> */}
      </div>
      <div className="flex h-[88vh] w-full flex-col border-lightgray-100 text-blueGray-300">
        <div className="flex h-full w-full flex-col space-y-6">
          <div className="w-[40vw]">
            {' '}
            <Searchbar
              value={searchCards}
              placeholder="Search"
              onChange={(e) => handleCardSearch(e.target.value)}
            />
          </div>
          <div className="flex h-full max-h-[calc(100vh-100px)] w-full flex-wrap overflow-auto">
            {filteredCards.map((data: any, index: any) => (
              <div
                className={`${data.tour} mb-6 mr-6 mt-0 h-[248px] w-[322px]  rounded bg-white-200 p-4`}
                key={index}
              >
                <div className="flex h-[60%] w-full flex-row">
                  <div className="flex w-[75%] flex-col space-y-1 overflow-hidden ">
                    <span className="font-sans text-base font-semibold text-gray-500">
                      {data.name}
                    </span>
                    <span className="font-sans text-xs font-normal text-gray-400">
                      {data.text}
                    </span>
                  </div>
                  <div className="flex w-[25%] items-center justify-center">
                    <div className="flex h-16 w-16 items-center justify-center  rounded-full bg-slate-100">
                      <img
                        className="h-[32px] w-[32px]"
                        src={`/assets/images/sidebar/${data.icon}`}
                        alt={data.icon}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex h-[40%]  flex-col space-y-1 overflow-auto">
                  {data.dashboard.map((dashboard: any, rowIndex: any) => (
                    <button
                      type="button"
                      key={rowIndex}
                      onClick={() => {
                        navigateToCardRoute(dashboard.name, dashboard.href);
                      }}
                      className="flex h-[44px] w-full cursor-pointer items-center rounded-sm bg-slate-100 px-2 font-sans text-sm font-semibold  text-blue-200"
                    >
                      {dashboard.name}
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemAdminComp;
