import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useEffect, useState } from 'react';

import { Button } from './Button';
import { Paragraph } from './Paragraph';

export const Modal: React.FC<any> = ({
  headerTitle,
  component,
  actionbuttonText,
  isOpen,
  footerPrimaryEventHandler,
  footerSecondaryEventHandler,
  footerTertiaryEventHandler,
  disablePrimaryFooterBtn,
  disableSecondaryFooterBtn,
  closeModal,
  isTertiaryBtnVisible,
  cancelbuttonText,
  isActionButtonVisible,
  isCancelVisible,
  isLastTab,
  footerPrimarySubmitHandler,
  panelWidth,
  removePannelBorder,
}) => {
  // States
  const [isSuccess, setIsSuccess] = useState(false);

  // Effects
  useEffect(() => {
    setIsSuccess(false);
  }, [component]);
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-40 h-3/4" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className=" fixed inset-0 bg-blueGray-500" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`h-full ${
                  panelWidth ?? 'w-[85vw]'
                }  max-w-[90%] overflow-hidden bg-gray-300 px-4 pb-3 pt-0 text-left align-middle shadow-xl transition-all`}
              >
                <Dialog.Title
                  as="h3"
                  className=" flex flex-row items-center justify-between px-2 py-[10px] text-lg font-medium  text-blueGray-300"
                >
                  <span>{headerTitle}</span>
                  <button type="button" onClick={closeModal}>
                    <img
                      src="/assets/images/gray-cross.svg"
                      alt="Close modal"
                      className="h-[24px] w-[24px]"
                    />
                  </button>
                </Dialog.Title>
                <div className="h-[67vh] w-full overflow-hidden  border-t border-lightgray-100 pt-2">
                  <div
                    className={`h-[64.5vh] overflow-auto  bg-white-200 ${
                      removePannelBorder
                        ? 'mt-2 border-none'
                        : 'border-[1px] border-lightgray-100'
                    }`}
                  >
                    {component}
                  </div>
                </div>
                <div className="flex flex-row items-center justify-between pb-2 pt-5">
                  <div className="text-left">
                    {isTertiaryBtnVisible && (
                      <div className="flex flex-row items-center justify-between">
                        <Button
                          type="button"
                          onClick={footerTertiaryEventHandler || closeModal}
                          intent="secondary"
                          disabled={disableSecondaryFooterBtn}
                        >
                          Test Connection
                        </Button>
                        {isSuccess && (
                          <>
                            <img
                              className="mx-2"
                              src="/assets/images/approved-green.svg"
                              alt="Custom Calendar"
                            />
                            <Paragraph
                              content="Connection is Valid"
                              intent="p200"
                              className="text-green-100"
                            />
                          </>
                        )}
                      </div>
                    )}
                  </div>
                  <div className="text-right">
                    {isCancelVisible && (
                      <Button
                        type="button"
                        onClick={footerSecondaryEventHandler || closeModal}
                        intent="secondary"
                        disabled={disableSecondaryFooterBtn}
                      >
                        {cancelbuttonText || ' Previous'}
                      </Button>
                    )}
                    {isActionButtonVisible && (
                      <>
                        {' '}
                        {isLastTab ? (
                          <Button
                            disabled={disablePrimaryFooterBtn}
                            type="button"
                            onClick={footerPrimarySubmitHandler}
                            className="ml-4"
                          >
                            {actionbuttonText || 'Create'}
                          </Button>
                        ) : (
                          <Button
                            disabled={disablePrimaryFooterBtn}
                            type="button"
                            onClick={footerPrimaryEventHandler || closeModal}
                            className="ml-4"
                          >
                            {actionbuttonText || 'Next'}
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
