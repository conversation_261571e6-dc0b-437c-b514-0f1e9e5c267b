/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable unused-imports/no-unused-vars */
import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const input = cva(
  'min-w-[216px] rounded border-[1px] bg-white-200 px-3 py-4 font-sans text-sm font-normal leading-4  ',
  {
    variants: {
      intent: {
        enabled: [
          'border-lightgray-100',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'text-blueGray-300',
          'placeholder:text-gray-400',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:border-purple-200',
          'focus:outline-none',
        ],
        disabled: [
          'border-lightgray-100 ',
          'pointer-events-none',
          'opacity-50',
          'text-gray-400',
          'placeholder:text-gray-400',
        ],
        hasError: [
          'border-red-200',
          'focus:outline-none',
          'text-blueGray-300',
          'placeholder:text-gray-400',
          'active:text-blueGray-300',
          'hover:text-gray-400',
          'focus:text-gray-400',
          'focus:ring-0',
          'focus:shadow-blue',
          'focus:border-none',
          'focus:border-purple-200',
        ],
      },
    },
    compoundVariants: [
      {
        intent: 'enabled',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'enabled',
    },
  },
);

const containerDiv = cva(' flex w-[216px] flex-col space-y-2', {
  variants: {
    intent: {
      enabled: [],
      disabled: ['opacity-50', 'text-gray-400'],
      hasError: [],
    },
  },
  compoundVariants: [
    {
      intent: 'enabled',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'enabled',
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof input> {
  error?: any;
  label?: string;
  ValueName?: any;
}

export const InputFile: React.FC<InputProps> = ({
  className,
  value,
  placeholder,
  intent,
  name,
  type,
  onChange,
  label,
  error,
  ValueName,
  ...props
}) => (
  <div className={containerDiv({ className, intent })}>
    {label && (
      <div className="font-sans text-sm font-semibold text-gray-500 disabled:opacity-50">
        {label}
      </div>
    )}{' '}
    {/* <input
      type={type}
      placeholder={placeholder}
      name={name}
      value={content}
      className={input({ intent, className })}
      {...props}
    /> */}
    <div className="flex h-[48px] w-max rounded border text-sm leading-6 text-gray-600">
      <p className="pl-5 pr-20 leading-[48px]">
        {value || ValueName || 'Select or drag file'}
      </p>
      <label className="relative cursor-pointer border-lightgray-100 font-semibold focus-within:outline-none focus-within:ring-2  focus-within:ring-offset-2 ">
        <input
          id="file-upload"
          name="file-upload"
          type="file"
          onChange={onChange}
          value={value}
          className="sr-only h-full"
          {...props}
        />
        <div className="flex h-[48px] items-center justify-center rounded border px-4 font-sans text-xs">
          Select File
        </div>
      </label>
    </div>
    {error && (
      <div className="pt-1 font-sans text-xs font-normal leading-4 text-red-200">
        {error}
      </div>
    )}{' '}
  </div>
);
