/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React from 'react';

const Avatar: React.FC<{
  name: string;
  color?: string;
  className?: string;
}> = ({ name, color, className }) => {
  if (!name) {
    return null;
  }
  const initials = name
    .split(' ')
    .filter((word) => word) // Remove empty strings
    .map((word) => word[0])
    .slice(0, 2)
    .join('');

  return (
    <div
      className={`flex h-12 w-12 items-center justify-center rounded-full  ${
        color ? `bg-${color}/10` : 'bg-purple-300 '
      } ${className}`}
    >
      <span
        className={`font-sans text-xs font-normal leading-4 ${
          color ? `text-${color}` : 'text-purple-400'
        }`}
      >
        {initials}
      </span>
    </div>
  );
};

export default Avatar;
