/* eslint-disable no-param-reassign */
import { createSlice } from '@reduxjs/toolkit';

export interface AppState {
  isLoading: boolean;
  isTourOpen: boolean;
  isChatbotVisible: boolean;
  isChatModalOpen: boolean;
  chatComponent: any;
  sidebarDropdownVisibility: any;
  notificationPanelVisible: boolean;
  sort: any;
  reports: any;
}

const initialState: AppState = {
  isLoading: false,
  isTourOpen: false,
  notificationPanelVisible: false,
  isChatbotVisible: true,
  isChatModalOpen: false,
  chatComponent: null,
  sidebarDropdownVisibility: {
    Insights: false,
    'Data Management': false,
    'Operations': false,
  },
  sort: {
    column: null,
    order: 'asc',
  },
  reports: [],
};

export const appSlice = createSlice({
  name: 'appSlice',
  initialState,
  reducers: {
    setIsLoading: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.isLoading = action.payload;
    },
    setNotificationPanelVisibility: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.notificationPanelVisible = action.payload;
    },
    setIsTourOpen: (state, action) => {
      state.isTourOpen = action.payload;
    },
    setIsChatbotVisible: (state, action) => {
      state.isChatbotVisible = action.payload;
    },
    setSidebarDropDownVisibility: (state, action) => {
      state.sidebarDropdownVisibility = action.payload;
    },
    setSort: (state, action) => {
      state.sort = action.payload;
    },
    setReports: (state, action) => {
      state.reports = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setIsLoading,
  setIsTourOpen,
  setIsChatbotVisible,
  setSidebarDropDownVisibility,
  setNotificationPanelVisibility,
  setSort,
  setReports,
} = appSlice.actions;

export default appSlice.reducer;
