/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable tailwindcss/no-custom-classname */

'use client';

import eData from 'public/testdata/jobMonitor/jobStatus.json';
import React, { useEffect, useState } from 'react';

import Table from '@/components/ui/Table';

import { Searchbar } from '../../ui/Searchbar';

const JobStatusTable: React.FC = () => {
  // Constants
  const header: any = [
    {
      title: 'Job Name',
      optionsEnabled: false,
      options: [],
      inputType: 'checkbox',
    },
    {
      title: 'Started At',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Ended At',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Duration',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Triggered By',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Status',
      optionsEnabled: true,
      options: [],
    },
  ];

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState<any>([]);
  const [scrollPosition, setScrollPosition] = useState(0);

  // Methods
  const handleScroll = (e: any) => {
    setScrollPosition(e.target.scrollTop);
  };

  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = eData.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  useEffect(() => {
    const jobStatusTableData = eData;
    setFilteredData(jobStatusTableData);
  }, []);

  return (
    <div className="business-modelling-table flex h-full min-h-[60vh] w-full flex-col">
      <div className="w-[40vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div
        className=" h-full min-h-[40vh] w-full overflow-auto"
        onScroll={handleScroll}
      >
        <Table
          isDashTable
          enableOptions={false}
          header={header}
          data={filteredData}
          isStatusTable
          scrollPosition={scrollPosition}
        />
      </div>
    </div>
  );
};

export default JobStatusTable;
