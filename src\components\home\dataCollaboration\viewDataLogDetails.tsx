/* eslint-disable unused-imports/no-unused-vars */

'use client';

/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import DatasetDetailsTable from '@/components/tables/dataset/datasetDetailsTable';
import { TestDataService } from '@/service/testdata.service';
import type { RootState } from '@/store/store';

import { Link } from '../../ui/Link';
import { Searchbar } from '../../ui/Searchbar';

const ViewDataLogDetails: React.FC = () => {
  // essentials
  const testDataService = new TestDataService();

  // States
  const [tableData, setTableData] = useState<any>([]);
  const [selectedTableIndex, setSelectedTableIndex] = useState<any>(null);
  const [selectedTableData, setSelectedTableData] = useState({
    header: [],
    data: [],
  });
  const [searchTableItem, setSearchTableItem] = useState('');
  const [searchTableDataset, setSearchTableDataset] = useState('');
  const [searchResults, setSearchResults] = useState(tableData);

  // Selectors
  const selectedDataset = useSelector(
    (state: RootState) => state.datasetCrud.selectedDataset,
  );

  // Methods
  const handleTableSearch = (value: string) => {
    const filteredResults = tableData.filter((item: any) =>
      item.name.toLowerCase().includes(value.toLowerCase()),
    );
    setSearchTableItem(value);
    setSearchResults(filteredResults);
  };

  const handleTableDatasetSearch = (query: string) => {
    const filteredData = tableData[selectedTableIndex].tableData.data.filter(
      (row: any) =>
        row.some((value: any) =>
          value.toLowerCase().includes(query.toLowerCase()),
        ),
    );
    console.log(filteredData);
    // setSelectedTableData({
    //   ...selectedTableData,
    //   data: filteredData,
    // });
    setSearchTableDataset(query);
  };

  const setSelectedIndexAndData = (index: number, item: any) => {
    setSelectedTableIndex(index);
    setSelectedTableData({
      header: item.tableData.header,
      data: item.tableData.data,
    });
    setSearchTableDataset('');
  };

  // Effects
  useEffect(() => {
    // dispatch(setIsLoading(true));
    const fetchData = async () => {
      // const url = `${
      //   ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
      // }/${selectedDataset.id}/tables`;
      // apiService
      //   .getRequest(url)
      //   .then((res) => {
      //     dispatch(setIsLoading(false));
      //     setTableData(res.data);
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //     dispatch(setIsLoading(false));
      //   });
      const response = await testDataService.getAllTestDataFromFile(
        'dataset/datasetDetails',
      );
      setTableData(response);
    };
    fetchData();
  }, [selectedDataset]);

  useEffect(() => {
    setSearchResults(tableData);
  }, [tableData]);

  return (
    <div className="flex w-full flex-col space-y-4 overflow-hidden">
      <div className="flex w-full flex-row items-center space-x-2">
        <Link
          className="px-1"
          href="/dataCollaboration/dataLog?view=normal"
          content={`<img src='/assets/images/backIcon.svg'>`}
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Data Log
        </span>
      </div>
      <div className="flex h-fit min-h-[88vh] w-full flex-row overflow-hidden rounded border-[1px] border-lightgray-100  bg-white-200">
        <div className="flex h-full min-w-[218px] flex-col border-b-[1px] border-r-[1px] border-lightgray-200 ">
          <div className="w-full px-4">
            <span className=" font-sans text-base font-semibold leading-8 text-blueGray-300">
              Tables
            </span>
          </div>
          <div className="flex h-full w-full flex-col space-y-2 px-2 ">
            {' '}
            <Searchbar
              value={searchTableItem}
              placeholder="Search Table"
              onChange={(e) => handleTableSearch(e.target.value)}
            />
            <div className="h-[65vh] pb-2">
              <div className="flex h-[63vh] w-full flex-col space-y-1 overflow-y-auto ">
                {searchResults.map((item: any, index: number) => (
                  <button
                    key={item.id}
                    type="button"
                    onClick={() => {
                      setSelectedIndexAndData(index, item);
                      // get table data
                      // getTableContent(item.id);
                    }}
                    className={`flex  w-full items-center px-[13px] py-3 text-left font-sans text-sm font-semibold  ${
                      selectedTableIndex === index
                        ? 'bg-lightblue-100 text-lightblue-200'
                        : 'text-gray-500 hover:bg-lightblue-100 hover:text-lightblue-200 focus:bg-lightblue-100 focus:text-lightblue-200 active:bg-lightblue-100 active:text-lightblue-200'
                    }`}
                  >
                    {item.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="flex h-full flex-col">
          <div className="w-full p-4">
            <div className="w-[40vw]">
              {' '}
              <Searchbar
                value={searchTableDataset}
                placeholder="Search"
                onChange={(e) => handleTableDatasetSearch(e.target.value)}
              />
            </div>
          </div>
          <div className="">
            <DatasetDetailsTable data={selectedTableData} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewDataLogDetails;
