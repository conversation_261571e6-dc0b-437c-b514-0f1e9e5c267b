import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { <PERSON><PERSON> } from './<PERSON>ert';

const meta = {
  title: 'UI/Alert',
  component: Alert,
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof meta>;
const SuccessIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0 8C0 12.42 3.58 16 8 16C12.42 16 16 12.42 16 8C16 3.58 12.42 0 8 0C3.58 0 0 3.58 0 8ZM11.29 5.29C11.47 5.11 11.72 5 12 5C12.55 5 13 5.45 13 6C13 6.28 12.89 6.53 12.71 6.71L7.71 11.71C7.53 11.89 7.28 12 7 12C6.72 12 6.47 11.89 6.29 11.71L3.29 8.71C3.11 8.53 3 8.28 3 8C3 7.45 3.45 7 4 7C4.28 7 4.53 7.11 4.71 7.29L7 9.59L11.29 5.29Z"
      fill="#1CA675"
    />
  </svg>
);

const InfoIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C12.42 16 16 12.42 16 8C16 3.58 12.42 0 8 0ZM9 3V5H7V3H9ZM6 12V13H10V12H9V6H6V7H7V12H6Z"
      fill="#00AAE7"
    />
  </svg>
);

const WarningIcon = (
  <svg
    width="16"
    height="14"
    viewBox="0 0 16 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.846 12.5L15.856 12.49L8.86032 0.49L8.85033 0.5C8.68044 0.2 8.37063 0 8.00086 0C7.63109 0 7.33128 0.2 7.15139 0.5L7.1414 0.49L0.145772 12.49L0.155766 12.5C0.0658219 12.65 0.00585938 12.81 0.00585938 13C0.00585938 13.55 0.455578 14 1.00523 14H14.9965C15.5461 14 15.9959 13.55 15.9959 13C15.9959 12.81 15.9359 12.65 15.846 12.5ZM9.00024 11.99H7.00149V9.99H9.00024V11.99ZM7.00149 8.99H9.00024V3.99H7.00149V8.99Z"
      fill="#996A13"
    />
  </svg>
);

const ErrorIcon = (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.00086 0.00585938C3.58362 0.00585938 0.00585938 3.58362 0.00585938 8.00086C0.00585938 12.4181 3.58362 15.9959 8.00086 15.9959C12.4181 15.9959 15.9959 12.4181 15.9959 8.00086C15.9959 3.58362 12.4181 0.00585938 8.00086 0.00585938ZM7.00149 12.9977V10.999H9.00024V12.9977H7.00149ZM7.00149 3.00399V9.99961H9.00024V3.00399H7.00149Z"
      fill="#E5333E"
    />
  </svg>
);
const Success: Story = {
  args: {
    intent: 'success',
    children: SuccessIcon,
    title: 'Success message.',
    content:
      'Lorem success ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore.',
  },
};

const Info: Story = {
  args: {
    intent: 'info',
    children: InfoIcon,
    title: 'Info message.',
    content:
      'Lorem info ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore.',
  },
};

const Warning: Story = {
  args: {
    intent: 'warning',
    children: WarningIcon,
    title: 'Warning message.',
    content:
      'Lorem warning ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore.',
  },
};
const Error: Story = {
  args: {
    intent: 'error',
    children: ErrorIcon,
    title: 'Error message.',
    content:
      'Lorem error ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore.',
  },
};
export { Error, Info, Success, Warning };
