'use client';

import React from 'react';
import {
  <PERSON>,
  Unlock,
  CheckCircle,
  AlertTriangle,
  Info,
  XCircle,
  Clock
} from 'lucide-react';

import { Button } from './Button';

export interface StatusBannerProps {
  status: 'locked' | 'draft' | 'success' | 'warning' | 'error' | 'info' | 'pending';
  title: string;
  description: string;
  actionButton?: {
    text: string;
    onClick: () => void;
    intent?: 'primary' | 'secondary';
    icon?: React.ReactNode;
  };
  className?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
}

const StatusBanner: React.FC<StatusBannerProps> = ({
  status,
  title,
  description,
  actionButton,
  className = '',
  dismissible = false,
  onDismiss,
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'locked':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          descriptionColor: 'text-green-700',
          icon: <Lock className="w-5 h-5 text-green-600" />,
        };
      case 'draft':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          descriptionColor: 'text-yellow-700',
          icon: <Unlock className="w-5 h-5 text-yellow-600" />,
        };
      case 'success':
        return {
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-800',
          descriptionColor: 'text-green-700',
          icon: <CheckCircle className="w-5 h-5 text-green-600" />,
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
          descriptionColor: 'text-yellow-700',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-600" />,
        };
      case 'error':
        return {
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
          descriptionColor: 'text-red-700',
          icon: <XCircle className="w-5 h-5 text-red-600" />,
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-800',
          descriptionColor: 'text-blue-700',
          icon: <Info className="w-5 h-5 text-blue-600" />,
        };
      case 'pending':
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          descriptionColor: 'text-gray-700',
          icon: <Clock className="w-5 h-5 text-gray-600" />,
        };
      default:
        return {
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
          descriptionColor: 'text-gray-700',
          icon: <Info className="w-5 h-5 text-gray-600" />,
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`rounded-lg p-4 mb-6 ${config.bgColor} border ${config.borderColor} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {config.icon}
          <div>
            <h3 className={`font-semibold ${config.textColor}`}>
              {title}
            </h3>
            <p className={`text-sm ${config.descriptionColor}`}>
              {description}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {actionButton && (
            <Button
              onClick={actionButton.onClick}
              intent={actionButton.intent || (status === 'locked' ? 'secondary' : 'primary')}
              className="whitespace-nowrap flex justify-center items-center"
            >
              {actionButton.icon && <span className="mr-2">{actionButton.icon}</span>}
              {actionButton.text}
            </Button>
          )}
          {dismissible && onDismiss && (
            <Button
              onClick={onDismiss}
              intent="secondary"
              className="p-2"
            >
              <XCircle className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatusBanner;
