/* eslint-disable  @typescript-eslint/no-shadow */
import { Popover } from '@headlessui/react';
import React, { useEffect, useRef, useState } from 'react';

import { getBgColor } from '@/utils/utilityHelper';

import Table from './Table';

export const PopoverComp: React.FC<{
  scrollPosition?: any;
  data?: any;
  score?: any;
}> = ({ scrollPosition, data, score }) => {
  const menuButtonRef: any = useRef(null);
  const menuItemsRef: any = useRef(null);
  const [isOpen, setIsOpen] = React.useState(false);
  const [openBottom, setOpenBottom] = React.useState(false);
  const [openLeft, setOpenLeft] = React.useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [headers, setHeaders] = useState<any>([]);

  const generateId = () => {
    return Math.random().toString(36).substr(2, 9);
  };

  const resolveData = (data: any, headers: string[]) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data.map((row) => {
      const components = headers.map((header: any) => ({
        value: row[header.title],
        userAvatarLink: null,
        disabled: false,
        type: 'text',
      }));
      return {
        id: row.id || generateId(),
        components,
      };
    });
  };

  const calculatePosition = () => {
    const buttonPosition = menuButtonRef.current.getBoundingClientRect();
    if (buttonPosition && scrollPosition.y / 16 + 350 < buttonPosition.bottom) {
      setOpenBottom(true);
    } else {
      setOpenBottom(false);
    }
    if (buttonPosition && scrollPosition.x / 16 + 525 < buttonPosition.right) {
      setOpenLeft(true);
    } else {
      setOpenLeft(false);
    }
  };

  const resolveHeader = (data: any) => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data.map((row) => ({
      title: row,
      optionsEnabled: true,
      options: ['sort'],
    }));
  };

  useEffect(() => {
    if (data) {
      const tableheader: any = resolveHeader(data.header);
      const tableData: any = resolveData(data.rows, tableheader);
      setHeaders(tableheader);
      setFilteredData(tableData);
    }
  }, [data]);

  useEffect(() => {
    if (headers.length > 0) {
      setHeaders(headers);
    }
  }, [headers]);

  useEffect(() => {
    calculatePosition();
    window.addEventListener('scroll', calculatePosition);
    return () => {
      window.removeEventListener('scroll', calculatePosition);
    };
  }, [data, menuButtonRef]);

  useEffect(() => {
    if (menuItemsRef.current) {
      menuItemsRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
      });
    }
  }, [isOpen]);

  return (
    <Popover className="relative" ref={menuButtonRef}>
      <Popover.Button
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        className="border-none outline-none"
        disabled={data?.header.length === 0 && data?.rows.length === 0}
      >
        <div className="data-completeness-indicator flex flex-row items-center justify-center">
          <div
            className={`h-[19px] w-[19px] ${
              data?.header.length === 0 && data?.rows.length === 0
                ? ''
                : 'cursor-pointer'
            } ${getBgColor(score)} rounded-full`}
          />
        </div>
      </Popover.Button>

      <Popover.Panel
        ref={menuItemsRef}
        className={`${openBottom ? 'bottom-8' : ''} ${
          openLeft ? 'right-8' : ''
        } absolute z-50 `}
      >
        <div className="max-h-[350px] max-w-[600px] overflow-auto rounded border-[1px] border-lightgray-100 bg-white-200 px-2 shadow-md   ">
          {headers.length > 0 && filteredData.length > 0 && (
            <Table
              isDashTable
              enableOptions={false}
              header={headers}
              data={filteredData}
            />
          )}
        </div>
      </Popover.Panel>
    </Popover>
  );
};
