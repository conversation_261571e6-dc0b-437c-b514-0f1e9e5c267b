/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import {
  setAllSystemIntegrationTableDataFetched,
  setIsEditing,
  setIsTestingConnection,
  setIsTestingConnectionValid,
  setOnlineConnectionName,
  setSchedulingConfig,
  setSelectedDomains,
  setSelectedSourceSystem,
  setSelectedTables,
  setSourceSystemConfig,
  setSourceSystemConfigData,
  setTableSchedules,
} from '@/slices/sourceSystemCrudSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Button } from '../../ui/Button';
import { OnlineERPSelector } from './systemIntegrationModals/OnlineERPSelector';
import ViewConnectionDtails from './systemIntegrationModals/viewConnectionDetails';

const AddNewOnlineConnection: React.FC = () => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Derivatives
  const mode: any = searchParams?.get('mode');
  const id: any = searchParams?.get('id');

  // States
  const [selectedTab, setSelectedTab] = useState(0);
  const [completedTabs, setCompletedTabs] = useState([-1]);

  // Selectors
  const selectedSourceSystem = useSelector(
    (state: RootState) => state.sourceSystemsCrud.selectedSourceSystem,
  );
  const SourceSystemCRUD = useSelector(
    (state: RootState) => state.sourceSystemsCrud,
  );
  const isTestingConnectionValid = useSelector(
    (state: RootState) => state.sourceSystemsCrud.isTestingConnectionValid,
  );

  const connectionName = useSelector(
    (state: RootState) => state.sourceSystemsCrud.onlineConnectionName,
  );

  // Methods

  const getDisableOnlineConn = () => {
    return selectedTab === 0
      ? selectedSourceSystem == null
      : selectedTab === 1
      ? Object.keys(SourceSystemCRUD.sourceSystemConfigData).length === 0
      : selectedTab === 2
      ? SourceSystemCRUD.selectedTables.length === 0
      : false;
  };

  const getDisableOnlineConnForEditMode = () => {
    return (
      (selectedTab === 0 ? selectedSourceSystem == null : false) ||
      (selectedTab === 1
        ? Object.keys(SourceSystemCRUD.sourceSystemConfigData).length === 0
        : false) ||
      (selectedTab === 2
        ? SourceSystemCRUD.selectedTables.length === 0
        : false) ||
      (selectedTab === 1 && isTestingConnectionValid === false)
    );
  };

  const cleanStorage = () => {
    dispatch(setSelectedSourceSystem(null));
    dispatch(setSourceSystemConfig([]));
    dispatch(setSourceSystemConfigData({}));
    dispatch(setSelectedDomains([]));
    dispatch(setSelectedTables([]));
    dispatch(setSelectedTables([]));
    dispatch(setTableSchedules({}));
    dispatch(setOnlineConnectionName(''));
    dispatch(setSchedulingConfig(''));
  };

  const goBack = () => {
    cleanStorage();
    router.push('/systemIntegration');
  };

  const createOnlineConnection = () => {
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url;
    const params = {
      source_system_id: selectedSourceSystem,
      connection_type: 'erp',
      connection_name: SourceSystemCRUD.sourceSystemConfigData.connection_name,
      connection_details: {
        schedule: SourceSystemCRUD.schedulingConfig,
        tables: SourceSystemCRUD.selectedTables,
        analytics: SourceSystemCRUD.selectedDomains,
        connection_config: SourceSystemCRUD.sourceSystemConfigData,
      },
      table_schedules: {},
    };
    dispatch(setIsLoading(true));
    apiService
      .postRequest(url, params)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          console.log('connection created');
          goBack();
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'Connection Created successfully',
              content: 'New Connection has been created successfully',
            }),
          );
        } else {
          alert('something went wrong');
        }
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  const editOnlineConnection = () => {
    const url =
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url;
    const params = {
      id,
      source_system_id: SourceSystemCRUD.selectedSourceSystem,
      connection_type: 'erp',
      connection_name: SourceSystemCRUD.sourceSystemConfigData.connection_name,
      connection_details: {
        schedule: SourceSystemCRUD.schedulingConfig,
        tables: SourceSystemCRUD.selectedTables,
        analytics: SourceSystemCRUD.selectedDomains,
        connection_config: SourceSystemCRUD.sourceSystemConfigData,
      },
      table_schedules: {},
    };
    dispatch(setIsLoading(true));
    apiService
      .putRequest(url, params)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 200) {
          goBack();
          dispatch(setAllSystemIntegrationTableDataFetched(false));

          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Connection successfully ${id ? 'updated' : 'created'}`,
              content: `New connection has been successfully ${
                id ? 'updated' : 'created'
              }.`,
            }),
          );
          console.log('connection updated');
        } else {
          dispatch(setIsLoading(false));
          alert('something went wrong');
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
      });
  };

  const getEditConnectionDetailsInit = (key: any) => {
    dispatch(setIsLoading(true));
    const url = `${
      ApiUtilities.getApiServerUrl +
      ApiUtilities.apiPath.getOnlineConnections.url
    }/${key}`;
    apiService
      .getRequest(url)
      .then((res) => {
        dispatch(setOnlineConnectionName(res.data.connection_name));
        dispatch(setAllSystemIntegrationTableDataFetched(false));
        dispatch(setIsTestingConnectionValid(false));
        dispatch(setIsLoading(false));
        dispatch(setSelectedSourceSystem(res.data.source_system_id));
        dispatch(
          setSourceSystemConfigData(
            res.data?.connection_details?.connection_config,
          ),
        );
        dispatch(setSchedulingConfig(res.data?.connection_details?.schedule));
        dispatch(setIsEditing(true));
        dispatch(setTableSchedules(res.data?.table_schedules));
        dispatch(setSelectedDomains(res.data?.connection_details?.analytics));
        dispatch(setSelectedTables(res.data?.connection_details?.tables));
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  useEffect(() => {
    if (mode === 'Edit') {
      getEditConnectionDetailsInit(id);
    }
  }, [id]);

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} Online Connection{' '}
          {mode !== 'Create' && connectionName && (
            <span>- {connectionName}</span>
          )}
        </span>
      </div>
      {mode !== 'View' ? (
        <div className="flex h-[82vh] w-full flex-col  rounded border-[1px] border-lightgray-100 bg-white-200 p-4 py-2">
          <OnlineERPSelector
            selectedModalTab={selectedTab}
            setSelectedModalTab={setSelectedTab}
            completedTabs={completedTabs}
          />
        </div>
      ) : (
        <div className="flex h-[82vh] w-full flex-col overflow-auto rounded border-[1px] border-lightgray-100 bg-white-200 p-4 py-2">
          <ViewConnectionDtails RowID={id} />
        </div>
      )}

      {mode !== 'View' && (
        <div className="flex h-[40px] w-full flex-row items-center justify-between">
          <Button
            className="flex h-[40px] items-center"
            type="button"
            onClick={goBack}
            intent="secondary"
          >
            Save as Draft
          </Button>

          <div className="flex flex-row space-x-4">
            {' '}
            <Button
              className="flex h-[40px] items-center"
              type="button"
              onClick={goBack}
              intent="secondary"
            >
              Cancel
            </Button>
            {selectedTab === 1 && (
              <Button
                className="flex h-[40px] items-center"
                type="button"
                onClick={() => {
                  dispatch(setIsTestingConnection(true));
                  dispatch(setIsTestingConnectionValid(true));
                  setTimeout(() => {
                    dispatch(setIsTestingConnection(false));
                  }, 200);
                }}
                intent="secondary"
              >
                Test
              </Button>
            )}
            {selectedTab > 0 && (
              <Button
                className="flex h-[40px] items-center"
                type="button"
                onClick={() => {
                  setSelectedTab(selectedTab - 1);
                }}
                disabled={selectedTab === 0}
                intent="secondary"
              >
                Previous
              </Button>
            )}
            <Button
              className="flex h-[40px] items-center"
              type="button"
              disabled={
                mode === 'Create'
                  ? getDisableOnlineConn()
                  : getDisableOnlineConnForEditMode()
              }
              onClick={() => {
                setSelectedTab(selectedTab + 1);
                setCompletedTabs([selectedTab, ...completedTabs]);
                if (selectedTab === 3) {
                  if (mode === 'Create') {
                    createOnlineConnection();
                  } else if (mode === 'Edit') {
                    editOnlineConnection();
                  }
                }
              }}
            >
              {selectedTab === 3
                ? mode !== 'Create'
                  ? 'Update'
                  : 'Create'
                : 'Next'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddNewOnlineConnection;
