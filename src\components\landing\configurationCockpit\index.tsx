/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable jsx-a11y/anchor-is-valid */

'use client';

import ConfigurationCockpitForm from './configurationForm';

const ConfigurationCockpit: React.FC = () => {
  return (
    <div className="flex h-[100vh] w-full flex-col bg-blue-200 p-10">
      <div className="mt-[65px] w-full">
        {' '}
        {/* <Image
          src="/assets/images/astrai-logo-white.svg"
          width={175}
          height={65}
          alt="astrai-logo"
          className="max-w-none"
        /> */}
      </div>
      <div className=" flex h-full w-full lg:flex-row flex-col ">
        <div className="flex h-full lg:w-[50%] w-full mb-6 lg:mb-0 flex-col justify-center space-y-2">
          <div>
            <span className="font-sans text-3xl font-semibold text-white-200">
              Configuration Cockpit
            </span>
          </div>
          <div className="w-[90%]">
            <span className="font-sans text-sm font-normal leading-5 text-white-200">
              Kindly provide your responses to these inquiries, as they will
              significantly enhance our comprehension of your ecosystem and
              enable us to fine-tune the application to better cater to your
              needs
            </span>
          </div>
        </div>
        <div className="configuration-form h-[calc(100vh-150px)]  lg:w-[50%] w-full rounded bg-white-200 p-4">
          <ConfigurationCockpitForm />
        </div>
      </div>
    </div>
  );
};

export default ConfigurationCockpit;
