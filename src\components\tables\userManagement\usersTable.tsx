import { useRouter } from 'next/navigation';
import eData from 'public/testdata/userManagement/userManagement.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsWarningAlertOpen, setWarningAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';
import { Searchbar } from '../../ui/Searchbar';

interface UsersTableProps {
  onFilteredDataCountChange: (count: string) => void;
}

const UsersTable: React.FC<UsersTableProps> = ({
  onFilteredDataCountChange,
}) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selector
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Constants
  const statusmapper: any = {
    CONFIRMED: 'success',
    PAUSE: 'error',
    FORCE_CHANGE_PASSWORD: 'warning',
  };
  const enableOptions: boolean = true;
  const header: any = [
    {
      title: 'UserName',
      optionsEnabled: false,
      options: ['sort'],
      inputType: 'checkbox',
    },
    {
      title: 'Mail ID',
      optionsEnabled: true,
      options: ['sort'],
    },
    { title: 'Last Updated', optionsEnabled: true, options: ['sort'] },
    { title: 'Status', optionsEnabled: false, options: ['sort'] },
  ];

  const [data, setData] = useState<any>([]);

  // States
  const [searchItem, setSearchItem] = useState('');
  const [filteredData, setFilteredData] = useState<any>(data);

  // Methods
  const handleSearch = (value: string) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };
  const deleteItem = () => {
    dispatch(setIsWarningAlertOpen(false));
  };
  const menuData: any = [
    {
      option: 'View User Details',
      clickFn: () => {
        const parameter: any = {
          mode: 'View',
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/userManagement/addNewUser?${queryString}`);
      },
    },
    {
      option: 'Edit User Details',
      clickFn: () => {
        const parameter: any = {
          mode: 'Edit',
        };
        const queryString = new URLSearchParams(parameter).toString();
        router.push(`/userManagement/addNewUser?${queryString}`);
      },
    },
    {
      option: 'Delete User',
      clickFn: () => {
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: 'Delete User 01',
            message:
              'Are you sure? Do you want to delete the User 01 Please note this action cannot be reverted ',
            actionbuttonText: 'Yes, Delete ',
          }),
        );
      },
    },
  ];

  const markFav = (rowId: any, isFav: boolean) => {
    console.log('row', rowId);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  const resolveComponent = (value: any, type: any, badges: any) => {
    const resolvedComponent: any = {
      value,
      disabled: false,
      userAvatarLink: null,
      type,
    };
    if (type === 'badges') {
      resolvedComponent.badges = [...badges];
    }
    return resolvedComponent;
  };

  const formatUserData = (result: any) => {
    return (
      result?.map((row: any) => {
        const email: any = row.Attributes[2].Value;
        const userId: any = row.Username;
        const {
          Username,
          isFavourite,
          Enabled,
          UserLastModifiedDate,
          UserStatus,
        } = row;

        const components = [
          resolveComponent(Username, 'checkbox', []),
          resolveComponent(email, 'text', []),
          resolveComponent(UserLastModifiedDate.split('T')[0], 'text', []),
          resolveComponent('', 'badges', [
            { intent: statusmapper[UserStatus], content: UserStatus },
          ]),
        ];

        return {
          id: userId,
          Username,
          selected: Enabled,
          isFavourite: isFavourite || false,
          components,
        };
      }) || []
    );
  };

  const getResolvedData = () => {
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getUsers.url;
    const apiData = apiService.getRequest(url);
    apiData.then((resp) => {
      setFilteredData(formatUserData(resp.data));
      setData(formatUserData(resp.data));
    });
  };

  useEffect(() => {
    if (!isTourOpen) {
      if (!filteredData) {
        getResolvedData();
      }
    }
  });

  useEffect(() => {
    if (!isTourOpen) {
      getResolvedData();
    } else {
      const userData: any = eData;
      setFilteredData(userData);
      setData(userData);
    }
  }, [isTourOpen]);

  // Effects
  useEffect(() => {
    onFilteredDataCountChange(filteredData.length);
  }, [filteredData, onFilteredDataCountChange]);

  return (
    <div className="flex h-full w-full flex-col py-4">
      <div className="w-[40vw]">
        {' '}
        <Searchbar
          value={searchItem}
          placeholder="Search Connection"
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div className="h-[70vh] w-full overflow-auto">
        <Table
          isCondensedTable
          isDashTable
          menuData={menuData}
          header={header}
          data={filteredData}
          enableOptions={enableOptions}
          canMarkFav
          onMarkFav={(rowId: any, isFav: boolean) => {
            markFav(rowId, isFav);
          }}
        />
      </div>

      <WarningDialog onConfirm={deleteItem} />
    </div>
  );
};

export default UsersTable;
