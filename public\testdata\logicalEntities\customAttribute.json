[{"id": "Attribute 01", "selected": true, "components": [{"value": "Attribute 01", "disabled": false, "columnKey": "attribute", "userAvatarLink": null, "type": "text"}, {"value": "INT", "type": "text", "columnKey": "attribute_datatype", "disabled": false, "userAvatarLink": null}, {"value": "Number of the payment", "columnKey": "description", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "select item_num from material where material.inventory_item_id = AP_INVOICE_LINEITEM.INVENT..", "columnKey": "formula", "type": "formula", "disabled": false, "userAvatarLink": null}]}]