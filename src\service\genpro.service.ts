'use client';

import { APIService } from './api.service';
import { ApiUtilities } from '@/utils/ApiUtilities';

export class GenProService {
  private apiService: APIService;

  constructor(apiService: APIService) {
    this.apiService = apiService;
  }

  // File Management Methods
  async uploadFile(file: File, fileType: string, originalFileName?: string) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('file_type', fileType);
    if (originalFileName) {
      formData.append('original_file_name', originalFileName);
    }

    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFileUpload);
    return this.apiService.genproFileUploadRequest(url, formData);
  }

  async getFileList(fileType?: string, validationStatus?: string) {
    const params: Record<string, string> = {};
    if (fileType) params.file_type = fileType;
    if (validationStatus) params.validation_status = validationStatus;

    const queryString = new URLSearchParams(params).toString();
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFileList) + 
                (queryString ? `?${queryString}` : '');
    
    return this.apiService.genproGetRequest(url);
  }

  async validateFile(fileId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFileValidate, { id: fileId });
    return this.apiService.genproPostRequest(url, {});
  }

  async getFileDetails(fileId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFileDetails, { id: fileId });
    return this.apiService.genproGetRequest(url);
  }

  async deleteFile(fileId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFileDelete, { id: fileId });
    return this.apiService.genproDeleteRequest(url);
  }

  async uploadWorkflowFiles(files: File[], automated: boolean = false) {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    formData.append('automation_enabled', automated.toString());

    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproUploadWorkflowFiles);
    return this.apiService.genproFileUploadRequest(url, formData);
  }

  // Workflow Management Methods
  async createWorkflow(workflowName: string, automationEnabled: boolean, fileUploadIds: number[]) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowCreate);
    const data = {
      workflow_name: workflowName,
      automation_enabled: automationEnabled,
      file_upload_ids: fileUploadIds,
    };
    return this.apiService.genproPostRequest(url, data);
  }

  async getWorkflowList() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowList);
    return this.apiService.genproGetRequest(url);
  }

  async startWorkflow(workflowId: number, automationEnabled: boolean) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowStart, { id: workflowId });
    const data = { automation_enabled: automationEnabled };
    return this.apiService.genproPostRequest(url, data);
  }

  async getWorkflowStatus(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowStatus, { id: workflowId });
    return this.apiService.genproGetRequest(url);
  }

  async addFilesToWorkflow(workflowId: number, fileUploadIds: number[]) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowAddFiles, { id: workflowId });
    const data = { file_upload_ids: fileUploadIds };
    return this.apiService.genproPostRequest(url, data);
  }

  async nextStage(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowNextStage, { id: workflowId });
    return this.apiService.genproPostRequest(url, {});
  }

  async workflowRunNextStage(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproWorkflowNextStage, { id: workflowRunId });
    return this.apiService.genproPostRequest(url, {});
  }

  // Business Logic Methods
  async calculateBf(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproBfCalculate);
    const data = { workflow_run_id: workflowRunId };
    return this.apiService.genproPostRequest(url, data);
  }

  async applyTransformation(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproTransformation);
    const data = { workflow_run_id: workflowRunId };
    return this.apiService.genproPostRequest(url, data);
  }

  async generatePivotTables(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproPivotGenerate);
    const data = { workflow_run_id: workflowRunId };
    return this.apiService.genproPostRequest(url, data);
  }

  async calculateDistribution(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproDistributionCalculate);
    const data = { workflow_run_id: workflowRunId };
    return this.apiService.genproPostRequest(url, data);
  }

  // Data Management Methods
  async getVesselEntityMappings() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntity);
    return this.apiService.genproGetRequest(url);
  }

  async getActiveVesselMappings() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntityActive);
    return this.apiService.genproGetRequest(url);
  }

  async bulkCreateVesselMappings(fileUploadId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntityBulkCreate);
    const data = { file_upload_id: fileUploadId };
    return this.apiService.genproPostRequest(url, data);
  }

  async getSmcList() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSmc);
    return this.apiService.genproGetRequest(url);
  }

  async getActiveSmcList() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSmcActive);
    return this.apiService.genproGetRequest(url);
  }

  async createSmc(data: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSmcCreate);
    return this.apiService.genproPostRequest(url, data);
  }

  async updateSmc(id: number, data: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSmcUpdate, { id });
    return this.apiService.genproPutRequest(url, data);
  }

  async deleteSmc(id: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSmcDelete, { id });
    return this.apiService.genproDeleteRequest(url);
  }

  async createVesselEntity(data: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntity);
    return this.apiService.genproPostRequest(url, data);
  }

  async updateVesselEntity(id: number, data: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntityUpdate, { id });
    return this.apiService.genproPutRequest(url, data);
  }

  async deleteVesselEntity(id: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproVesselEntityDelete, { id });
    return this.apiService.genproDeleteRequest(url);
  }

  // Export Methods
  async exportResults(workflowRunId: number, exportFormat: string, includePivotTables: boolean, includeDistribution: boolean) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproExport);
    const data = {
      workflow_run_id: workflowRunId,
      export_format: exportFormat,
      include_pivot_tables: includePivotTables,
      include_distribution: includeDistribution,
    };
    return this.apiService.genproPostRequest(url, data);
  }

  async getExportList() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproExportList);
    return this.apiService.genproGetRequest(url);
  }

  async sendExportEmail(exportId: number, recipients: string[]) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproExportEmail, { id: exportId });
    const data = { recipients };
    return this.apiService.genproPostRequest(url, data);
  }

  async getExportHistory(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproExportHistory) + 
                `?workflow_id=${workflowId}`;
    return this.apiService.genproGetRequest(url);
  }

  async getFinalMapping(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFinalMapping) + 
                `?workflow_id=${workflowId}`;
    return this.apiService.genproGetRequest(url);
  }

  async finalizeReport(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproFinalizeReport);
    const data = { workflow_id: workflowId };
    return this.apiService.genproPostRequest(url, data);
  }

  async unlockReport(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproUnlockReport);
    const data = { workflow_id: workflowId };
    return this.apiService.genproPostRequest(url, data);
  }

  async exportReport(workflowId: number, options: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproExportReport);
    const data = { workflow_id: workflowId, ...options };
    return this.apiService.genproPostRequest(url, data);
  }

  async sendReportEmail(workflowId: number, emailOptions: any) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSendReportEmail);
    const data = { workflow_id: workflowId, ...emailOptions };
    return this.apiService.genproPostRequest(url, data);
  }

  async previewReport(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproPreviewReport);
    const data = { workflow_id: workflowId };
    return this.apiService.genproPostRequest(url, data);
  }

  async completeWorkflow(workflowId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproCompleteWorkflow, { id: workflowId });
    return this.apiService.genproPostRequest(url, {});
  }

  // Analytics Methods
  async getAnalyticsSummary() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproAnalyticsSummary);
    return this.apiService.genproGetRequest(url);
  }

  async getAnalyticsTrends() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproAnalyticsTrends);
    return this.apiService.genproGetRequest(url);
  }

  async getDashboardAnalytics() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproDashboardAnalytics);
    return this.apiService.genproGetRequest(url);
  }

  async getRecentActivity() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproRecentActivity);
    return this.apiService.genproGetRequest(url);
  }

  async getSystemStatus() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSystemStatus);
    return this.apiService.genproGetRequest(url);
  }

  // System Configuration Methods
  async getSystemConfig() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproSystemConfig);
    return this.apiService.genproGetRequest(url);
  }

  // Audit Log Methods
  async getAuditLogs() {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproAuditLog);
    return this.apiService.genproGetRequest(url);
  }

  async getAuditLogsByWorkflow(workflowRunId: number) {
    const url = this.apiService.buildUrl(ApiUtilities.apiPath.genproAuditLogByWorkflow) + 
                `?workflow_run=${workflowRunId}`;
    return this.apiService.genproGetRequest(url);
  }

  // Helper method to determine file type from filename
  static getFileTypeFromName(filename: string): string {
    const name = filename.toLowerCase();
    
    if (name.includes('po') || name.includes('purchase_order')) {
      return 'PO';
    } else if (name.includes('bf') || name.includes('brokerage')) {
      return 'BF_OVERVIEW';
    } else if (name.includes('expense')) {
      return 'EXPENSE';
    } else if (name.includes('vessel') || name.includes('mapping')) {
      return 'VESSEL_MAPPING';
    } else if (name.includes('template')) {
      return 'IMPORT_TEMPLATE';
    }
    
    return 'PO'; // Default to PO if can't determine
  }
}