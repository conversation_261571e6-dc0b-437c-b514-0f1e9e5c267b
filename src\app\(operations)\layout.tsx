'use client';

import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import AppLoader from '@/components/appLoader';
import { Alert } from '@/components/ui/Alert';
import { Notification } from '@/components/ui/Notification';
import Sidebar from '@/components/ui/Sidebar';
import { currentEnv } from '@/constants/appConstants';
import ReduxProvider from '@/store/ReduxProvider';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface OperationsLayoutProps {
  children: ReactNode;
}

const Operations: React.FC<OperationsLayoutProps> = ({ children }) => {
  // Selectors with safe fallbacks
  const isExpandedInitialValue = useSelector(
    (state: RootState) => state?.sideBarExpand?.value || false,
  );

  // States
  const [isExpanded, setIsExpanded] = useState(isExpandedInitialValue);

  // Effects
  useEffect(() => {
    if (currentEnv === 'dev') {
      ApiUtilities.preloadImages();
    }
  }, []);

  useEffect(() => {
    setIsExpanded(isExpandedInitialValue);
  }, [isExpandedInitialValue]);

  return (
    <ReduxProvider>
      <div className="flex h-screen w-screen flex-row overflow-x-auto">
        <Sidebar isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
        <main
          className={`${
            isExpanded
              ? 'w-[70vw] lg:w-[calc(100vw-256px)]'
              : ' w-[93vw] lg:w-[calc(100vw-60px)]'
          } h-fit min-h-full bg-lightgray-100/40 p-10 pb-2 pt-6`}
        >
          {children}
        </main>

        <AppLoader />
      </div>
      <Alert />
      <Notification />
    </ReduxProvider>
  );
};

export default Operations;
