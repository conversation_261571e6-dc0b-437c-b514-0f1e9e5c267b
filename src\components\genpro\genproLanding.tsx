"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import {
  FileText,
  Settings,
  BarChart3,
  MapPin,
  Activity,
  Play,
  CheckCircle,
  TrendingUp,
} from "lucide-react";

import { Button } from "@/components/ui/Button";
import { GenProDummyService } from "@/service/genpro-dummy.service";
import { setToastAlert } from "@/slices/metaDataSlice";
import { setIsLoading } from "@/slices/appSlice";
import { useGenProWorkflow } from "@/contexts/GenProWorkflowContext";

interface DashboardStats {
  totalFiles: number;
  completedToday: number;
  pendingValidation: number;
  totalProcessed: number;
  successRate: number;
  avgProcessingTime: string;
  activeWorkflows: number;
}

interface ActivityItem {
  id: number;
  type: string;
  message: string;
  time: string;
  status: "success" | "warning" | "error";
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  action: () => void;
  primary?: boolean;
}

const GenProLandingPage: React.FC = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const genproService = useMemo(() => new GenProDummyService(), []);
  const { resetWorkflow } = useGenProWorkflow();

  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalFiles: 0,
    completedToday: 0,
    pendingValidation: 0,
    totalProcessed: 0,
    successRate: 0,
    avgProcessingTime: "Loading...",
    activeWorkflows: 0,
  });
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);

  useEffect(() => {
    // Reset workflow state when landing on GenPro hub
    resetWorkflow();
    loadDashboardData();
  }, []); // Empty dependency array - only run on mount

  const loadDashboardData = async () => {
    try {
      dispatch(setIsLoading(true));

      // Load dashboard analytics
      const analyticsResponse = await genproService.getDashboardAnalytics();
      const analytics = analyticsResponse.data;

      setDashboardStats({
        totalFiles: analytics.total_files || 0,
        completedToday: analytics.completed_today || 0,
        pendingValidation: analytics.pending_validation || 0,
        totalProcessed: analytics.total_processed || 0,
        successRate: analytics.success_rate || 0,
        avgProcessingTime: analytics.avg_processing_time || "N/A",
        activeWorkflows: analytics.active_workflows || 0,
      });

      // Load recent activity
      const activityResponse = await genproService.getRecentActivity();
      const activityData = activityResponse.data.results || [];
      setRecentActivity(activityData);
    } catch (error: any) {
      console.error("Failed to load dashboard data:", error);

      // Set fallback data if API fails
      setDashboardStats({
        totalFiles: 1247,
        completedToday: 12,
        pendingValidation: 2,
        totalProcessed: 1247,
        successRate: 98.5,
        avgProcessingTime: "4.2 min (Manual)",
        activeWorkflows: 3,
      });

      setRecentActivity([
        {
          id: 1,
          type: "workflow_completed",
          message: "January 2025 BF allocation completed",
          time: "2 hours ago",
          status: "success",
        },
        {
          id: 2,
          type: "validation_pending",
          message: 'Unknown entity code "NEW-001" requires mapping',
          time: "4 hours ago",
          status: "warning",
        },
        {
          id: 3,
          type: "export_generated",
          message: "Monthly report exported to Excel",
          time: "6 hours ago",
          status: "success",
        },
      ]);

      dispatch(
        setToastAlert({
          isToastOpen: true,
          intent: "warning",
          title: "Dashboard Data",
          content:
            "Using cached dashboard data. Some information may not be current.",
        })
      );
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  const startNewWorkflow = () => {
    // Ensure workflow state is reset before navigating
    resetWorkflow();
    router.push("/genpro/workflow");
  };

  const quickActions: QuickAction[] = [
    {
      title: "Start New Workflow",
      description: "Begin monthly BF allocation process",
      icon: Play,
      action: startNewWorkflow,
      primary: true,
    },

    {
      title: "Manage Mappings",
      description: "Update vessel and smc mappings",
      icon: MapPin,
      action: () => router.push("/genpro/mapping"),
    },
    {
      title: "Configuration",
      description: "System settings and rules",
      icon: Settings,
      action: () => router.push("/genpro/configuration"),
    },
    {
      title: "View Analytics",
      description: "Check performance metrics",
      icon: BarChart3,
      action: () => router.push("/genpro/analytics"),
    },
  ];

  return (
    <div>
      {/* Page Header */}
      {/* <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">GenPro Hub</h1>
        <p className="text-gray-600">
          Brokerage Fee Allocation Workflow System
        </p>
      </div> */}

      {/* Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Total Files Processed</p>
              <p className="text-2xl font-bold text-gray-800">
                {dashboardStats.totalFiles}
              </p>
            </div>
            <FileText className="w-6 h-6 text-gray-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Completed Today</p>
              <p className="text-2xl font-bold text-gray-800">
                {dashboardStats.completedToday}
              </p>
            </div>
            <CheckCircle className="w-6 h-6 text-gray-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Success Rate</p>
              <p className="text-2xl font-bold text-gray-800">
                {dashboardStats.successRate}%
              </p>
            </div>
            <TrendingUp className="w-6 h-6 text-gray-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-gray-600">Avg Processing</p>
              <p className="text-2xl font-bold text-gray-800">
                {dashboardStats.avgProcessingTime}
              </p>
            </div>
            <Activity className="w-6 h-6 text-gray-500" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">
            Quick Actions
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
            {quickActions.map((action, index) => {
              const IconComponent = action.icon;
              return (
                <div
                  key={index}
                  className={`bg-white-200 rounded-lg p-4 border border-lightgray-100 cursor-pointer hover:shadow-md hover:border-blue-200 transition-all duration-200 ${
                    action.primary ? "ring-2 ring-blue-200" : ""
                  }`}
                  onClick={action.action}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="w-5 h-5 text-gray-600" />
                    <div>
                      <h3 className="font-medium text-gray-800">
                        {action.title}
                      </h3>
                      <p className="text-xs text-gray-600">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Information Note */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <h3 className="text-sm font-semibold text-gray-800 mb-2">
              💡 Getting Started
            </h3>
            <div className="text-xs text-gray-700 space-y-1">
              <p>
                • <strong>New to GenPro?</strong> Start with "Start New
                Workflow" to begin your first allocation process
              </p>
              <p>
                • <strong>Need to update mappings?</strong> Use "Manage
                Mappings" to configure vessel entity codes
              </p>
              <p>
                • <strong>System settings?</strong> Access "Configuration" to
                customize workflow rules and preferences
              </p>
              {/* <p>• <strong>Track progress?</strong> Check "View Analytics" for performance metrics and insights</p> */}
            </div>
          </div>
        </div>

        {/* Recent Activity Sidebar */}
        <div>
          <h2 className="text-lg font-semibold text-gray-800 mb-3">
            Recent Activity
          </h2>
          <div className="bg-white-200 rounded-lg border border-lightgray-100 p-4">
            <div className="space-y-3">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-2">
                  <div
                    className={`w-2 h-2 rounded-full mt-1 ${
                      activity.status === "success"
                        ? "bg-green-500"
                        : activity.status === "warning"
                          ? "bg-yellow-500"
                          : "bg-red-500"
                    }`}
                  ></div>
                  <div className="flex-1">
                    <p className="text-xs text-gray-800">{activity.message}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-3 border-t border-gray-100">
              <Button
                intent="secondary"
                className="w-full text-xs py-2"
                onClick={() => router.push("/genpro/logs")}
              >
                View All Logs
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenProLandingPage;
