/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

import 'reactflow/dist/style.css';

import { useRouter } from 'next/navigation';
import React from 'react';
import ReactFlow, { Background, Controls, MiniMap } from 'reactflow';

import CustomNode from '@/components/nodes/customNode/customNode';
import GroupNode from '@/components/nodes/groupNode/groupNode';
import NoHanldeNode from '@/components/nodes/noHandleNode/noHandleNode';
import RightBottomHandleNode from '@/components/nodes/rightBottomHandleNode/rightBottomHandleNode';

const nodeTypes = {
  customNode: CustomNode,
  groupNode: GroupNode,
  nohandleNode: NoHanldeNode,
  rightBottomHandleNode: RightBottomHandleNode,
};
const ConceptualFlow: React.FC = () => {
  const router = useRouter();
  const initialNodes: any = [
    {
      id: '1',
      type: 'rightBottomHandleNode',
      data: { label: 'Consolidated Budget(BGC)\nConsolidated Forecast (BGC)' },
      position: { x: 0, y: 0 },
    },
    {
      id: '2',
      type: 'customNode',
      data: { label: 'Consolidated Budget(AGC)\nConsolidated Actuals (AGC)' },
      position: { x: 300, y: 0 },
    },
    {
      id: 'feesGroup',
      type: 'group',
      position: {
        x: 900,
        y: 0,
      },
      style: {
        width: 180,
        height: 180,
        border: 'none',
        backgroundColor: 'rgba(208, 192, 247, 0.2)',
      },
    },
    {
      id: 'feesGroup-nd1',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 20,
      },
      data: { label: 'Management Fees' },
      parentId: 'feesGroup',
    },
    {
      id: 'feesGroup-nd2',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 70,
      },
      data: { label: 'Termination Fees' },
      parentId: 'feesGroup',
    },
    {
      id: 'feesGroup-nd3',
      type: 'output',
      targetPosition: 'left',
      position: {
        x: 15,
        y: 120,
      },
      data: { label: 'Crew Lumpsum fees' },
      parentId: 'feesGroup',
    },

    {
      id: 'transactionalGroup',
      type: 'groupNode',
      data: {
        label: 'Transactional Data',
      },
      position: {
        x: 200,
        y: 500,
      },
      style: {
        width: 300,
        height: 250,
        backgroundColor: 'rgba(208, 192, 247, 0.2)',
      },
    },
    {
      id: 'transGroup-nd1',
      position: {
        x: 50,
        y: 40,
      },
      style: {
        height: 40,
        width: 200,
      },
      type: 'output',
      targetPosition: 'top',
      data: { label: 'Consolidated Budget (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd2',
      type: 'output',
      targetPosition: 'left',
      style: {
        height: 40,
        width: 200,
      },
      position: {
        x: 50,
        y: 80,
      },
      data: { label: 'Consolidated Forecast-1 (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd3',
      position: {
        x: 50,
        y: 120,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecast-2 (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'transGroup-nd4',
      position: {
        x: 50,
        y: 160,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecast-3 (BGC)' },
      parentId: 'transactionalGroup',
    },
    {
      id: 'chartAccount-nd',
      sourcePosition: 'bottom',
      targetPosition: 'top',
      position: {
        x: 300,
        y: 325,
      },
      data: { label: 'Chart of Accounts' },
    },
    {
      id: 'orgHeirarchy-nd',
      sourcePosition: 'bottom',
      targetPosition: 'top',
      position: {
        x: 100,
        y: 325,
      },
      data: { label: 'Org Heirarchy' },
    },
    {
      id: 'calendar',
      type: 'input',
      sourcePosition: 'right',
      position: {
        x: 0,
        y: 450,
      },
      data: { label: 'Calendar' },
    },

    {
      id: 'transactionalGroup2',
      type: 'groupNode',
      data: {
        label: 'Transactional Data',
      },
      position: {
        x: 900,
        y: 500,
      },
      style: {
        width: 300,
        height: 250,
        backgroundColor: 'rgba(208, 192, 247, 0.2)',
      },
    },
    {
      id: 'transGroup2-nd1',
      position: {
        x: 50,
        y: 40,
      },
      style: {
        height: 40,
        width: 200,
      },
      type: 'output',
      targetPosition: 'top',
      data: { label: 'Consolidated Budget (AGC)' },
      parentId: 'transactionalGroup2',
    },
    {
      id: 'transGroup2-nd2',
      type: 'output',
      targetPosition: 'left',
      style: {
        height: 40,
        width: 200,
      },
      position: {
        x: 50,
        y: 80,
      },
      data: { label: 'Consolidated Forecast-1 (AGC)' },
      parentId: 'transactionalGroup2',
    },
    {
      id: 'transGroup2-nd3',
      position: {
        x: 50,
        y: 120,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecast-2 (AGC)' },
      parentId: 'transactionalGroup2',
    },
    {
      id: 'transGroup2-nd4',
      position: {
        x: 50,
        y: 160,
      },
      type: 'nohandleNode',
      data: { label: 'Consolidated Forecast-3 (AGC)' },
      parentId: 'transactionalGroup2',
    },
    {
      id: 'chartAccount-nd2',
      sourcePosition: 'bottom',
      targetPosition: 'top',
      position: {
        x: 1000,
        y: 325,
      },
      data: { label: 'Chart of Accounts' },
    },
    {
      id: 'orgHeirarchy-nd2',
      sourcePosition: 'bottom',
      targetPositon: 'top',
      position: {
        x: 800,
        y: 325,
      },
      data: { label: 'Org Heirarchy' },
    },
    {
      id: 'calendar-nd2',
      type: 'input',
      sourcePosition: 'right',
      position: {
        x: 550,
        y: 450,
      },
      data: { label: 'Calendar' },
    },
  ];

  const initialEdges = [
    { id: 'e1', source: '1', sourceHandle: 'right', target: '2' },
    {
      id: 'e13',
      source: '1',
      sourceHandle: 'bottom',
      target: 'chartAccount-nd',
    },
    {
      id: 'e14',
      source: '1',
      sourceHandle: 'bottom',
      target: 'orgHeirarchy-nd',
    },
    { id: 'e3', source: '2', sourceHandle: 'right', target: 'feesGroup-nd1' },
    { id: 'e4', source: '2', sourceHandle: 'right', target: 'feesGroup-nd2' },
    { id: 'e5', source: '2', sourceHandle: 'right', target: 'feesGroup-nd3' },
    {
      id: 'e13',
      source: '2',
      sourceHandle: 'bottom',
      target: 'chartAccount-nd2',
    },
    {
      id: 'e14',
      source: '2',
      sourceHandle: 'bottom',
      target: 'orgHeirarchy-nd2',
    },
    { id: 'e7', source: 'chartAccount-nd', target: 'transGroup-nd1' },
    { id: 'e8', source: 'orgHeirarchy-nd', target: 'transGroup-nd1' },
    { id: 'e9', source: 'calendar', target: 'transGroup-nd2' },
    { id: 'e10', source: 'chartAccount-nd2', target: 'transGroup2-nd1' },
    { id: 'e11', source: 'orgHeirarchy-nd2', target: 'transGroup2-nd1' },
    { id: 'e12', source: 'calendar-nd2', target: 'transGroup2-nd2' },
  ];

  const proOptions = { hideAttribution: true };

  const goBack = () => {
    router.push('/home');
  };

  return (
    <div className="h-[calc(100vh-70px)]  w-[90vw]">
      <div className="flex w-fit flex-row items-center space-x-2">
        <img
          className="size-[24px] cursor-pointer"
          src="/assets/images/arrow-left.svg"
          onClick={goBack}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          Data Model - Budget & Forecast (Annual)
        </span>
      </div>
      <div className="h-[calc(100vh-110px)] ">
        <ReactFlow
          nodes={initialNodes}
          edges={initialEdges}
          nodeTypes={nodeTypes}
          proOptions={proOptions}
          nodesDraggable={false}
          deleteKeyCode={null}
          nodesConnectable={false}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background />
        </ReactFlow>
      </div>
    </div>
  );
};

export default ConceptualFlow;
