import { Formik } from 'formik';

import { Input } from '../../../ui/Input';
import { InputFile } from '../../../ui/InputFile';
import { Paragraph } from '../../../ui/Paragraph';

export const OfflineERPSelector = ({
  selectedOption,
  setSelectedOption,
  setOffConnectionName,
  OffConnectionName,
  selectedFile,
  setSelectedFile,
}: any) => {
  const onImageSelection = (id: string) => {
    if (selectedOption === id) {
      setSelectedOption(null);
    } else {
      setSelectedOption(id);
    }
  };
  return (
    <div className="h-full w-full p-4">
      <Formik
        initialValues={{ CName: '', file: '' }}
        // validate={(values) => {
        //   const errors: any = {};
        //   if (!values.CName) {
        //     errors.CName = 'Required.';
        //   }
        //   if (!values.file) {
        //     errors.file = 'Required.';
        //   }
        //   return errors;
        // }}
        onSubmit={(values, { setSubmitting }) => {
          setTimeout(() => {
            console.log(values);
            // can show loader if wanted
            setSubmitting(false);
            // router.push('/dashboard');
          }, 400);
        }}
      >
        {({ values, errors, touched, handleBlur, handleSubmit }) => (
          <form className="mb-4 w-full" onSubmit={handleSubmit}>
            <Input
              label="Connection Name"
              name="CName"
              type="text"
              className="w-[216px]"
              placeholder="Enter Connection Name"
              onChange={(e: any) => setOffConnectionName(e.target.value)}
              onBlur={handleBlur}
              value={OffConnectionName}
              intent={errors.CName && touched.CName ? 'hasError' : 'enabled'}
              error={errors.CName && touched.CName && errors.CName}
            />

            <Paragraph
              content="Select source from below to establish an offline connection"
              intent="p300"
              className="pt-6"
            />
            <div className="mb-8 grid grid-cols-4 gap-4 pt-4">
              {[
                {
                  id: '1',
                  type: 'local',
                  title: 'Local System',
                  inactive_logo: 'local.png',
                },
                {
                  id: '2',
                  type: 'ftp',
                  title: 'FTP',
                  inactive_logo: 'ftp.png',
                },
                {
                  id: '3',
                  type: 'teams',
                  title: 'Teams Folder',
                  inactive_logo: 'teams.png',
                },
              ].map((data) => (
                <button
                  type="button"
                  key={data.id}
                  className={`flex flex-col items-center space-y-2 px-7 py-8 disabled:cursor-not-allowed ${
                    selectedOption === data.id
                      ? 'border-[2px] border-blue-200'
                      : 'border'
                  } `}
                  disabled={data.id !== '1'}
                  onClick={() => {
                    onImageSelection(data.id);
                  }}
                >
                  <div className="h-[64px] w-[64px]">
                    <img
                      src={`/assets/images/${data.inactive_logo}`}
                      width={64}
                      height={64}
                      alt={data.title}
                    />
                  </div>
                  <Paragraph
                    content={data.title}
                    intent="p200"
                    className="py-2"
                  />
                </button>
              ))}
            </div>
            <div className=" flex w-[27%] flex-col space-y-1">
              <InputFile
                label="Select the file to upload"
                name="file"
                type="file"
                className=""
                placeholder="Select File"
                onChange={(e: any) => {
                  setSelectedFile(e.target.files[0]);
                }}
                accept=".csv"
                onBlur={handleBlur}
                value={values.file}
                ValueName={selectedFile?.name}
                intent={errors.file && touched.file ? 'hasError' : 'enabled'}
                error={errors.file && touched.file && errors.file}
              />
              <span className="font-sans text-[10px] text-gray-400">
                *only csv format supported
              </span>
            </div>
          </form>
        )}
      </Formik>
    </div>
  );
};
