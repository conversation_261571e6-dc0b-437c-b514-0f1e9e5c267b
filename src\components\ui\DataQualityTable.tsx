/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';

import { LocalService } from '@/service/local.service';
import { getIcon } from '@/utils/utilityHelper';

const DataQualityTable: React.FC<{
  selectedYear?: any;
  onRowClick: any;
  headers: any[];
  rows: any[];
}> = ({ selectedYear, headers, rows, onRowClick }) => {
  const localService = new LocalService();
  const [selectedRowIndex, setSelectedRowIndex] = useState<number>(
    localService.getItem('selectedRecon') || 0,
  );
  const handleRowClick = (row: any, index: any) => {
    console.log('index', index);
    setSelectedRowIndex(Number(index));
    onRowClick(row, index);
  };

  useEffect(() => {
    setSelectedRowIndex(Number(0));
  }, [selectedYear]);

  useEffect(() => {
    setSelectedRowIndex(selectedRowIndex);
    onRowClick(rows[selectedRowIndex], selectedRowIndex);
  }, [selectedRowIndex, rows]);

  return (
    <div className=" bg-white-200">
      <table className="w-full table-auto">
        <thead>
          <tr>
            {headers.map((header: any, index: any) => (
              <th
                key={index}
                className={`border-[1px] border-lightgray-100 p-3 text-left   `}
              >
                {typeof header === 'object' ? (
                  <div className="flex flex-row space-x-3">
                    <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                      {header.data}
                    </span>
                  </div>
                ) : (
                  <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                    {header}
                  </span>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row: any, rowIndex: number) => (
            <tr
              key={rowIndex}
              onClick={() => handleRowClick(row, rowIndex)}
              className={`cursor-pointer hover:bg-blue-200/10 ${
                Number(selectedRowIndex) === rowIndex ? 'bg-blue-200/10' : ''
              }`}
            >
              {headers.map((header, colIndex) => (
                <td
                  key={colIndex}
                  className="border-[1px] border-lightgray-100 px-3 py-2 text-left font-sans text-sm font-semibold  capitalize text-gray-500"
                >
                  {typeof row[header.data] === 'object' ? (
                    <div className="flex flex-row justify-between">
                      {row[header.data].data === 'error' ||
                      row[header.data].data === 'success' ||
                      row[header.data].data === 'warning' ? (
                        <img
                          className="h-[18px] w-[18px]"
                          src={getIcon(row[header.data].data)}
                          alt="error"
                        />
                      ) : (
                        <span> {row[header.data].data}</span>
                      )}
                    </div>
                  ) : (
                    row[header]
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DataQualityTable;
