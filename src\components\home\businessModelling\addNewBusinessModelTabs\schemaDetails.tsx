/* eslint-disable react/no-array-index-key */
import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import SchemaDetailTable from '@/components/tables/businessModelling/SchemaDetailsTable';
import { Input } from '@/components/ui/Input';
import { TextField } from '@/components/ui/TextField';
import { APIService } from '@/service/api.service';
import {
  setBusinessModelDescription,
  setBusinessModelName,
  setEntityAttributes,
} from '@/slices/businessModelCrudSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const SchemaDetails: React.FC<{
  mode: string | string[] | undefined;
}> = (props) => {
  // Essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Selectors
  const businessModelName = useSelector(
    (state: any) => state.businessModelCrud.businessModelName,
  );
  const businessModelDescription = useSelector(
    (state: any) => state.businessModelCrud.businessModelDescription,
  );

  const entityAttributes = useSelector(
    (state: any) => state.businessModelCrud.entityAttributes,
  );

  // Handlers
  const handleNameChange = (e: any) => {
    dispatch(setBusinessModelName(e.target.value));
  };
  const handleDescriptionChange = (e: any) => {
    dispatch(setBusinessModelDescription(e.target.value));
  };

  // Effects
  useEffect(() => {
    function getResolvedData() {
      if (entityAttributes.length === 0) {
        const url =
          ApiUtilities.getApiServerUrl +
          ApiUtilities.apiPath.getExistingAttributes.url;
        const apiData = apiService.getRequest(url);
        apiData.then((resp) => {
          dispatch(setEntityAttributes(resp.data));
        });
      }
    }
    getResolvedData();
  }, []);

  return (
    <div className="flex h-[72vh] w-full flex-col space-y-4 px-6 pt-2">
      <div className="w-full">
        {props.mode !== 'View' ? (
          <Formik
            initialValues={
              props.mode === 'Edit'
                ? {
                    BName: 'VW_ACCOUNTS_PAYABLE',
                    description: 'VW_ACCOUNTS_PAYABLE',
                  }
                : { BName: '', description: '' }
            }
            onSubmit={(values, { setSubmitting }) => {
              setTimeout(() => {
                console.log(values);
                // can show loader if wanted
                setSubmitting(false);
                // router.push('/dashboard');
              }, 400);
            }}
          >
            {({
              values,
              errors,
              touched,
              handleChange,
              handleBlur,
              handleSubmit,
            }) => (
              <form
                className="flex h-full w-full flex-col space-y-6 "
                onSubmit={handleSubmit}
              >
                <div className="flex flex-row space-x-6">
                  <div className="w-[30%]">
                    {' '}
                    <Input
                      label="Schema Name"
                      name="BName"
                      type="text"
                      className=""
                      placeholder="Enter Schema Name"
                      onChange={(e) => {
                        console.log(values);
                        handleChange(e);
                        handleNameChange(e);
                      }}
                      onBlur={handleBlur}
                      value={businessModelName}
                      intent={
                        errors.BName && touched.BName ? 'hasError' : 'enabled'
                      }
                      error={errors.BName && touched.BName && errors.BName}
                    />
                  </div>
                  <div className="h-full w-[33vw]">
                    <TextField
                      label="Description"
                      name="description"
                      className="h-[78px]"
                      placeholder="Enter Description"
                      onChange={handleDescriptionChange}
                      onBlur={handleBlur}
                      value={businessModelDescription}
                      intent={
                        errors.description && touched.description
                          ? 'hasError'
                          : 'enabled'
                      }
                    />
                  </div>
                </div>
              </form>
            )}
          </Formik>
        ) : (
          <div className="flex flex-row space-x-11 ">
            <div className="flex flex-col space-y-2">
              <span className="font-sans text-xs font-medium text-gray-400">
                Schema Name
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {businessModelName}
              </span>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="font-sans text-xs font-medium text-gray-400">
                Description
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {businessModelDescription}
              </span>
            </div>
          </div>
        )}
      </div>
      <div className="h-[85%] w-full rounded bg-gray-300 px-4 pt-2">
        <SchemaDetailTable mode={props.mode} />
      </div>
    </div>
  );
};

export default SchemaDetails;
