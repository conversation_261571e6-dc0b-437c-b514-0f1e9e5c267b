'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/businessModelling/viewDataLog.json';
import React, { useState } from 'react';

import ViewLogTable from '@/components/tables/businessModelling/dataLogTable';

const ViewDataLog: React.FC = () => {
  // essentials
  const router = useRouter();

  // states
  const [details] = useState(eData);

  const goBack = () => {
    router.push('/businessModelling');
  };

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-full flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Data Log - Business Schema 01
        </span>
      </div>
      <div className="flex h-fit min-h-[88vh] w-full flex-col space-y-9 rounded border-[1px] border-lightgray-100 bg-white-200 p-6">
        <div className="flex w-full flex-row space-x-16">
          {' '}
          {Object.entries(details).map(([key, value]) => (
            <div className="flex flex-col space-y-2" key={key}>
              <span className="font-sans text-xs font-medium text-gray-400">
                {key}
              </span>{' '}
              <span className="font-sans text-sm font-semibold text-gray-500">
                {value}
              </span>
            </div>
          ))}
        </div>
        <div className="h-full w-full rounded bg-gray-300 px-4 py-6">
          <ViewLogTable />
        </div>
      </div>
    </div>
  );
};

export default ViewDataLog;
