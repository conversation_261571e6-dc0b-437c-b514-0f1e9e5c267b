/* eslint-disable import/no-extraneous-dependencies */

'use client';

import LoadingOverlay from 'react-loading-overlay-ts';

export default function Loading() {
  // You can add any UI inside Loading, including a Skeleton.
  return (
    <LoadingOverlay
      active
      styles={{
        overlay: (base) => ({
          ...base,
          background: 'rgba(0, 0, 0, 0.2)',
        }),
        spinner: (base) => ({
          ...base,
          width: '100px',
          '& svg circle': {
            stroke: 'rgba(0, 0, 0, 0.5)',
          },
        }),
        wrapper: {
          width: '100vw',
          height: '100vh',
          overflow: 'hidden',
        },
      }}
      spinner
    />
  );
}
