'use client';

import React, { useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import {
  <PERSON><PERSON><PERSON>,
  BarChart3,
  DollarSign,
  Calculator,
  TrendingUp,
  Download,
  RefreshCw,
  CheckCircle
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import { useGenProWorkflow } from '@/contexts/GenProWorkflowContext';
import { GenProDummyService } from '@/service/genpro-dummy.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { setIsLoading } from '@/slices/appSlice';

interface DistributionProps {
  onNext?: () => void;
  onPrevious?: () => void;
}

const Distribution: React.FC<DistributionProps> = ({ onNext, onPrevious }) => {
  const dispatch = useDispatch();
  const genproService = useMemo(() => new GenProDummyService(), []);
  
  const { currentWorkflow } = useGenProWorkflow();
  
  const [selectedView, setSelectedView] = useState('summary');
  const [distributionResult, setDistributionResult] = useState<any>(null);
  const [pivotResults, setPivotResults] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  const generatePivotTables = async () => {
    if (!currentWorkflow?.id) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'No Workflow',
        content: 'No active workflow found. Please start from the Ingestion step.',
      }));
      return;
    }

    setIsCalculating(true);
    dispatch(setIsLoading(true));

    try {
      const response = await genproService.generatePivotTables(currentWorkflow.id);
      setPivotResults(response.data);
      
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'success',
        title: 'Pivot Tables Generated',
        content: `Generated pivot tables with totals: $${response.data.pivot1_total_amount?.toLocaleString()} and $${response.data.pivot2_total_amount?.toLocaleString()}`,
      }));
    } catch (error: any) {
      console.error('Pivot generation failed:', error);
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Pivot Generation Failed',
        content: error.response?.data?.error || 'Failed to generate pivot tables',
      }));
    } finally {
      setIsCalculating(false);
      dispatch(setIsLoading(false));
    }
  };

  const calculateDistribution = async () => {
    if (!currentWorkflow?.id) {
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'No Workflow',
        content: 'No active workflow found. Please start from the Ingestion step.',
      }));
      return;
    }

    setIsCalculating(true);
    dispatch(setIsLoading(true));

    try {
      const response = await genproService.calculateDistribution(currentWorkflow.id);
      setDistributionResult(response.data);
      
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'success',
        title: 'Distribution Calculated',
        content: `Distributed $${response.data.total_distributed?.toLocaleString()} across ${response.data.distribution_records} records`,
      }));
    } catch (error: any) {
      console.error('Distribution calculation failed:', error);
      dispatch(setToastAlert({
        isToastOpen: true,
        intent: 'error',
        title: 'Distribution Failed',
        content: error.response?.data?.error || 'Failed to calculate distribution',
      }));
    } finally {
      setIsCalculating(false);
      dispatch(setIsLoading(false));
    }
  };

  // Mock data for distribution calculations
  const distributionData = [
    {
      finalSMC: 'BSM HEL',
      finalVessel: 'Vessel X',
      totalBF: 33333,
      totalBFIncluding50: 35000,
      bfPercentage: 26.7,
      distributionAmount: 33375,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM CYP',
      finalVessel: 'Vessel Y',
      totalBF: 50000,
      totalBFIncluding50: 52500,
      bfPercentage: 40.0,
      distributionAmount: 50000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'SEACHEF',
      finalVessel: 'SEACHEF',
      totalBF: 16667,
      totalBFIncluding50: 18750,
      bfPercentage: 13.3,
      distributionAmount: 16625,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM SG',
      finalVessel: 'Vessel C',
      totalBF: 15000,
      totalBFIncluding50: 15750,
      bfPercentage: 12.0,
      distributionAmount: 15000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'Frontline',
      finalVessel: 'Frontline',
      totalBF: 10000,
      totalBFIncluding50: 10000,
      bfPercentage: 0,
      distributionAmount: 0,
      excludeFromDistribution: true
    }
  ];

  const seachefSplitData = [
    {
      smc: 'BSM CHI',
      vessel: 'SEACHEF A',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SMC'
    },
    {
      smc: 'SEACHEF',
      vessel: 'SEACHEF',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SEACHEF'
    }
  ];

  const totalBFToDistribute = 125000;
  const totalDistributed = distributionData.reduce((sum, item) => sum + item.distributionAmount, 0);
  const distributionSummary = {
    totalBF: totalBFToDistribute,
    totalDistributed: totalDistributed,
    remaining: totalBFToDistribute - totalDistributed,
    entities: distributionData.length,
    excludedEntities: distributionData.filter(d => d.excludeFromDistribution).length
  };

  const summaryHeader = [
    { title: 'Final SMC', align: 'left' },
    { title: 'Final Vessel', align: 'left' },
    { title: 'Total BF', align: 'right' },
    { title: 'Total BF (Including 50%)', align: 'right' },
    { title: 'BF %', align: 'right' },
    { title: 'Distribution Amount', align: 'right' },
  ];

  const summaryData = distributionData.map((item, index) => ({
    id: index,
    components: [
      {
        type: 'text',
        value: (
          <div className="flex items-center space-x-2">
            <PieChart className="w-4 h-4 text-gray-500" />
            <span className="font-medium">{item.finalSMC}</span>
          </div>
        )
      },
      { type: 'text', value: item.finalVessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBF.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBFIncluding50.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-blue-600'
          }`}>
            {item.bfPercentage.toFixed(1)}%
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-mono font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-green-600'
          }`}>
            ${item.distributionAmount.toLocaleString()}
            {item.excludeFromDistribution && ' (Excluded)'}
          </span>
        )
      },
    ],
  }));

  const seachefHeader = [
    { title: 'SMC', align: 'left' },
    { title: 'Vessel', align: 'left' },
    { title: 'Original Amount', align: 'right' },
    { title: '50% Split Amount', align: 'right' },
    { title: 'Allocation Type', align: 'left' },
  ];

  const seachefData = seachefSplitData.map((item, index) => ({
    id: index,
    components: [
      { type: 'text', value: item.smc },
      { type: 'text', value: item.vessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.originalAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono font-semibold text-right text-orange-600">
            ${item.splitAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'badges',
        badges: [{
          intent: item.allocation === 'SEACHEF' ? 'warning' : 'info',
          content: item.allocation
        }]
      },
    ],
  }));

  const handleCalculateDistribution = () => {
    alert('Recalculating distribution with current parameters...');
  };

  const handleExportResults = () => {
    alert('Exporting distribution results...');
  };

  return (
    <div className="flex flex-col h-full p-6">
      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto pr-2">

      {/* No Workflow State */}
      {!currentWorkflow?.id && (
        <div className="text-center py-12">
          <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Workflow</h3>
          <p className="text-gray-500">Please start from the Ingestion step to create a workflow</p>
        </div>
      )}

      {/* Distribution Controls */}
      {currentWorkflow?.id && (
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-800 flex items-center">
                <PieChart className="w-5 h-5 mr-2" />
                Distribution & Pivot Tables
              </h3>
              <p className="text-sm text-gray-600">
                Generate pivot tables and calculate final BF distribution
              </p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={generatePivotTables}
                intent="secondary"
                className="flex items-center"
                disabled={isCalculating}
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Generate Pivots
              </Button>
              <Button
                onClick={calculateDistribution}
                intent="primary"
                className="flex items-center"
                disabled={isCalculating}
              >
                {isCalculating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Calculator className="w-4 h-4 mr-2" />
                )}
                Calculate Distribution
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Pivot Results */}
      {pivotResults && (
        <div className="bg-green-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-green-800 flex items-center mb-3">
            <CheckCircle className="w-5 h-5 mr-2" />
            Pivot Tables Generated
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Pivot Table 1 Total</p>
              <p className="text-xl font-bold text-green-700">
                ${pivotResults.pivot1_total_amount?.toLocaleString() || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Pivot Table 2 Total</p>
              <p className="text-xl font-bold text-green-700">
                ${pivotResults.pivot2_total_amount?.toLocaleString() || 'N/A'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Distribution Results */}
      {distributionResult && (
        <div className="bg-green-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-green-800 flex items-center mb-3">
            <CheckCircle className="w-5 h-5 mr-2" />
            Distribution Complete
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Distribution Records</p>
              <p className="text-xl font-bold text-green-700">
                {distributionResult.distribution_records?.toLocaleString() || 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Distributed</p>
              <p className="text-xl font-bold text-green-700">
                ${distributionResult.total_distributed?.toLocaleString() || 'N/A'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Distribution Summary */}
      {currentWorkflow?.id && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total BF Pool</p>
              <p className="text-2xl font-bold text-blue-600">
                ${distributionSummary.totalBF.toLocaleString()}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Distributed</p>
              <p className="text-2xl font-bold text-green-600">
                ${distributionSummary.totalDistributed.toLocaleString()}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Remaining</p>
              <p className="text-2xl font-bold text-orange-600">
                ${distributionSummary.remaining.toLocaleString()}
              </p>
            </div>
            <Calculator className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Entities</p>
              <p className="text-2xl font-bold text-gray-800">{distributionSummary.entities}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-gray-500" />
          </div>
        </div>
        <div className="bg-white-200 rounded-lg p-4 border border-lightgray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Excluded</p>
              <p className="text-2xl font-bold text-red-600">{distributionSummary.excludedEntities}</p>
            </div>
            <PieChart className="w-8 h-8 text-red-500" />
          </div>
        </div>
        </div>
      )}

      {/* BF Calculation Details */}
      {currentWorkflow?.id && (
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <Calculator className="w-5 h-5 mr-2" />
            Brokerage Fee Calculation Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Estimated GenPro Expense (MTD):</p>
              <p className="font-semibold text-gray-800">$12,500</p>
            </div>
            <div>
              <p className="text-gray-600">Estimated Tax (12.5%):</p>
              <p className="font-semibold text-gray-800">$14,062</p>
            </div>
            <div>
              <p className="text-gray-600">Total BF to be Distributed:</p>
              <p className="font-semibold text-blue-600">${totalBFToDistribute.toLocaleString()}</p>
            </div>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      {currentWorkflow?.id && (
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'summary', label: 'Distribution Summary', icon: BarChart3 },
                { id: 'seachef', label: 'SEACHEF 50% Split', icon: PieChart }
              ].map((tab) => {
                const IconComponent = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setSelectedView(tab.id)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                      selectedView === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {currentWorkflow?.id && selectedView === 'summary' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Final Distribution Summary</h3>
            <div className="flex space-x-2">
              <Button intent="secondary" onClick={handleCalculateDistribution} className='whitespace-nowrap flex justify-center items-center'>
                <RefreshCw className="w-4 h-4 mr-2" />
                Recalculate
              </Button>
              <Button onClick={handleExportResults} className='whitespace-nowrap flex justify-center items-center'>
                <Download className="w-4 h-4 mr-2" />
                Export Results
              </Button>
            </div>
          </div>
          <div className="bg-white-200 rounded-lg border border-lightgray-100 max-h-96 overflow-y-auto">
            <Table header={summaryHeader} data={summaryData} isDashTable={false} enableOptions={false} />
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> Frontline entities are excluded from percentage-based distribution calculations
              as per business rules. SEACHEF allocations include 50% split logic where applicable.
            </p>
          </div>
        </div>
      )}

      {currentWorkflow?.id && selectedView === 'seachef' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">SEACHEF 50% Allocation Split</h3>
          </div>
          <div className="bg-white-200 rounded-lg border border-lightgray-100 max-h-96 overflow-y-auto">
            <Table header={seachefHeader} data={seachefData} isDashTable={false} enableOptions={false} />
          </div>
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <h4 className="font-semibold text-orange-800 mb-2">SEACHEF Split Logic:</h4>
            <ul className="text-sm text-orange-700 space-y-1">
              <li>• For entities where BF Allocation = 0.5, split the amount 50/50</li>
              <li>• 50% goes to the original SMC/Vessel combination</li>
              <li>• 50% goes to SEACHEF allocation pool</li>
              <li>• This applies to specific SMCs: BSM CHI, BSM GER, SEACHEF, BSM HEL, BSM SG, BSM India, BSM CY</li>
            </ul>
          </div>
        </div>
      )}
      </div>

      {/* Navigation Buttons - Always visible */}
      <div className="flex-shrink-0 flex justify-between items-center bg-white-200 border border-lightgray-100 rounded p-4 mt-4">
        <Button onClick={onPrevious} intent="secondary">
          ← Back to Transformation
        </Button>
        <Button onClick={onNext}>
          Proceed to Export →
        </Button>
      </div>
    </div>
  );
};

export default Distribution;
