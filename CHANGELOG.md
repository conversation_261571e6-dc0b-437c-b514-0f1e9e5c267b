## [1.45.2](https://github.com/StackProTech/astrai_webapp/compare/v1.45.1...v1.45.2) (2024-01-16)


### Bug Fixes

* removed license file ([d2161eb](https://github.com/StackProTech/astrai_webapp/commit/d2161eb3737c99a0c2f01cc713b4419831caacac))

## [1.45.1](https://github.com/StackProTech/astrai_webapp/compare/v1.45.0...v1.45.1) (2024-01-01)


### Bug Fixes

* fixed styling issues ([0b3e53d](https://github.com/StackProTech/astrai_webapp/commit/0b3e53de852b1425cde324d9219444ec312ed733))
* integrated api and fixed styling ([027572b](https://github.com/StackProTech/astrai_webapp/commit/027572ba28a7388a451d92cca5a5ce2416b6f050))

# [1.45.0](https://github.com/StackProTech/astrai_webapp/compare/v1.44.4...v1.45.0) (2023-12-20)


### Features

* added documentation links ([288fcfc](https://github.com/StackProTech/astrai_webapp/commit/288fcfc0b25774e866e730f237c9d795ade57b33))

## [1.44.4](https://github.com/StackProTech/astrai_webapp/compare/v1.44.3...v1.44.4) (2023-12-16)


### Bug Fixes

* logging out when token is not valid ([31bced2](https://github.com/StackProTech/astrai_webapp/commit/31bced2e4a9bf6a7bda37d932a6dab48634e846a))

## [1.44.3](https://github.com/StackProTech/astrai_webapp/compare/v1.44.2...v1.44.3) (2023-12-12)


### Bug Fixes

* changed function name ([581d0ad](https://github.com/StackProTech/astrai_webapp/commit/581d0ad8e42b58d5686c8f5d5ed7b0f5fd5aafa2))
* changed tour steps ([a9c9e27](https://github.com/StackProTech/astrai_webapp/commit/a9c9e27413978d37b585c01673aa1b7e9aaa9799))
* fixed authentication header and routes ([b653a09](https://github.com/StackProTech/astrai_webapp/commit/b653a0950f9deb785dba9b4bc652f3b5270618db))
* fixed few header issues and done refactoring ([28b7cf4](https://github.com/StackProTech/astrai_webapp/commit/28b7cf41c881a7fb240b1ce18f2f041ecad9cec8))
* fixed name issue ([14a3dfe](https://github.com/StackProTech/astrai_webapp/commit/14a3dfed571316dcab34e66b8e3423a42a6c52a0))

## [1.44.2](https://github.com/StackProTech/astrai_webapp/compare/v1.44.1...v1.44.2) (2023-12-06)


### Bug Fixes

* done code refactoring ([14129c0](https://github.com/StackProTech/astrai_webapp/commit/14129c0bfdd7261ed853aea2492103950b403fc2))
* done refactoring ([048d374](https://github.com/StackProTech/astrai_webapp/commit/048d3748ee339bd193f869cafe444f748c901ba9))
* fixed implementation of data set and few styling issues ([72583cf](https://github.com/StackProTech/astrai_webapp/commit/72583cfb0e17c6a2997cfca4f3427d09488ecd41))
* fixed one back issue ([fbf9618](https://github.com/StackProTech/astrai_webapp/commit/fbf961881056e3c02b7c3d8a79398e254d608eac))

## [1.44.1](https://github.com/StackProTech/astrai_webapp/compare/v1.44.0...v1.44.1) (2023-11-30)


### Bug Fixes

* added logic for harmonization ([9ff9fa0](https://github.com/StackProTech/astrai_webapp/commit/9ff9fa0cc34b4b11a4b6b231d993467a1dc43737))
* added name in headers of  add pages ([9747e4e](https://github.com/StackProTech/astrai_webapp/commit/9747e4eacaca0b1365642d18df6e34c58f6fa83b))
* corrected code placement ([7dc5620](https://github.com/StackProTech/astrai_webapp/commit/7dc56207a18e82659b5f64d8fa769df54fff093d))
* fixed search ion selectDate and attribute harmonization ([7cad32d](https://github.com/StackProTech/astrai_webapp/commit/7cad32d964a6487359bac07e75fa2dfe062ac6c3))
* fixed svg issues ([ccb4d5e](https://github.com/StackProTech/astrai_webapp/commit/ccb4d5e410849e58cedc31bf1e60765ad9a6d931))
* fixed table and styling ([3d3265a](https://github.com/StackProTech/astrai_webapp/commit/3d3265a84ffc151733a2d5b5425d4ae69226a310))
* fixed view screen of business model ([a805899](https://github.com/StackProTech/astrai_webapp/commit/a8058995546dad284a8fc3e74ad6d23a34d85b63))

# [1.44.0](https://github.com/StackProTech/astrai_webapp/compare/v1.43.1...v1.44.0) (2023-11-25)


### Features

* added insights dashboards new view ([e61a557](https://github.com/StackProTech/astrai_webapp/commit/e61a5573503f073fbc4f709ac4a2335ddedfb32c))
* removed logo from configuration cockpit screen ([224e8d0](https://github.com/StackProTech/astrai_webapp/commit/224e8d0fc7956741e2a79a936873837dc184bd33))

## [1.43.1](https://github.com/StackProTech/astrai_webapp/compare/v1.43.0...v1.43.1) (2023-11-22)


### Bug Fixes

* done refactoring ([af11b60](https://github.com/StackProTech/astrai_webapp/commit/af11b601eb24ea273fd425367887ae10171166ba))
* fixed styling for configurationCockpit ([e011a5a](https://github.com/StackProTech/astrai_webapp/commit/e011a5a798d38099981fe0ec499a0629264b36fe))

# [1.43.0](https://github.com/StackProTech/astrai_webapp/compare/v1.42.0...v1.43.0) (2023-11-21)


### Bug Fixes

* fixed button disable issue in edit ([1d6eeb1](https://github.com/StackProTech/astrai_webapp/commit/1d6eeb1cc6840e5b7534ec99776df48ca03d793b))
* fixed config form data update issue in edit mode ([a28141c](https://github.com/StackProTech/astrai_webapp/commit/a28141c931ef5450e7fb7cedfbd6044fc30beca5))


### Features

* fix modal issue ([b29b673](https://github.com/StackProTech/astrai_webapp/commit/b29b6733c8985bd35df3ec7328f22e71ca87fad8))

# [1.42.0](https://github.com/StackProTech/astrai_webapp/compare/v1.41.3...v1.42.0) (2023-11-19)


### Bug Fixes

* added flow route in sidebar ([eaa216d](https://github.com/StackProTech/astrai_webapp/commit/eaa216d9b44fb6199457b8b77bfafd67fded1db5))
* fixed sidebar for route /businessModelling/flow ([ea9f64d](https://github.com/StackProTech/astrai_webapp/commit/ea9f64dd76ad2747108ba063802b44eb4ffb3d60))
* heilighted handles on edge hover ([ec5c36f](https://github.com/StackProTech/astrai_webapp/commit/ec5c36f05312a4109f5852f3e7b82290d2fe64bc))
* latency in clicking checkbox in  select data in LE, width of select in table is restricted ([4ab0b2b](https://github.com/StackProTech/astrai_webapp/commit/4ab0b2b15cd178a906374e90b7da70bb8836d0af))
* resolved merge conflicts ([27d9c58](https://github.com/StackProTech/astrai_webapp/commit/27d9c587ca5c862308c56223046d67caf0b211f1))
* **ui:** fixed the distance between edge and handle ([b5e47bb](https://github.com/StackProTech/astrai_webapp/commit/b5e47bbe4928e9f7461df350246e7a8f51729b59))
* **ui:** fixed the edge hovering inside a subgroup ([162b49a](https://github.com/StackProTech/astrai_webapp/commit/162b49af55cb2c523c20f243e42d9fa602f88e53))
* **ui:** fixed the node highlight issue and added a gap between nodes in tables ([89c84bf](https://github.com/StackProTech/astrai_webapp/commit/89c84bfc321ebc0a7c3ab10b6492e1af661a0169))
* **ui:** fixed the styles according to figma ([8d0837e](https://github.com/StackProTech/astrai_webapp/commit/8d0837ea2b415ed50ac5d6121a6539a8be7975ad))
* **ui:** made the edges as grayish when not hovered and blue on hover ([a43d6c4](https://github.com/StackProTech/astrai_webapp/commit/a43d6c45c27b18e4c34a2a57867b6537d6f190d2))


### Features

* added react flow and page for it ([7ca1116](https://github.com/StackProTech/astrai_webapp/commit/7ca1116ec95d9db94cc72a6e4818e8dcea410c60))
* animation added and node hover effect added ([1614452](https://github.com/StackProTech/astrai_webapp/commit/16144524eb1bc303cf681eaa95e47d408ab4f39b))
* **ui:** added the custom parent group node ([65be8e3](https://github.com/StackProTech/astrai_webapp/commit/65be8e338c999e6ab201ea0aa60d407485fe993c))
* **ui:** added the groups, tables, nodes, and hover effect on edge ([c32c46d](https://github.com/StackProTech/astrai_webapp/commit/c32c46d8f2dea5927ced87338b6de3d542b760d3))
* **ui:** added the source and target node highlighting and unhighlighting on hover ([b784299](https://github.com/StackProTech/astrai_webapp/commit/b78429964fffdf0221b0477da70780db621f0949))
* **ui:** added the style to hide the handle ([b475944](https://github.com/StackProTech/astrai_webapp/commit/b47594420ca32ee7203b42ff3f7bb619da30a19b))
* **ui:** added the sub group type node ([4a30e8c](https://github.com/StackProTech/astrai_webapp/commit/4a30e8cb7b27aabeee2fb6e524aed8060a205ee2))
* **ui:** added the testdata integration for edges ([9dc2e40](https://github.com/StackProTech/astrai_webapp/commit/9dc2e403940ca423a55848dd28f1ce50cf2e9e96))
* **ui:** disabled the user interactions in reactflow ([7ec569b](https://github.com/StackProTech/astrai_webapp/commit/7ec569bf266ec0ff6d87477a51a9b2a7349acffa))
* **ui:** fixed the issue of edge-node highlighting and styled the table and nodes ([715509e](https://github.com/StackProTech/astrai_webapp/commit/715509e95a21946bc430bccbe962569c552f259e))
* **ui:** fixed the overflow text of table header in table group node ([cbbb95d](https://github.com/StackProTech/astrai_webapp/commit/cbbb95db73b366f3af440bb3a370ba6fc9930782))
* **ui:** fixed the padding in table header ([92ae474](https://github.com/StackProTech/astrai_webapp/commit/92ae4740a39c27f68197034c4e0347c26ddec02b))
* **ui:** integrated the consume groups with testdata for nodes ([71d2a69](https://github.com/StackProTech/astrai_webapp/commit/71d2a69d21201d0614b4c1b0f6436c1d8cee6f4e))
* **ui:** integrated the flow with testdata for col_name and connection_info ([2f44f98](https://github.com/StackProTech/astrai_webapp/commit/2f44f985986a4c7268ce2c6676800fb7f04dccae))
* **ui:** integrated the raw and enrich groups with testdata for nodes ([047f58d](https://github.com/StackProTech/astrai_webapp/commit/047f58d48ae503c078fc085233c8f195f56b5406))
* **ui:** made the tables in a row configurable ([f86301c](https://github.com/StackProTech/astrai_webapp/commit/f86301c03018b5993a5c72ec657e699812c06e6d))
* **ui:** removed the tooltip from the table header and reduced the font size ([eaec9c0](https://github.com/StackProTech/astrai_webapp/commit/eaec9c09c93d91ccd599e8696d3f4d9897339770))

## [1.41.3](https://github.com/StackProTech/astrai_webapp/compare/v1.41.2...v1.41.3) (2023-11-17)


### Bug Fixes

* fixed alert on business model update ([fdf2bd5](https://github.com/StackProTech/astrai_webapp/commit/fdf2bd5fbebabf6e90a3dc4c02142969bfe12223))

## [1.41.2](https://github.com/StackProTech/astrai_webapp/compare/v1.41.1...v1.41.2) (2023-11-17)


### Bug Fixes

* dashboards height fix ([8d35292](https://github.com/StackProTech/astrai_webapp/commit/8d35292d10339169fdacd851d635ba543f39bc3b))

## [1.41.1](https://github.com/StackProTech/astrai_webapp/compare/v1.41.0...v1.41.1) (2023-11-17)


### Bug Fixes

* added fix for table update ([3dd8110](https://github.com/StackProTech/astrai_webapp/commit/3dd8110d53103d5fbd2bd4480cba444eca3ad9c6))
* fixed infinite rendering issue in listBox ([a6259ee](https://github.com/StackProTech/astrai_webapp/commit/a6259ee5dcdd166f2bf9ff464c3f217b734a0f4a))
* fixed state update issue ([b030a6f](https://github.com/StackProTech/astrai_webapp/commit/b030a6fc3a4630be1052a3521cd3fa6ced7e94fe))
* fixed table update issue in datasets ([ebc5a42](https://github.com/StackProTech/astrai_webapp/commit/ebc5a4297fca0a4ef044fda3c194d0fa710ddb20))

# [1.41.0](https://github.com/StackProTech/astrai_webapp/compare/v1.40.0...v1.41.0) (2023-11-17)


### Features

* issue 118 and 162 ([65b2807](https://github.com/StackProTech/astrai_webapp/commit/65b28076e74e6e818f40db9e711fe30bf494bc79))

# [1.40.0](https://github.com/StackProTech/astrai_webapp/compare/v1.39.0...v1.40.0) (2023-11-17)


### Bug Fixes

* fixed business model create flow when custom attribute is added ([dc2495e](https://github.com/StackProTech/astrai_webapp/commit/dc2495e35fdfd45704d6451102188157fe5d2942))
* fixed button disability and padding issue ([e9a0983](https://github.com/StackProTech/astrai_webapp/commit/e9a09835739063cbcf0c51133cc315aab3815efe))
* fixed dataset update issue ([7c07a01](https://github.com/StackProTech/astrai_webapp/commit/7c07a01200dc543420a674e3fdf2c5af0b67b7b0))
* fixed few styling issues ([b853375](https://github.com/StackProTech/astrai_webapp/commit/b8533758011aa55b9b296bf81e283bdea047da76))
* fixed few styling issues ([919d371](https://github.com/StackProTech/astrai_webapp/commit/919d37172bbf21d1ea72f515c546f3c8ce5e4a24))
* fixed homepage responsive issue when sidebar is expanded ([0a3582a](https://github.com/StackProTech/astrai_webapp/commit/0a3582a8521d2c08ebff0efbbffee936c0e8c12a))
* fixed issue of table update on delete ([c783fab](https://github.com/StackProTech/astrai_webapp/commit/c783fabc3c14cb33c8ada66f6ec6bfb23de3bcab))
* fixed the data update issue on del create and refresh in system integration ([8317600](https://github.com/StackProTech/astrai_webapp/commit/8317600423141448421c5d25397bb0a11cfd9559))
* fixed toggle issue ([ca67f75](https://github.com/StackProTech/astrai_webapp/commit/ca67f75b75e716b4f63f62dbd37e839f1fddc01a))
* join config - making other fields blank when parent table is changed ([76a0a10](https://github.com/StackProTech/astrai_webapp/commit/76a0a10e3be1cd4de2918f2f3705a1805c360c40))
* merge branch ([3bfc80a](https://github.com/StackProTech/astrai_webapp/commit/3bfc80ac9bddcef1eb576886c6420a729f403082))
* mering branch ([bf5cb68](https://github.com/StackProTech/astrai_webapp/commit/bf5cb685f2178f1468d263fb92b38977238aca5a))
* removed edit from system Integration offline connect and commented unused code ([367a81b](https://github.com/StackProTech/astrai_webapp/commit/367a81b1f17868562b2d49ea0e65de6c0bfe6e07))
* removed unused code ([b91e3e0](https://github.com/StackProTech/astrai_webapp/commit/b91e3e003661f0239d8044717e9f7dfd839db502))
* removed unused package ([ded2c0a](https://github.com/StackProTech/astrai_webapp/commit/ded2c0ab58c1da95556f8df10b523099dac311f0))


### Features

* harmanise popup logic ([300691c](https://github.com/StackProTech/astrai_webapp/commit/300691c6859ec8773ab90f722900785716169598))
* issue no. 143, 150, added blur to table input lag issue ([8f6f97f](https://github.com/StackProTech/astrai_webapp/commit/8f6f97f75ffb7c8c57850873e3359de3ff7edeb2))
* table input lag ([514dc3e](https://github.com/StackProTech/astrai_webapp/commit/514dc3e5e3d9eedf9ce4cb0e1789671a7bd0bdb5))
* ticket no. 157 and 117 ([16ca3bc](https://github.com/StackProTech/astrai_webapp/commit/16ca3bc2933a8d24603bb90b817092263dd740d7))
* tooltip issue and upload api for mapping ([0c09ad3](https://github.com/StackProTech/astrai_webapp/commit/0c09ad3345c52b82747ddaa364362b576ac2418d))

# [1.39.0](https://github.com/StackProTech/astrai_webapp/compare/v1.38.0...v1.39.0) (2023-11-13)


### Bug Fixes

* changed all svg to image ([4b66d59](https://github.com/StackProTech/astrai_webapp/commit/4b66d5945ab49ef55bd043a2d3231f1d430f2b89))
* fixed checkbox state ([baee81a](https://github.com/StackProTech/astrai_webapp/commit/baee81af2b3c4733a6cba1686056ef5637341683))
* fixed icon side issue info message issue and alert issue ([d4441be](https://github.com/StackProTech/astrai_webapp/commit/d4441be220a1dadcaef059e355a8f1f51d154a94))
* fixed issue with custom attributes in business model creation ([dc667e6](https://github.com/StackProTech/astrai_webapp/commit/dc667e6d74618b85f12840c877a54c04af3e56ef))
* moved all svg constant to constants file ([f2f225c](https://github.com/StackProTech/astrai_webapp/commit/f2f225c6e29abbabe996256dd1f122f4cee7ce53))
* on edit logical entity, when a dataset is added, attribute harmonization is disturbed ([1f56c73](https://github.com/StackProTech/astrai_webapp/commit/1f56c73cee0a43f7c53d37676001f4b8a25c2c5f))
* organised the code and added basic comments ([b2830ed](https://github.com/StackProTech/astrai_webapp/commit/b2830ed57115bcc84c84339043fb6f9575798c8a))
* resolved merge conflict ([656bcd3](https://github.com/StackProTech/astrai_webapp/commit/656bcd383da7624898a24e8a1cb1d51e822f4b5d))


### Features

* bug fixes ([a351b4a](https://github.com/StackProTech/astrai_webapp/commit/a351b4ac9083c92b6db0e72b846a1a3a0d45f3a9))
* commented table overlay ([07fc1e3](https://github.com/StackProTech/astrai_webapp/commit/07fc1e3e3f81782f191de9afa38a68db332e0bf7))
* fixed jira tickets 115, 116, 121, 113 ([402641d](https://github.com/StackProTech/astrai_webapp/commit/402641d40b6815f1e9bba3f5c004acacba068f28))
* view business model ([a918c59](https://github.com/StackProTech/astrai_webapp/commit/a918c59b267f9517a0bb7caf8ef8d5dda8e6be95))

# [1.38.0](https://github.com/StackProTech/astrai_webapp/compare/v1.37.0...v1.38.0) (2023-11-10)


### Bug Fixes

* corrected spelling ([29e3cc8](https://github.com/StackProTech/astrai_webapp/commit/29e3cc8d2682515256c78516ab6d7e7dfc4319d6))
* corrected spelling ([0051918](https://github.com/StackProTech/astrai_webapp/commit/0051918bdc8d70d577fd88f8e8c37e53d729ddac))
* fixed search variable ([f0fdde9](https://github.com/StackProTech/astrai_webapp/commit/f0fdde95d2129cabaa4c24d91fb3a2adc5536d8d))
* merging branch ([5462926](https://github.com/StackProTech/astrai_webapp/commit/5462926cc9b7804646fe41cc6aab115b610adf3c))


### Features

* added loaders ([018be54](https://github.com/StackProTech/astrai_webapp/commit/018be54ec1b904cf10476e73e13f4e8a123dbd31))
* added loaders ([3c655d4](https://github.com/StackProTech/astrai_webapp/commit/3c655d479c2cf04886abb258dcc6bdcc79a8ea16))

# [1.37.0](https://github.com/StackProTech/astrai_webapp/compare/v1.36.3...v1.37.0) (2023-11-09)


### Bug Fixes

* fixed landing page styling and navigation corrected sidebar and dropdown ([e969f0b](https://github.com/StackProTech/astrai_webapp/commit/e969f0b39c14a9b46718c97d6588e570089c66d8))
* fixed spacing and font issues ([3f4dde0](https://github.com/StackProTech/astrai_webapp/commit/3f4dde0bf3b77f8e97a79077e041b0d023db3f7a))
* merging branch ([36795c5](https://github.com/StackProTech/astrai_webapp/commit/36795c58946802301d2425cbabae8ca663f48ea5))


### Features

* add custom popup not opening ([6196401](https://github.com/StackProTech/astrai_webapp/commit/61964018661426a310258db5cda53a62fbc06c8d))
* business model edit midway ([d134570](https://github.com/StackProTech/astrai_webapp/commit/d134570ad37450e7ecbda5bf987e8d494e1c39b5))
* changes btn text ([34408f2](https://github.com/StackProTech/astrai_webapp/commit/34408f22b433d2bd943203e7ce30f8c4ba4f08a5))
* merge fix ([2f3e83c](https://github.com/StackProTech/astrai_webapp/commit/2f3e83cfa66ae38926a10c111ec686c70b657daf))
* removed validation for join query ([86f097a](https://github.com/StackProTech/astrai_webapp/commit/86f097a6fdd4e813943970e6c0265f2e5d858779))

## [1.36.3](https://github.com/StackProTech/astrai_webapp/compare/v1.36.2...v1.36.3) (2023-11-08)


### Bug Fixes

* merged branch ([0f1a448](https://github.com/StackProTech/astrai_webapp/commit/0f1a4482090aa551893ebba3eb3192a09809d7d7))

## [1.36.2](https://github.com/StackProTech/astrai_webapp/compare/v1.36.1...v1.36.2) (2023-11-07)


### Bug Fixes

* changed complete page html ([415250b](https://github.com/StackProTech/astrai_webapp/commit/415250b3d0838342df16078360fe273635372f51))

## [1.36.1](https://github.com/StackProTech/astrai_webapp/compare/v1.36.0...v1.36.1) (2023-11-07)


### Bug Fixes

* fixed select state issue in selectData Tab ([7109971](https://github.com/StackProTech/astrai_webapp/commit/7109971fce3e44c81c01dd74b58e533adf9696f9))
* fixed styling issue in warning popup ([8e4b841](https://github.com/StackProTech/astrai_webapp/commit/8e4b841755c2ad2ba801147848a07bd6b5a491f0))
* optimized the table code ([b1e2c3d](https://github.com/StackProTech/astrai_webapp/commit/b1e2c3d9791a5bcd3a7a4cef8abe3c44a4cef8c0))
* refactored Warning popups ([4f782ab](https://github.com/StackProTech/astrai_webapp/commit/4f782ab7552be4d046dc2aaa0dd0a767da36db55))
* removed modal from the component table and added functions in component ([82970e4](https://github.com/StackProTech/astrai_webapp/commit/82970e490bf8ba80a6fff0d3b708fa536af6b359))

# [1.36.0](https://github.com/StackProTech/astrai_webapp/compare/v1.35.0...v1.36.0) (2023-11-03)


### Features

* added datatype, length and column in mapped attribute ([9ad863f](https://github.com/StackProTech/astrai_webapp/commit/9ad863f6b44be0dfdde884aa95c5ce54841dda74))
* sending datatype, length and actual column name in mapped attributes ([f4b9521](https://github.com/StackProTech/astrai_webapp/commit/f4b95218096599ad861b3b59bc075f7a70f15125))

# [1.35.0](https://github.com/StackProTech/astrai_webapp/compare/v1.34.0...v1.35.0) (2023-11-03)


### Bug Fixes

* added checkbox ([bb7f9d2](https://github.com/StackProTech/astrai_webapp/commit/bb7f9d2e466db607fc9b463743e480a49a70589d))
* added tooltip in listbox ([8f85f12](https://github.com/StackProTech/astrai_webapp/commit/8f85f128d783393a312292133322bd779d1e76b1))
* fixed input value issue in table select Data ([02cb837](https://github.com/StackProTech/astrai_webapp/commit/02cb837337e8cb9b065b366d38bef84f8bec55e6))
* fixed spacing issue ([9afa3ae](https://github.com/StackProTech/astrai_webapp/commit/9afa3aef4eb85142aecb8846b48033b01cefaf7e))
* made changes for checkbox in styling ([0a90738](https://github.com/StackProTech/astrai_webapp/commit/0a907381fab5ca8436311be2f060503345e6627a))
* merge branch ([c6330b7](https://github.com/StackProTech/astrai_webapp/commit/c6330b748d5a6577a006c64b8dbdc0bce28e4168))
* merged branch ([63bac16](https://github.com/StackProTech/astrai_webapp/commit/63bac1660b0644eee3838399bf8a7f7c8b16d5bc))


### Features

* fixed spacing issues ([4c12ef3](https://github.com/StackProTech/astrai_webapp/commit/4c12ef3173f990a8816e61edd1cb05993b78376d))
* height issue fix and upload logo ([a3e3cda](https://github.com/StackProTech/astrai_webapp/commit/a3e3cda8b766cc1348fa460260e79b893abef419))

# [1.34.0](https://github.com/StackProTech/astrai_webapp/compare/v1.33.0...v1.34.0) (2023-11-03)


### Bug Fixes

* business schema join config dropdown placeholder fix ([770f36b](https://github.com/StackProTech/astrai_webapp/commit/770f36bb95a8ef3544012ab80cf1c2ce9f0fcf54))
* fixed aggregation error ([f03280d](https://github.com/StackProTech/astrai_webapp/commit/f03280db6bd930b0aae26a26e500d1a9a2df2db8))
* fixed attribute table update issue ([00e08ba](https://github.com/StackProTech/astrai_webapp/commit/00e08bae319f44c5e9ae44ca8b2a30c6d6bc1e17))
* fixed few styling issues ([deedf21](https://github.com/StackProTech/astrai_webapp/commit/deedf212d6040ffee3fbcfb1fc3382e26c6a815a))
* fixed reset data ([0899c0b](https://github.com/StackProTech/astrai_webapp/commit/0899c0bea476b53bb022ed30ba4d827fa4a3d095))
* merged branch ([6f80fe0](https://github.com/StackProTech/astrai_webapp/commit/6f80fe0601c0fa747611740bda01f486e9094e49))
* next btn logic in new entity ([2bfee1e](https://github.com/StackProTech/astrai_webapp/commit/2bfee1e2477c3127d5d1d79f3b3ef37576df1e88))


### Features

* business modelling next btns ([f221604](https://github.com/StackProTech/astrai_webapp/commit/f22160467bb09756e78e600361a2fac704c583f9))
* data collab view log and external collaborator page added ([df83aa4](https://github.com/StackProTech/astrai_webapp/commit/df83aa405c6cc7f4480cb0d2186732989e48d2e3))
* delete api and ui fixes and table name sorting ([126ea33](https://github.com/StackProTech/astrai_webapp/commit/126ea33b9b0d161a19bf806a04331ffbb9c7bf98))

# [1.33.0](https://github.com/StackProTech/astrai_webapp/compare/v1.32.0...v1.33.0) (2023-11-02)


### Bug Fixes

* added tooltip in table ([40ab72c](https://github.com/StackProTech/astrai_webapp/commit/40ab72c45eaa0dde1ff1107ee5ae2db92ffc44a8))
* fixed all styling issues added truncate ([2e04631](https://github.com/StackProTech/astrai_webapp/commit/2e046315d6aa1fe0f42f1c65849c0441d1d606c6))
* fixed loader and placeholder issues ([7582852](https://github.com/StackProTech/astrai_webapp/commit/7582852dbe395373b3106ac252f9c22a5c093eab))
* fixed scroll issues in business model tab ([98d647c](https://github.com/StackProTech/astrai_webapp/commit/98d647cc39c84973bcd6d69ea16c9e3b7fe19117))
* fixed select pannel position ([6a7a2fe](https://github.com/StackProTech/astrai_webapp/commit/6a7a2feb7cc5b3d2c61a165f6cfef9ab3b4e5573))
* fixed spelling issue ([7ce68ad](https://github.com/StackProTech/astrai_webapp/commit/7ce68ade9aa169e8e261bb7fad62ff9ec43f14cb))
* fixed table height isssue in join table ([96bdc79](https://github.com/StackProTech/astrai_webapp/commit/96bdc7923119b3a6e9a233bb598fba808cf62105))
* merging brach ([e4ead1a](https://github.com/StackProTech/astrai_webapp/commit/e4ead1a61c0a7caaacc8e3b2b85e33b953c6a844))
* merging branch ([0e149c7](https://github.com/StackProTech/astrai_webapp/commit/0e149c77c044cdc6a52b67b618db6c480e5c3a95))
* removed border ([d90c986](https://github.com/StackProTech/astrai_webapp/commit/d90c986d66127d37458f06a77e2f386251d5b0e8))


### Features

* fixed scroll popup ([e327ead](https://github.com/StackProTech/astrai_webapp/commit/e327ead68c34d612740f279ec0cab709159d8cb2))
* next btn fix ([1ad9fe9](https://github.com/StackProTech/astrai_webapp/commit/1ad9fe941895dc877038376783431831fec92984))
* search bar functionalities fixed ([b4d21f5](https://github.com/StackProTech/astrai_webapp/commit/b4d21f5697f6c05c7a373d71daa520670da4ba2f))

# [1.32.0](https://github.com/StackProTech/astrai_webapp/compare/v1.31.1...v1.32.0) (2023-10-31)


### Bug Fixes

* added data for aggregation in api ([6bc333d](https://github.com/StackProTech/astrai_webapp/commit/6bc333d624f5905d601fffec9a3a7bf86feafb6b))
* added refresh table when created new item in logical entity and business model ([09f9ac5](https://github.com/StackProTech/astrai_webapp/commit/09f9ac508233bc1aaac301207adf87661f86db73))
* fixed select data table issue ([c50981e](https://github.com/StackProTech/astrai_webapp/commit/c50981e6c5f9c8b860e3f1768d912406333f7052))
* fixed table and counter update issue ([8ae5f53](https://github.com/StackProTech/astrai_webapp/commit/8ae5f534ad95c0332495a4d8b8bf2b77a2d1376e))
* making api call from parent component of system integration ([04ab7be](https://github.com/StackProTech/astrai_webapp/commit/04ab7be23ab763619de6a76bc9417283d3170313))
* removed tab changes ([bbc40a9](https://github.com/StackProTech/astrai_webapp/commit/bbc40a93c099e615a4a466a7b7e20b762f304d8f))
* resolved merge conflict ([6d8d7ea](https://github.com/StackProTech/astrai_webapp/commit/6d8d7eade038b5f6eb944a48544147fe8642bcde))
* ui fixes for date changes ([edc74f3](https://github.com/StackProTech/astrai_webapp/commit/edc74f32ee8ab63063e3f9d7e573e841212cea40))


### Features

* added toast in state ([d9ba3c3](https://github.com/StackProTech/astrai_webapp/commit/d9ba3c32590af34225bef858aca585310e07fec9))
* upload in offline connection ([626a837](https://github.com/StackProTech/astrai_webapp/commit/626a837382dd441246e179a9346ac8d77681c583))

## [1.31.1](https://github.com/StackProTech/astrai_webapp/compare/v1.31.0...v1.31.1) (2023-10-31)


### Bug Fixes

* made the aggregation table working ([0cc18e2](https://github.com/StackProTech/astrai_webapp/commit/0cc18e2adc73dd48fa12cadcf055004b94ffded1))
* ui fixes and loader fixes ([7a827c9](https://github.com/StackProTech/astrai_webapp/commit/7a827c9c503eb8709494d87a2e4c446344bdf9c0))

# [1.31.0](https://github.com/StackProTech/astrai_webapp/compare/v1.30.0...v1.31.0) (2023-10-30)


### Bug Fixes

* addded loader for attribute table in logical entity ([85c827e](https://github.com/StackProTech/astrai_webapp/commit/85c827ec0ad423978b0e0f6479687c4f1b8d8b36))
* changed ERP to application ([1038050](https://github.com/StackProTech/astrai_webapp/commit/1038050a2229c9612d7261be27f788943d42f57d))
* disabled table in view mode in logical entities ([9b58732](https://github.com/StackProTech/astrai_webapp/commit/9b58732d539c842fc896306a2697ee6ece848553))
* disabled tabs when not completed ([d50014e](https://github.com/StackProTech/astrai_webapp/commit/d50014e8d24d50f49e17628dc7c7cbd8d4f419f3))
* fixed radio button issues ([37f14be](https://github.com/StackProTech/astrai_webapp/commit/37f14be9fe00c22433f3a49a38044248608e13f9))
* implemented api integration in aggregation but add new row and delete not working properly ([220953a](https://github.com/StackProTech/astrai_webapp/commit/220953a8e7aaec4d73d3436737907d6354be397b))
* view connection fixed ([5bc4394](https://github.com/StackProTech/astrai_webapp/commit/5bc4394742bc810abec587dc7834484d9ecd5a45))


### Features

* added ui fixes for create system integration ([840b6e2](https://github.com/StackProTech/astrai_webapp/commit/840b6e247454aea549f59f6a7b65ef61bee1ede4))
* fixed connection count ([24d4497](https://github.com/StackProTech/astrai_webapp/commit/24d4497a646044239acb19968d2482575413c57d))
* height fix ([742b5c5](https://github.com/StackProTech/astrai_webapp/commit/742b5c56717edf3c3d7970420c38fa5e5318cc70))
* ui fixes and data collab test api int ([aea5d6d](https://github.com/StackProTech/astrai_webapp/commit/aea5d6d9a01d07470d0bfbf144d9ce34a870b243))

# [1.30.0](https://github.com/StackProTech/astrai_webapp/compare/v1.29.0...v1.30.0) (2023-10-29)


### Features

* logo map for available source systems ([212a0ca](https://github.com/StackProTech/astrai_webapp/commit/212a0cadb2d80514533195981b0ca31c06f4dad5))

# [1.29.0](https://github.com/StackProTech/astrai_webapp/compare/v1.28.0...v1.29.0) (2023-10-29)


### Features

* added falback for loading ([7546981](https://github.com/StackProTech/astrai_webapp/commit/75469818e3a72797dacead750f71932f0583d5a9))

# [1.28.0](https://github.com/StackProTech/astrai_webapp/compare/v1.27.0...v1.28.0) (2023-10-29)


### Features

* added loader on the apis ([05624af](https://github.com/StackProTech/astrai_webapp/commit/05624afaf0e5cf76b6aa1345a114ee1999d9ffad))

# [1.27.0](https://github.com/StackProTech/astrai_webapp/compare/v1.26.0...v1.27.0) (2023-10-28)


### Bug Fixes

* existing attribute table fixed ([be20376](https://github.com/StackProTech/astrai_webapp/commit/be203764dbf54123e290e98d226b92c1dc568c7c))


### Features

* fixed issues in create business model ([f8f006e](https://github.com/StackProTech/astrai_webapp/commit/f8f006ec1a2c45f9b2c3e4b63965c989a28b10f7))
* integration with business schema api ([e5c80a7](https://github.com/StackProTech/astrai_webapp/commit/e5c80a7c45a84379fe6c810c0221de5c6bbd61f0))

# [1.26.0](https://github.com/StackProTech/astrai_webapp/compare/v1.25.0...v1.26.0) (2023-10-27)


### Features

* added dynamic position of menu items ([711da4b](https://github.com/StackProTech/astrai_webapp/commit/711da4b192003670d60519760fbd0ac612e95a92))
* added state for business model ([3950c6d](https://github.com/StackProTech/astrai_webapp/commit/3950c6d92cd3a57a2b256b3f4ee998d447be6aaa))
* changes for edit logical entities ([f58cd0c](https://github.com/StackProTech/astrai_webapp/commit/f58cd0c4984230be3fd741711e863692cda777c3))
* data in Existing Attributes ([a080fa9](https://github.com/StackProTech/astrai_webapp/commit/a080fa9ac518ac6e4034f91ff21d96adb4f337d5))
* integrated edit view ([b653899](https://github.com/StackProTech/astrai_webapp/commit/b65389912af7d2f47ca57f64abfd9a71f0e55e7f))
* integrated schema in business schema ([2ad806d](https://github.com/StackProTech/astrai_webapp/commit/2ad806dd71de51418a784507685455f5c122e7f8))
* integration for offiline connections ([3dac843](https://github.com/StackProTech/astrai_webapp/commit/3dac843f3513a90375d48acc313a66290a2e6dab))
* integration with dataset table data api ([9de8e62](https://github.com/StackProTech/astrai_webapp/commit/9de8e625fb5838b48b9889bc3a4ae74e3f38a5c4))
* menu items and overflow fix ([b1ee36c](https://github.com/StackProTech/astrai_webapp/commit/b1ee36c89772b449416e54c66ff8c47abdd3052d))
* state management in dataload ([e7110ca](https://github.com/StackProTech/astrai_webapp/commit/e7110cabe25c33683220097b5d12914b5801a600))
* submit offline connection api ([b0ed95e](https://github.com/StackProTech/astrai_webapp/commit/b0ed95edcb35096b6c397595189ea7c14260d50e))
* ui work in data collab ([4f94c7f](https://github.com/StackProTech/astrai_webapp/commit/4f94c7fe01907283ff8d185896263f96fba0a991))

# [1.25.0](https://github.com/StackProTech/astrai_webapp/compare/v1.24.0...v1.25.0) (2023-10-26)


### Bug Fixes

* fixed create entity flow ([410b835](https://github.com/StackProTech/astrai_webapp/commit/410b835adce5a9a0a242127ce3da8f9a1beb12e1))


### Features

* api integration ([0f413ff](https://github.com/StackProTech/astrai_webapp/commit/0f413ff4602cd3207f9eb7d44a001f7eab4bf2aa))
* data collaboration screens ([5871bfa](https://github.com/StackProTech/astrai_webapp/commit/5871bfaff7f8354b68eff66dda469303e294d9a5))
* edit api system integration ([8a50833](https://github.com/StackProTech/astrai_webapp/commit/8a50833c1b644eff831bd22728324d670a8c9786))
* select data dropddown to not be preselected ([5700849](https://github.com/StackProTech/astrai_webapp/commit/5700849cd67f5e21050bb8544f821237cbb387ae))
* using redux store for logical entity creation ([c6015bc](https://github.com/StackProTech/astrai_webapp/commit/c6015bcd51f331936b443c0e73bcb250deaa118f))
* warning popup for harmonisation ([d251e58](https://github.com/StackProTech/astrai_webapp/commit/d251e58abb0cb2dd6fe3dfc0a750e35940878e0f))

# [1.24.0](https://github.com/StackProTech/astrai_webapp/compare/v1.23.0...v1.24.0) (2023-10-22)


### Bug Fixes

* clear on close popup ([f6997cd](https://github.com/StackProTech/astrai_webapp/commit/f6997cdb442303fd86c5e060debfd4b5dc6b1437))
* dataset fixed table column ([5690ed1](https://github.com/StackProTech/astrai_webapp/commit/5690ed18e72b3e2bf9cff22144bc7527687f4824))


### Features

* disable next btns, add create btn, fixed add row ([e1dde5b](https://github.com/StackProTech/astrai_webapp/commit/e1dde5b9439ace1a22771c461570b838398e1a9f))

# [1.23.0](https://github.com/StackProTech/astrai_webapp/compare/v1.22.0...v1.23.0) (2023-10-22)


### Features

* added create button ([f8d387e](https://github.com/StackProTech/astrai_webapp/commit/f8d387e9e860d1b1d64c1df81c7dc1764aed0c2a))
* added integration for view connection ([f412fd8](https://github.com/StackProTech/astrai_webapp/commit/f412fd80e45c5ea9cd1ebe799b656415d164e21b))
* added integrations of api with create connection modal ([713f001](https://github.com/StackProTech/astrai_webapp/commit/713f001f7d828770c066846a4a984429710ecd2d))
* added tooltip for sidebar in collapsed view ([a3dcf0c](https://github.com/StackProTech/astrai_webapp/commit/a3dcf0ca648ef23b7fbaab4be0a51e27e7819897))
* integrated create entity get apis ([56190bf](https://github.com/StackProTech/astrai_webapp/commit/56190bf4d932410465e8cfd4347f9d05b031c566))
* integration of view source systems ([aee92b9](https://github.com/StackProTech/astrai_webapp/commit/aee92b92c94a716d315f81e2d26a97acf376dda7))
* integration with system integration partially ([429756e](https://github.com/StackProTech/astrai_webapp/commit/429756e3c512b07a0b771bf6d8928a684d154aa3))


### Reverts

* Revert "Revert "feat: edit api midway"" ([ab51832](https://github.com/StackProTech/astrai_webapp/commit/ab5183273bb9d5799418d911daa24f6978bbc2bb))
* Revert "Revert "feat: view connection and disable next btns"" ([3e81f55](https://github.com/StackProTech/astrai_webapp/commit/3e81f5534a1b00b56952faf1f313d298269b8cf3))

# [1.22.0](https://github.com/StackProTech/astrai_webapp/compare/v1.21.0...v1.22.0) (2023-10-21)


### Features

* integrated table ([77e150d](https://github.com/StackProTech/astrai_webapp/commit/77e150da8af3d4daec5b4424cf222a44042f3620))

# [1.21.0](https://github.com/StackProTech/astrai_webapp/compare/v1.20.0...v1.21.0) (2023-10-19)


### Features

* mapped JSON in fe to p ([85e9602](https://github.com/StackProTech/astrai_webapp/commit/85e96021c4806a9681f0d40cecf5207c2a0810b8))

# [1.20.0](https://github.com/StackProTech/astrai_webapp/compare/v1.19.1...v1.20.0) (2023-10-18)


### Features

* disabled hover effect in sidebar ([5965668](https://github.com/StackProTech/astrai_webapp/commit/596566863c0b19e8574a70facc524719d909af83))

## [1.19.1](https://github.com/StackProTech/astrai_webapp/compare/v1.19.0...v1.19.1) (2023-10-17)


### Bug Fixes

* corrected the link for ar dashboard ([5501e59](https://github.com/StackProTech/astrai_webapp/commit/5501e59a8c68d1b12d6225a45a52b02834abbcb2))

# [1.19.0](https://github.com/StackProTech/astrai_webapp/compare/v1.18.1...v1.19.0) (2023-10-16)


### Features

* integration of insight dashboards ([c6a976a](https://github.com/StackProTech/astrai_webapp/commit/c6a976ac716035fb8ad6a4f738f4aec04cca5ce7))

## [1.18.1](https://github.com/StackProTech/astrai_webapp/compare/v1.18.0...v1.18.1) (2023-10-13)


### Bug Fixes

* fixed few styling issues ([6ba4fd5](https://github.com/StackProTech/astrai_webapp/commit/6ba4fd5f53ecba369f1f3b1f5bf3e7236db3abf2))

# [1.18.0](https://github.com/StackProTech/astrai_webapp/compare/v1.17.3...v1.18.0) (2023-10-12)


### Bug Fixes

* corrected alerts added function and styling ([25ec4d5](https://github.com/StackProTech/astrai_webapp/commit/25ec4d5e13b15404efbc7d0fd84dd7810e086fe5))
* enabled add attribute button ([bb8dcef](https://github.com/StackProTech/astrai_webapp/commit/bb8dcef836f48466e91ee94d4e239b60f47a0225))


### Features

* added test data service function used it in systemIntegration screens ([128918d](https://github.com/StackProTech/astrai_webapp/commit/128918d312d9c0cbe9ec062dce84ee4f42b7cb60))
* made all the changes mentioned in the mail ([1c15d25](https://github.com/StackProTech/astrai_webapp/commit/1c15d25c5cc6e76148fe45f228df6143fba5bbee))

## [1.17.3](https://github.com/StackProTech/astrai_webapp/compare/v1.17.2...v1.17.3) (2023-10-10)


### Bug Fixes

* corrected icon name ([775f75f](https://github.com/StackProTech/astrai_webapp/commit/775f75fa7615153eab7bec98d1fac222efd242b9))
* nextjs Image tag is not working on amplify, changed it with native img ([09cf281](https://github.com/StackProTech/astrai_webapp/commit/09cf281573749db29679476645d874aa744d584f))

## [1.17.2](https://github.com/StackProTech/astrai_webapp/compare/v1.17.1...v1.17.2) (2023-10-10)


### Bug Fixes

* corrected mode in user mgmt ([10e49c8](https://github.com/StackProTech/astrai_webapp/commit/10e49c8065bb238b64af15cd70d8167ad39864f9))
* fixed localstorage issue and removed unwanted code from apiutilities and integrated login api ([3c120e7](https://github.com/StackProTech/astrai_webapp/commit/3c120e7b7a694ec0353e2217e0f0e719cee6e6d2))
* fixed styling for condensed table ([4cc77b2](https://github.com/StackProTech/astrai_webapp/commit/4cc77b28ea13b478172ad9c7055309f42eab4352))

## [1.17.1](https://github.com/StackProTech/astrai_webapp/compare/v1.17.0...v1.17.1) (2023-10-09)


### Bug Fixes

* changed file name to match the import ([5d5a778](https://github.com/StackProTech/astrai_webapp/commit/5d5a7782391f7e745de343f68ff7903c1a5e5dc4))

# [1.17.0](https://github.com/StackProTech/astrai_webapp/compare/v1.16.1...v1.17.0) (2023-10-09)


### Bug Fixes

* fixed import ([96f2c65](https://github.com/StackProTech/astrai_webapp/commit/96f2c655e569176458c1adb4980b1b01184dac9c))
* resolved merge conflicts ([b085c4b](https://github.com/StackProTech/astrai_webapp/commit/b085c4b75de000ba97699fb20f2ed19464368f33))


### Features

* updated the json test data files ([4a10ef6](https://github.com/StackProTech/astrai_webapp/commit/4a10ef6c297e64a4aecb2f1b8eef16be26bd3715))

## [1.16.1](https://github.com/StackProTech/astrai_webapp/compare/v1.16.0...v1.16.1) (2023-10-09)


### Bug Fixes

* fixed few color changes ([4597827](https://github.com/StackProTech/astrai_webapp/commit/45978271c2b09aee92f7d50c60ecd7365b7de2f7))
* fixed few common components sidebar and added services and dynamic form ([9c4d628](https://github.com/StackProTech/astrai_webapp/commit/9c4d628c0ab9a3cb1df6ebbeb340024f5b6dcc8a))
* fixed few query params issue and routes ([69b2e46](https://github.com/StackProTech/astrai_webapp/commit/69b2e46ee1bc25673fc4547363b3f1c0b0c51735))
* fixed folder structure ([6c368bb](https://github.com/StackProTech/astrai_webapp/commit/6c368bb75d4d5a7c4a1de58089e7907a59970502))
* removed padding ([8a7a27f](https://github.com/StackProTech/astrai_webapp/commit/8a7a27f8b6318609476492660f9a46219502f1fe))

# [1.16.0](https://github.com/StackProTech/astrai_webapp/compare/v1.15.2...v1.16.0) (2023-10-09)


### Bug Fixes

* fixed few height and width issues and fixed listbox issue ([f5ce969](https://github.com/StackProTech/astrai_webapp/commit/f5ce96960bc048e97e7e0d7cfb46b866cc9269e5))
* fixed few issues mentioned in the sheet ([d332853](https://github.com/StackProTech/astrai_webapp/commit/d332853fbf56a3fb2b37de4f5cf4bbfe9760f1ad))


### Features

* added missing testdata for logicalEntity  Screen ([607fd41](https://github.com/StackProTech/astrai_webapp/commit/607fd41737e544495f310b29434d8366d8817e91))
* added missing testdata for serDataTable details ([000d457](https://github.com/StackProTech/astrai_webapp/commit/000d457d7b9682eca8e1f1cb948674b0f2716651))
* added missing testdata for systemIntegration Screen ([9479e62](https://github.com/StackProTech/astrai_webapp/commit/9479e628ce9f7a171d675f4dcd2af1471eb5dcbc))
* added test data where required ([95011a7](https://github.com/StackProTech/astrai_webapp/commit/95011a79bc38460f85780a5ca7a56ddfcbb3b65e))
* added testdata for erp ([d371616](https://github.com/StackProTech/astrai_webapp/commit/d371616f22918334be69ac5887ddac2b2b9af887))
* change select list items in aggregation ([7813af7](https://github.com/StackProTech/astrai_webapp/commit/7813af7929b616b09f66480df5db4e5444780259))
* resolved merge conflict ([183b83f](https://github.com/StackProTech/astrai_webapp/commit/183b83fd9b00c3fc31718798fe66e615d1ed9a2b))

## [1.15.2](https://github.com/StackProTech/astrai_webapp/compare/v1.15.1...v1.15.2) (2023-10-04)


### Bug Fixes

* fixed the styling of auth layout ([fec4aaa](https://github.com/StackProTech/astrai_webapp/commit/fec4aaa62d0b60391de8aadbced0f6ff7050d57d))

## [1.15.1](https://github.com/StackProTech/astrai_webapp/compare/v1.15.0...v1.15.1) (2023-09-27)


### Bug Fixes

* fixed collapse pannel issue in select data ([f749284](https://github.com/StackProTech/astrai_webapp/commit/f749284439d75fa7463b62aeff49c51e1d29b033))
* fixed few component styling issues ([b646dd5](https://github.com/StackProTech/astrai_webapp/commit/b646dd564416157bd8d3cbdf095bfd6fe65b0675))

# [1.15.0](https://github.com/StackProTech/astrai_webapp/compare/v1.14.4...v1.15.0) (2023-09-21)


### Bug Fixes

* commiting pulled code ([78d4a0b](https://github.com/StackProTech/astrai_webapp/commit/78d4a0bf72148d6c640a78b6a67149266ae037e9))
* use entire table hight to display the data in the table ([ddbf562](https://github.com/StackProTech/astrai_webapp/commit/ddbf56209a00fc403db7a9c79177c3b9eef3bfca))


### Features

* initial setup of redux toolkit and implimenation of redux of side bar pin ([2071fe8](https://github.com/StackProTech/astrai_webapp/commit/2071fe81e777037a89aa407d1e566b78c2ffe076))

## [1.14.4](https://github.com/StackProTech/astrai_webapp/compare/v1.14.3...v1.14.4) (2023-09-20)


### Bug Fixes

* fixed height styling issues ([05d9f85](https://github.com/StackProTech/astrai_webapp/commit/05d9f8585ae3bb73dae999d59952d20f5fe5778d))
* fixed styling for auth screen ([e8b21d0](https://github.com/StackProTech/astrai_webapp/commit/e8b21d0ef4aab376c41b28d530d9e46597540b07))
* fixed styling for vie and edit logical entities ([99d2561](https://github.com/StackProTech/astrai_webapp/commit/99d256174cb0d679a33937c67c5dee24827b6677))

## [1.14.3](https://github.com/StackProTech/astrai_webapp/compare/v1.14.2...v1.14.3) (2023-09-19)


### Bug Fixes

* fixed modal height and made the inner div scrollable ([08a8173](https://github.com/StackProTech/astrai_webapp/commit/08a8173c41bb9ee0f29848edc04968401b9a5ebc))
* fixed modal height and made the innermost  div scrollable ([afbd8a4](https://github.com/StackProTech/astrai_webapp/commit/afbd8a48d50f6a9a4743543ab120f8015cb9527d))

## [1.14.2](https://github.com/StackProTech/astrai_webapp/compare/v1.14.1...v1.14.2) (2023-09-18)


### Bug Fixes

* added queryparams ([cdc8baf](https://github.com/StackProTech/astrai_webapp/commit/cdc8bafe53a75e4564a7f4f37da29df63d91aab2))
* corrected folder structure ([0c9ec1e](https://github.com/StackProTech/astrai_webapp/commit/0c9ec1e9a947df26dbf17d1a99b9ab6d2792fea2))
* corrected table ([a93688a](https://github.com/StackProTech/astrai_webapp/commit/a93688a718fdb8ac322859c23d947aa15575f012))
* created test data and corrected styling for few component ([7de9912](https://github.com/StackProTech/astrai_webapp/commit/7de991282c7e4fe5e0dab04e970bdc13f9e320fb))

## [1.14.1](https://github.com/StackProTech/astrai_webapp/compare/v1.14.0...v1.14.1) (2023-09-18)


### Bug Fixes

* changes for deployment ([c699107](https://github.com/StackProTech/astrai_webapp/commit/c699107b464ec63f070590e86696d7a85e1d7d17))

# [1.14.0](https://github.com/StackProTech/astrai_webapp/compare/v1.13.1...v1.14.0) (2023-09-15)


### Features

* added logic fir making tab as completed corrected tab component ([aecce0d](https://github.com/StackProTech/astrai_webapp/commit/aecce0da8e777cea957ac0b92346626f09994df4))

## [1.13.1](https://github.com/StackProTech/astrai_webapp/compare/v1.13.0...v1.13.1) (2023-09-13)


### Bug Fixes

* dataset detailed view css fix ([e84934d](https://github.com/StackProTech/astrai_webapp/commit/e84934d6ef9c057e8c566146ff3b993685b9a644))

# [1.13.0](https://github.com/StackProTech/astrai_webapp/compare/v1.12.0...v1.13.0) (2023-09-13)


### Bug Fixes

* added close and open function in alerts ([8a7b4eb](https://github.com/StackProTech/astrai_webapp/commit/8a7b4ebf86cfb3e01dd8f12b6ef8ac3f77087dcb))
* added fix for system integration styling on table ([7295369](https://github.com/StackProTech/astrai_webapp/commit/72953691502efc8ec660d27f6bffe94229981258))
* added flag in table to separate out dashboard tables ([e1f65ad](https://github.com/StackProTech/astrai_webapp/commit/e1f65add1efe1bc75903a9feff5fdeec5026855f))
* corrected alerts added function and styling ([cae338d](https://github.com/StackProTech/astrai_webapp/commit/cae338d6cfd320474d6e4e2eb6b7a4c6ad39b04a))
* corrected error message in alert ([799e9e1](https://github.com/StackProTech/astrai_webapp/commit/799e9e1eb4156d14dca63120df13c8af8151b165))
* fixed effects of expanding on sidebar ([bc5dadd](https://github.com/StackProTech/astrai_webapp/commit/bc5dadd49d63bea424e923589b93ee4119036da2))
* fixed height od table ([5e9b3ae](https://github.com/StackProTech/astrai_webapp/commit/5e9b3ae2c4b6ffe83f1f4b1e3c55f3c96ed6db58))
* fixed height od table ([bf1d571](https://github.com/StackProTech/astrai_webapp/commit/bf1d57196b59b2b7b79652a4005090a5b04a9079))


### Features

* add new online connection flow ([e80b2ff](https://github.com/StackProTech/astrai_webapp/commit/e80b2ff38cb52823b1df89184a27b28a539d7aa4))

# [1.12.0](https://github.com/StackProTech/astrai_webapp/compare/v1.11.2...v1.12.0) (2023-09-13)


### Bug Fixes

* added close and open function in alerts ([dea5789](https://github.com/StackProTech/astrai_webapp/commit/dea578904c1c9170feea03b1939a5aadc527267d))
* added fix for system integration styling on table ([370f100](https://github.com/StackProTech/astrai_webapp/commit/370f10011e9c781c5078b0f05a001b85663a2b9b))
* added flag in table to separate out dashboard tables ([51e65f5](https://github.com/StackProTech/astrai_webapp/commit/51e65f582f95372c4a408e4140f9cc86fe7acd30))
* corrected alerts added function and styling ([cf2b768](https://github.com/StackProTech/astrai_webapp/commit/cf2b768688a142aa8a8f1ac1b0da8a14e3c67e31))
* corrected error message in alert ([29ecc16](https://github.com/StackProTech/astrai_webapp/commit/29ecc16a52641f185657942e5ffa4d6b7fca475e))
* fixed effects of expanding on sidebar ([0f6c2e1](https://github.com/StackProTech/astrai_webapp/commit/0f6c2e171277b68b7f10e8f0eed98c60d3c46168))


### Features

* add new online connection flow ([473645f](https://github.com/StackProTech/astrai_webapp/commit/473645f690cce90211f3c66ade707af1fa9d7280))
* added logical entities screen, table, added blank screen for create logical entity ([dd4c371](https://github.com/StackProTech/astrai_webapp/commit/dd4c3715c8e247e6157694df39764019a168be16))

## [1.11.2](https://github.com/StackProTech/astrai_webapp/compare/v1.11.1...v1.11.2) (2023-09-13)


### Bug Fixes

* fixed effects of expanding on sidebar ([3deea12](https://github.com/StackProTech/astrai_webapp/commit/3deea1234411c79b3dd23fce3b338857bd29a4dd))

## [1.11.1](https://github.com/StackProTech/astrai_webapp/compare/v1.11.0...v1.11.1) (2023-09-13)


### Bug Fixes

* added fix for system integration styling on table ([759bd8a](https://github.com/StackProTech/astrai_webapp/commit/759bd8af8a8993d1ff46c411bf9fbafa4f1ea145))
* added flag in table to separate out dashboard tables ([10365d1](https://github.com/StackProTech/astrai_webapp/commit/10365d131bc90cf1a9e0d366bfe7fecf8272af8d))

# [1.11.0](https://github.com/StackProTech/astrai_webapp/compare/v1.10.2...v1.11.0) (2023-09-13)


### Features

* add new online connection flow ([463397d](https://github.com/StackProTech/astrai_webapp/commit/463397d7c5ce39d1fd6c6ebf571f4bfa513ee0de))

## [1.10.2](https://github.com/StackProTech/astrai_webapp/compare/v1.10.1...v1.10.2) (2023-09-12)


### Bug Fixes

* added close and open function in alerts ([568a040](https://github.com/StackProTech/astrai_webapp/commit/568a040acc2f7b662e397ee5f0e40ce3a7e367a7))
* corrected alerts added function and styling ([f663be3](https://github.com/StackProTech/astrai_webapp/commit/f663be3ee610b3faf3dd4547d1cf347caf4f4dcb))
* corrected error message in alert ([906a4cb](https://github.com/StackProTech/astrai_webapp/commit/906a4cbb360242737b8e98079924660460416326))

## [1.10.1](https://github.com/StackProTech/astrai_webapp/compare/v1.10.0...v1.10.1) (2023-09-12)


### Bug Fixes

* fixed padding and menu button ([fd17a91](https://github.com/StackProTech/astrai_webapp/commit/fd17a916fb1d37e4f05ebbe7eba3f01f0a30ae28))

# [1.10.0](https://github.com/StackProTech/astrai_webapp/compare/v1.9.0...v1.10.0) (2023-09-12)


### Bug Fixes

* fix for dropdown menu getting overlapped with toggle button ([b020818](https://github.com/StackProTech/astrai_webapp/commit/b020818cedc39e4e55d835b83389757f7602300d))
* fixed sidebar default active value ([16bb9bd](https://github.com/StackProTech/astrai_webapp/commit/16bb9bdedea6c121d3aba91325c0d2651f71dde1))
* fixed sidebar inner elements of dropdown ([da8b94b](https://github.com/StackProTech/astrai_webapp/commit/da8b94b708ea753873006cdb9d2a9a012e3be8e9))


### Features

* added pinnable sidebar ([21782d4](https://github.com/StackProTech/astrai_webapp/commit/21782d48349700f01397bf1522182eaccc38d3ad))
* added pinned image as per the state of sidebar ([3277ab2](https://github.com/StackProTech/astrai_webapp/commit/3277ab2d00c318184f178d64b89f2035be5b53c6))
* changes in alert styling ([4217647](https://github.com/StackProTech/astrai_webapp/commit/4217647097637e0108484d5af87a13f52ac5f440))

# [1.9.0](https://github.com/StackProTech/astrai_webapp/compare/v1.8.0...v1.9.0) (2023-09-12)


### Features

* added pinned image as per the state of sidebar ([18edc26](https://github.com/StackProTech/astrai_webapp/commit/18edc260b14ee8e4509e0182451357ec0add9f68))

# [1.8.0](https://github.com/StackProTech/astrai_webapp/compare/v1.7.2...v1.8.0) (2023-09-12)


### Features

* added pinnable sidebar ([3eb5761](https://github.com/StackProTech/astrai_webapp/commit/3eb576114aca7da5c2412c492abd37334fe87c14))

## [1.7.2](https://github.com/StackProTech/astrai_webapp/compare/v1.7.1...v1.7.2) (2023-09-11)


### Bug Fixes

* fix for dropdown menu getting overlapped with toggle button ([aeb3d30](https://github.com/StackProTech/astrai_webapp/commit/aeb3d307db6a052d42b973111676254fb33be720))

## [1.7.1](https://github.com/StackProTech/astrai_webapp/compare/v1.7.0...v1.7.1) (2023-09-11)


### Bug Fixes

* set default value ([66ee9b4](https://github.com/StackProTech/astrai_webapp/commit/66ee9b436419fe0ab2182c41f585d66c55df8856))

# [1.7.0](https://github.com/StackProTech/astrai_webapp/compare/v1.6.0...v1.7.0) (2023-09-11)


### Bug Fixes

* add same component in tab 2 also ([109d3f0](https://github.com/StackProTech/astrai_webapp/commit/109d3f0931c823f3b21c584927b042eb4ee66e71))
* fixed few styling issues ([ee326a7](https://github.com/StackProTech/astrai_webapp/commit/ee326a7b83d31fd73f4578189ab443ac05c8c0d3))
* fixed sidebar default and table data and function ([b72ed36](https://github.com/StackProTech/astrai_webapp/commit/b72ed36151ebdd562fca8a939784dcef331debde))
* fixed styling for sidebar ([0b67d2c](https://github.com/StackProTech/astrai_webapp/commit/0b67d2cdc4e9a3f2c5ad02e05f04ed7caf04a601))
* fixed toggle visibility issue ([46b4585](https://github.com/StackProTech/astrai_webapp/commit/46b458565778f6f045eb9133fbf1eef98f7399ef))
* removed un wanted div ([898caca](https://github.com/StackProTech/astrai_webapp/commit/898cacaddd62c96c804742a270374b5ce5c6d1db))
* removed un wanted div ([81bb435](https://github.com/StackProTech/astrai_webapp/commit/81bb435575aa00b95597f7ab59b62af35b8d024e))
* sent the header and data separately in props ([93450cd](https://github.com/StackProTech/astrai_webapp/commit/93450cd07f33882d914b63ccd17068e00e011f25))


### Features

* add erp list and modal ([d218c1d](https://github.com/StackProTech/astrai_webapp/commit/d218c1db81d551e2257046a5d500839e4d66144a))
* add erp list and modal ([0281982](https://github.com/StackProTech/astrai_webapp/commit/0281982c6380f7ab2b48f3a5182455f78e3b5a03))
* added clickfn to menuitem, which can be passed from the parent ([4be692d](https://github.com/StackProTech/astrai_webapp/commit/4be692de98d56f4ee14a969ee1581cc79541cac4))
* added screen for offline connection ([a227e9d](https://github.com/StackProTech/astrai_webapp/commit/a227e9d98dd6112301578a8812964419af05f4fb))

# [1.6.0](https://github.com/StackProTech/astrai_webapp/compare/v1.5.0...v1.6.0) (2023-09-11)


### Features

* added clickfn to menuitem, which can be passed from the parent ([bea14bf](https://github.com/StackProTech/astrai_webapp/commit/bea14bf941aeadd2d13b32f4b3fae3b0f97ae3ce))
* added screen for offline connection ([d8e6c3d](https://github.com/StackProTech/astrai_webapp/commit/d8e6c3d5870140d9618f3c4d6588f91c83f337ac))
* added screen for offline connection ([8b99e1f](https://github.com/StackProTech/astrai_webapp/commit/8b99e1ff937748eb6e2c9d0bcf57c1f9acb3b75d))

# [1.5.0](https://github.com/StackProTech/astrai_webapp/compare/v1.4.0...v1.5.0) (2023-09-11)


### Features

* added clickfn to menuitem, which can be passed from the parent ([4d18794](https://github.com/StackProTech/astrai_webapp/commit/4d187945a48c9edcd03c95848ec814adf796a7d0))

# [1.4.0](https://github.com/StackProTech/astrai_webapp/compare/v1.3.1...v1.4.0) (2023-09-11)


### Features

* add erp list and modal ([aa9f9d9](https://github.com/StackProTech/astrai_webapp/commit/aa9f9d9de5309804a872d1316a0088545124465e))

## [1.3.1](https://github.com/StackProTech/astrai_webapp/compare/v1.3.0...v1.3.1) (2023-09-10)


### Bug Fixes

* add same component in tab 2 also ([b354bd0](https://github.com/StackProTech/astrai_webapp/commit/b354bd0594c08c4f9f538e69d841980adf4c43e5))
* fixed toggle visibility issue ([5dfce44](https://github.com/StackProTech/astrai_webapp/commit/5dfce44941baa5b9cc57ecda377552ff4c01ed30))
* sent the header and data separately in props ([f6c2d61](https://github.com/StackProTech/astrai_webapp/commit/f6c2d619282898772cc5d17c627bd43adb6b3c4a))

# [1.3.0](https://github.com/StackProTech/astrai_webapp/compare/v1.2.0...v1.3.0) (2023-09-05)


### Bug Fixes

* changed the icon of check box ([34507c9](https://github.com/StackProTech/astrai_webapp/commit/34507c9c68f5293fc30b5a51c1128b10c08e1c53))
* changed the icon of check box ([99caabc](https://github.com/StackProTech/astrai_webapp/commit/99caabca770d3bead6eff20913e9c1bf3ed12b60))
* corrected tailwind class ([d91f44d](https://github.com/StackProTech/astrai_webapp/commit/d91f44de3eb8cbec8cf590c0afa4fc632010534f))
* resolved merge conflicts ([c89ab99](https://github.com/StackProTech/astrai_webapp/commit/c89ab9944840320e3107b50c4ff2ca5a761a30ce))


### Features

* added form validation using formik ([e37bcb8](https://github.com/StackProTech/astrai_webapp/commit/e37bcb88106a9eeba77a3675fe2784667ac1c60e))
* added sorting ([820829c](https://github.com/StackProTech/astrai_webapp/commit/820829cf158bb12605945bf58928f3bfdb23c934))
* completed table styling ([4b56668](https://github.com/StackProTech/astrai_webapp/commit/4b56668e85a87b3e44a308fef2ceeaa56ce63733))
* fix checkbox component ([160b455](https://github.com/StackProTech/astrai_webapp/commit/160b455459b9586bcae37275cf0434f2ab6f3a33))

# [1.2.0](https://github.com/StackProTech/astrai_webapp/compare/v1.1.0...v1.2.0) (2023-08-30)


### Bug Fixes

* changed the icon of check box ([0887579](https://github.com/StackProTech/astrai_webapp/commit/0887579a24bb53540efa5a24578befe54943e2c4))


### Features

* add ui component for menu dropdown ([aafde65](https://github.com/StackProTech/astrai_webapp/commit/aafde650e583854115b8549fccce9f95f9be1584))
* fix checkbox component ([02fe692](https://github.com/StackProTech/astrai_webapp/commit/02fe692f9785c174398f4001c5cf42ef3cc3aa64))

# [1.1.0](https://github.com/StackProTech/astrai_webapp/compare/v1.0.0...v1.1.0) (2023-08-30)


### Features

* add tabs and toggle component ([ca558c4](https://github.com/StackProTech/astrai_webapp/commit/ca558c486c23fdfb57a27bddd91e5f37cf387b60))

# 1.0.0 (2023-08-29)


### Features

* create btn/link/iconbtn/iconlink components and storybook implementations ([38bdd4d](https://github.com/StackProTech/astrai_webapp/commit/38bdd4dd9a447b5a27ad0d7c7c28f05b39c7705a))

# [3.18.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.17.0...v3.18.0) (2023-08-27)


### Features

* remove MIGRATE_DB which not needed anymore with process.env.NODE_ENV ([3fe81ae](https://github.com/ixartz/Next-js-Boilerplate/commit/3fe81ae98440b33ce18cee80265fdaa54e242184))

# [3.17.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.16.0...v3.17.0) (2023-08-27)


### Features

* add schema in drizzle instance and disable migrate in production ([5e26798](https://github.com/ixartz/Next-js-Boilerplate/commit/5e2679877a3da64a4cabfc22fdaacebd6abe6789))
* add script to migrate before building next.js ([220d05e](https://github.com/ixartz/Next-js-Boilerplate/commit/220d05e5d028852ccc533ca60b187bc3d47c5d73))
* do not run db migration when building on GitHub actions ([964cfa1](https://github.com/ixartz/Next-js-Boilerplate/commit/964cfa1a02fb41b387c851f0b2293c673859d60a))
* reload guestbook page when deployed on production ([c2e91b2](https://github.com/ixartz/Next-js-Boilerplate/commit/c2e91b2df944b0659d1768d2a7cc54a494d7d5c1))
* replace dotenv/config by dotenv-cli in db:studio NPM scripts ([f7f8743](https://github.com/ixartz/Next-js-Boilerplate/commit/f7f87435a984fa9d0407a7602d1ef38563c5e8d0))

# [3.16.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.15.0...v3.16.0) (2023-08-24)


### Bug Fixes

* build issues with prerendering ([ff117b9](https://github.com/ixartz/Next-js-Boilerplate/commit/ff117b9750e3609cebbf53a5dea01f0fbf94f865))


### Features

* add .env file for production ([58ed68c](https://github.com/ixartz/Next-js-Boilerplate/commit/58ed68cc2eefb1274e6e268c40a3ed8cd7d936be))
* add authToken support for production Turso ([26b8276](https://github.com/ixartz/Next-js-Boilerplate/commit/26b827618199f1dd73453c7ec021c13a4aaf5f7b))
* add await for migrate function ([96793f0](https://github.com/ixartz/Next-js-Boilerplate/commit/96793f0adedb10f802dfb46ff96b85f14c78ebf3))
* add database powered by Turso in guestbook page ([64073a5](https://github.com/ixartz/Next-js-Boilerplate/commit/64073a5babb38327a23fd3ae2b354152306e7977))
* add db file in gitignore ([cd45e09](https://github.com/ixartz/Next-js-Boilerplate/commit/cd45e0906cc79e87302ee6b88674089c5de059a3))
* add drizzle config and database schema ([df30388](https://github.com/ixartz/Next-js-Boilerplate/commit/df30388002ead9121ffb764e1bd11a71550cbe06))
* add style for guestbook ([339154c](https://github.com/ixartz/Next-js-Boilerplate/commit/339154ccfdaf7e53aeefd12fe0e347c645be5163))
* add typesafe environment variables ([5a2cd78](https://github.com/ixartz/Next-js-Boilerplate/commit/5a2cd78aca2fc60e6c0d4861ff656e7ba2ac86c4))
* create guestbook should not accept empty username and email ([37e4408](https://github.com/ixartz/Next-js-Boilerplate/commit/37e4408f968b36332a0a8ae9a90c687eee7fb4a0))
* implement AddGuestbookForm to create new guestbook message ([d7b37e6](https://github.com/ixartz/Next-js-Boilerplate/commit/d7b37e63f65d528e599b14d64cbf3ac5b2d3feba))
* implement delete guestbook entry ([b7f823a](https://github.com/ixartz/Next-js-Boilerplate/commit/b7f823a83435856ac32aea90da8317926e5b2b8b))
* improve UI for AddGuestbookForm ([153abfc](https://github.com/ixartz/Next-js-Boilerplate/commit/153abfc0e2f10a5aa59e24af8f0ef76667041578))
* insert in guestbook and retrieve all guestbooks ([23ee408](https://github.com/ixartz/Next-js-Boilerplate/commit/23ee4086a8c2166bdd6fe82b1cb50cc286793bb3))
* make guestbook editable ([8ec1406](https://github.com/ixartz/Next-js-Boilerplate/commit/8ec14066a966c76b02bf5552ec2f4f348048a45c))
* remove notnull in schema.ts ([10f4943](https://github.com/ixartz/Next-js-Boilerplate/commit/10f49434999ba0a884a72e640c67dc955bf7eedd))
* rename from email to username ([52ab0e4](https://github.com/ixartz/Next-js-Boilerplate/commit/52ab0e4f86b20ace52cbb6ce421f85357c0dfa6e))
* replace new-router page by guestbook ([efc84e6](https://github.com/ixartz/Next-js-Boilerplate/commit/efc84e607d23981dba07b931ff078776aa9693b5))
* replace with a working URL for the database to avoid timeout ([fecd8a5](https://github.com/ixartz/Next-js-Boilerplate/commit/fecd8a5d66934af774fde12759f8079cabfb382b))
* update dotenv path to .env, the file was renamed ([bd9b2c9](https://github.com/ixartz/Next-js-Boilerplate/commit/bd9b2c9efd12a0b54125ac352c43aab9d31c7c99))
* use local SQLite file ([fe52801](https://github.com/ixartz/Next-js-Boilerplate/commit/fe528010cf2d867fcbbc53156ae7fa6c862a88f4))
* validate t3 env on build ([6d448ed](https://github.com/ixartz/Next-js-Boilerplate/commit/6d448ed0fdea51952c8bfeaf4ce948cf9365675c))

# [3.15.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.14.1...v3.15.0) (2023-08-10)


### Features

* add next.js middleware with Clerk ([2f4a1d3](https://github.com/ixartz/Next-js-Boilerplate/commit/2f4a1d3e394eb835b011a13289f156a91993d782))
* add sign in and sign up link in index page ([4489085](https://github.com/ixartz/Next-js-Boilerplate/commit/4489085e8deb0ae1836a3741657f8331af6294ca))
* add sign in and sign up page ([f021f71](https://github.com/ixartz/Next-js-Boilerplate/commit/f021f71f755e3af3cb789d0330ad2a0237ec600d))
* add sign out button in dashboard ([c663d1c](https://github.com/ixartz/Next-js-Boilerplate/commit/c663d1c4799869faf2a2c549669521409f192830))
* add user profile to manage account ([470731b](https://github.com/ixartz/Next-js-Boilerplate/commit/470731ba960dfdd0aa57f66affde28b0226d5d42))
* add user profile to manage account ([581efbe](https://github.com/ixartz/Next-js-Boilerplate/commit/581efbef51cf700f9bbe94f268ff99639f5e49da))
* implement hello component by display user email address ([7047985](https://github.com/ixartz/Next-js-Boilerplate/commit/7047985ffbce9a986e7308040928783395cf7b68))
* implement sign out button ([8588834](https://github.com/ixartz/Next-js-Boilerplate/commit/8588834b5f1a53c51835d7aba5a4c9f1230c1bf7))
* implement sign out button and redirect to sign in page when logging out ([45ed137](https://github.com/ixartz/Next-js-Boilerplate/commit/45ed137d5c4e292ac8329f0661cb38fc29812927))
* redirect to dashboard when the user is signed in for sign up and sign in page ([629a033](https://github.com/ixartz/Next-js-Boilerplate/commit/629a03363af310e5411fea4cb554b53e72701e7d))

## [3.14.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.14.0...v3.14.1) (2023-08-07)


### Bug Fixes

* resolve sourcemap error with Cypress and TypeScript 5 ([54a5100](https://github.com/ixartz/Next-js-Boilerplate/commit/54a51004d6e22860eb1c6aad4ff689fac46bd0b4))

# [3.14.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.13.0...v3.14.0) (2023-08-03)


### Features

* use Next.js custom TypeScript plugin ([915e193](https://github.com/ixartz/Next-js-Boilerplate/commit/915e193f8037d36e9779fe7464a4d6c1685b3a94))

# [3.13.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.12.0...v3.13.0) (2023-08-02)


### Features

* add app routed pages ([9cc79a0](https://github.com/ixartz/Next-js-Boilerplate/commit/9cc79a00647b0a4ce64f66da4a430ec2c4972367)), closes [#64](https://github.com/ixartz/Next-js-Boilerplate/issues/64)
* add sitemap support app router ([b82e566](https://github.com/ixartz/Next-js-Boilerplate/commit/b82e566fb43d63329ef4507870494e554dea0e6a))
* app router doesn't support next export, use output: export ([76aa9cd](https://github.com/ixartz/Next-js-Boilerplate/commit/76aa9cd0597ad06fd0f0160ad6119a25b87d3336))
* generate statically portfolio pages ([1f1bf31](https://github.com/ixartz/Next-js-Boilerplate/commit/1f1bf3143215ab19d19cd4f13e4048b0ee84073c))
* update test for new router page ([b695666](https://github.com/ixartz/Next-js-Boilerplate/commit/b695666fd41c9ddf1886e9b5e3c7cc43b616820c))

# [3.12.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.11.0...v3.12.0) (2023-07-13)


### Features

* format code to respect prettier ([48b6a49](https://github.com/ixartz/Next-js-Boilerplate/commit/48b6a49fd204083deb94b01aab70b52a42b9593f))
* resolve conflict between airbnb-hook and next/core-web-vitals about react hooks ([5e0be4f](https://github.com/ixartz/Next-js-Boilerplate/commit/5e0be4fd8c2f9acd895f0b9ce373af7d782d44df))
* update to the latest dependencies version ([d93fd83](https://github.com/ixartz/Next-js-Boilerplate/commit/d93fd83b6ab93360ddd8489afc8cfb05603e504c))


### Reverts

* use older TypeScript to avoid e2e compilation with sourcemap ([6377d2f](https://github.com/ixartz/Next-js-Boilerplate/commit/6377d2f2efc71384fba236427086b4e75f189328))

# [3.11.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.10.1...v3.11.0) (2023-06-07)


### Features

* update dependencies to the latest version ([b7609de](https://github.com/ixartz/Next-js-Boilerplate/commit/b7609dea1c8bd49f6ac05439740ea78894cd4a79))

## [3.10.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.10.0...v3.10.1) (2023-05-29)


### Bug Fixes

* added types ([b35ddc9](https://github.com/ixartz/Next-js-Boilerplate/commit/b35ddc91ecad81986432dce1ba84c302e6394a5b))

# [3.10.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.9.0...v3.10.0) (2023-04-26)


### Features

* add vscode yoavbls.pretty-ts-errors extension ([3588ce1](https://github.com/ixartz/Next-js-Boilerplate/commit/3588ce1dd366ebaa69f97551be58528d1ae38457))
* remove stories in the coverage from Jest ([d502869](https://github.com/ixartz/Next-js-Boilerplate/commit/d502869a08a0b1d9025a4ce582651c5353f29d59))
* use default airbnb instead of the base version ([5c05116](https://github.com/ixartz/Next-js-Boilerplate/commit/5c05116fb777aee09c1af7df6694e54403eaaccb))

# [3.9.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.2...v3.9.0) (2023-04-05)


### Features

* add storybook into project ([51f3748](https://github.com/ixartz/Next-js-Boilerplate/commit/51f3748c0cb6d9cd04cdb0d3b9d95a0f60851866))
* add tailwind css support in Storybook ([5e0d287](https://github.com/ixartz/Next-js-Boilerplate/commit/5e0d287cef8a898df8f1a98632a8703657282100))
* remove warning for no extreneous deps in stories ([b243d44](https://github.com/ixartz/Next-js-Boilerplate/commit/b243d441e4b75566e16f5fa64d26900267eb89f5))


### Reverts

* remove storybook addon-styling which is not needed ([e863fed](https://github.com/ixartz/Next-js-Boilerplate/commit/e863fedcbc5a1aaf808c295d80f8de95b6abd1f7))

## [3.8.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.1...v3.8.2) (2023-03-28)


### Bug Fixes

* error generated by eslint-plugin-cypress ([7562c6b](https://github.com/ixartz/Next-js-Boilerplate/commit/7562c6bddb31e6941aee7e4e2bbcdabf5be3bddf))

## [3.8.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.8.0...v3.8.1) (2023-03-16)


### Bug Fixes

* typo in Readme ([8f7c1b7](https://github.com/ixartz/Next-js-Boilerplate/commit/8f7c1b79a46406b04b90ed8a5fe5029b3c24ff8c))

# [3.8.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.7.0...v3.8.0) (2023-03-02)


### Features

* fix heading levels increase by one ([e712e60](https://github.com/ixartz/Next-js-Boilerplate/commit/e712e60402f04033673d93e464d7b3c46fff7dbe))

# [3.7.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.6.0...v3.7.0) (2023-02-05)


### Features

* improve accessibility ([aa0f0b1](https://github.com/ixartz/Next-js-Boilerplate/commit/aa0f0b12085e31f13574fc9f4349157102d4467b))


### Reverts

* add support for all Node.js 14+, too restrictive with only Node.js 18+ ([4e27540](https://github.com/ixartz/Next-js-Boilerplate/commit/4e27540f638d4767fb60b612519669ad6bf69367))
* downgrade semantic-release version to 19 ([26d5a6e](https://github.com/ixartz/Next-js-Boilerplate/commit/26d5a6ebe2fc4fe59fef40779e132ccf1f31c09f))

# [3.6.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.4...v3.6.0) (2022-12-03)


### Bug Fixes

* add npx before percy command line ([4824e98](https://github.com/ixartz/Next-js-Boilerplate/commit/4824e98a4d621684494fe2c7e8c3351551e52845))
* retrive PERCY_TOKEN and set token for percy cli ([afe00f2](https://github.com/ixartz/Next-js-Boilerplate/commit/afe00f2e47b5dbc5fb701dd2e46756f4b7e498fd))
* wait until the link rendered instead a wrong heading tag ([e38655b](https://github.com/ixartz/Next-js-Boilerplate/commit/e38655b853b39fdcb9bccd3a84e99dd5caa1681d))


### Features

* add visual testing with Percy ([b0a39f5](https://github.com/ixartz/Next-js-Boilerplate/commit/b0a39f58e1bd0934158b0bab8ab7e4c9215e88f0))

## [3.5.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.3...v3.5.4) (2022-12-03)


### Bug Fixes

* change matching regex for Cypress files ([861d545](https://github.com/ixartz/Next-js-Boilerplate/commit/861d54596b61b7706cfbb681df334d73b34a378e))

## [3.5.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.2...v3.5.3) (2022-12-02)


### Bug Fixes

* resolve merge conflict ([276f57a](https://github.com/ixartz/Next-js-Boilerplate/commit/276f57aeb0d4a346f8e19ad81ce4703458d9f41c))

## [3.5.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.1...v3.5.2) (2022-12-02)


### Bug Fixes

* use npx npm-check-updates ([e530193](https://github.com/ixartz/Next-js-Boilerplate/commit/e5301939a5ff98c598899ff49bee1ad351759292))

## [3.5.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.5.0...v3.5.1) (2022-12-02)


### Bug Fixes

* add steps in update-deps.yml file, syntax error ([b5de445](https://github.com/ixartz/Next-js-Boilerplate/commit/b5de445f1f927a5a7c2b0c85746b8fd07629cb55))

# [3.5.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.4.0...v3.5.0) (2022-12-02)


### Features

* add auto-update GitHub Actions ([364168f](https://github.com/ixartz/Next-js-Boilerplate/commit/364168f3407c7cdd21da7cd1de6d9d930f89d99a))

# [3.4.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.3.0...v3.4.0) (2022-12-02)


### Features

* automatically format the whole codebase with npm run format ([9299209](https://github.com/ixartz/Next-js-Boilerplate/commit/92992096ede4d2b3e77c3e0c96b75e5e6b84067d))
* update footer message and comment ([4f74176](https://github.com/ixartz/Next-js-Boilerplate/commit/4f74176b05528666fd8b92a8becdc7e3c2f0db4a))

# [3.3.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.4...v3.3.0) (2022-11-22)


### Features

* change 'powered by' text to 'built' with ([fe0a29f](https://github.com/ixartz/Next-js-Boilerplate/commit/fe0a29f8fbab14c7e8c8e98a75ce488ac157e509))

## [3.2.4](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.3...v3.2.4) (2022-11-20)


### Bug Fixes

* update README file for next-sitemap ([9496217](https://github.com/ixartz/Next-js-Boilerplate/commit/94962171a35a07e84319374500f28a76f264a266))

## [3.2.3](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.2...v3.2.3) (2022-11-20)


### Bug Fixes

* add sitemap file in gitignore, it shouldn't commit to git ([344b731](https://github.com/ixartz/Next-js-Boilerplate/commit/344b7312df2f7e12e642a6346ef05ad9a7ca766c))

## [3.2.2](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.1...v3.2.2) (2022-11-20)


### Bug Fixes

* rename from mjs to js next-sitemap file ([7d450ff](https://github.com/ixartz/Next-js-Boilerplate/commit/7d450ffce77f0be4c533cb1aab757f7fb1f13596))

## [3.2.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.2.0...v3.2.1) (2022-11-20)


### Bug Fixes

* code styling in blog component pages ([f4a55c4](https://github.com/ixartz/Next-js-Boilerplate/commit/f4a55c4234fc03ed719859c12f13bffabd120c6d))
* move getStaticPaths at the top of blog page ([83892ea](https://github.com/ixartz/Next-js-Boilerplate/commit/83892ea865459f59da824c9358fbf4ccea6475e6))
* remove generated files by next-sitemap ([c5d93bf](https://github.com/ixartz/Next-js-Boilerplate/commit/c5d93bf9fe67a6737b536edf4d50d56cd4c8af2c))

# [3.2.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.1.0...v3.2.0) (2022-11-19)


### Features

* run github release only on completed CI workflow ([dd4de76](https://github.com/ixartz/Next-js-Boilerplate/commit/dd4de76b6ea013190a6ea18d69eb3764e1b915f9))

# [3.1.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v3.0.0...v3.1.0) (2022-11-19)


### Bug Fixes

* just rebuild sitemap ([831bae9](https://github.com/ixartz/Next-js-Boilerplate/commit/831bae93831eb5c4f259c4a0fa9ec3012ede8927))


### Features

* add blog page ([89c4ec7](https://github.com/ixartz/Next-js-Boilerplate/commit/89c4ec79db48f4ae09af3e8ddb3ce5a980ed8ee6))
* add sitemap.xml and robots.txt from build ([545d133](https://github.com/ixartz/Next-js-Boilerplate/commit/545d133decee4f7d42c228049ef3bde2b9a94b0a))
* disable Husky for release ([f20c595](https://github.com/ixartz/Next-js-Boilerplate/commit/f20c5951e018c99421e833eef6ce14bd9632838f))
* rename from master to main ([10920ec](https://github.com/ixartz/Next-js-Boilerplate/commit/10920ece4892ca73639388116af59fdd3e077d5f))
* update TypeScript to 4.9.x ([471dc70](https://github.com/ixartz/Next-js-Boilerplate/commit/471dc70306c69ecb524af40aa76403daa83597e2))

# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [3.0.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.1.1...v3.0.0) (2022-10-26)


### ⚠ BREAKING CHANGES

* update to Next.js 13 and Tailwind CSS 3.2

### Features

* add commit script in package.json ([8f4719e](https://github.com/ixartz/Next-js-Boilerplate/commit/8f4719ec550ab0dbffa93ca1d278aa9e370a773a))


### Bug Fixes

* Eslint comment update ([8baa5d1](https://github.com/ixartz/Next-js-Boilerplate/commit/8baa5d160734a3cadb419534509cc6edaac57456))


* update to Next.js 13 and Tailwind CSS 3.2 ([fc9f2c1](https://github.com/ixartz/Next-js-Boilerplate/commit/fc9f2c1cf914c15b36cdf881306d20b405a259e8))

### [2.1.1](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.1.0...v2.1.1) (2022-09-08)

## [2.1.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v2.0.0...v2.1.0) (2022-07-08)


### Features

* add cypress and cypress eslint plugin ([5657ee6](https://github.com/ixartz/Next-js-Boilerplate/commit/5657ee6dab03b11020bb2ce80083669785edd6ce))

## [2.0.0](https://github.com/ixartz/Next-js-Boilerplate/compare/v1.1.0...v2.0.0) (2022-07-03)


### ⚠ BREAKING CHANGES

* add Jest and React testing library
* to React 18

### Features

* add coverage for vscode-jest and configure jest autoRun ([ad8a030](https://github.com/ixartz/Next-js-Boilerplate/commit/ad8a03019010577bfb8e8ed850e8d45ca274dbe9))
* add Jest and React testing library ([e182b87](https://github.com/ixartz/Next-js-Boilerplate/commit/e182b87db5943abbe706568e77285e1eb6bddf5e))
* add TypeScript support for Tailwind CSS configuration ([41f1918](https://github.com/ixartz/Next-js-Boilerplate/commit/41f19189655abe3941485363e057812a5fcd6c02))
* add vscode jest extension ([49ab935](https://github.com/ixartz/Next-js-Boilerplate/commit/49ab935a03f5a9d1074a155331107737fd7dad13))


* to React 18 ([c78f215](https://github.com/ixartz/Next-js-Boilerplate/commit/c78f2152a978a39b2c6d381427df8e8ad2a30099))

## 1.1.0 (2022-04-25)


### Features

* add commitlint with config-conventional ([97a9ac7](https://github.com/ixartz/Next-js-Boilerplate/commit/97a9ac7dbbca3f8d4fad22a9e4a481c029cd2cb5))


### Bug Fixes

* add missing files for commitzen ([018ba8b](https://github.com/ixartz/Next-js-Boilerplate/commit/018ba8bde81b0f6cc60230fe4668b149ac3b2e6a))
* update package-lock.json ([fba016d](https://github.com/ixartz/Next-js-Boilerplate/commit/fba016dec202d5748e30804b1bf50e30c00ef120))
