'use client';

import '@/styles/global.scss';
import '@/styles/fonts.scss';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';

import AuthenticateRoutes from '@/authenticateRoutes';
import { Alert } from '@/components/ui/Alert';
import { Notification } from '@/components/ui/Notification';
import TourComponent from '@/components/ui/Tour';
import { tourStepsAdmin, tourStepsCFO } from '@/constants/tout';
import { LocalService } from '@/service/local.service';
import ReduxProvider from '@/store/ReduxProvider';

export default function RootLayout({
  // Layouts must accept a children prop.
  // This will be populated with nested layouts or pages
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const localService = new LocalService();
  const pathname = usePathname();
  const currentUser: any = localService.getItem('user_type') || '';

  useEffect(() => {
    const currentRoute: any = pathname;
    const token: any = localService.getItem('access_token') || '';
    
    if (
      currentUser === 'CFO' &&
      currentRoute !== '/home' &&
      currentRoute !== '/insight'
    ) {
      router.replace('/home');
    }
    
    if (currentRoute === '/login') {
      if (token !== '') {
        // Always redirect to home for all authenticated users
        router.replace('/home');
      }
    }
  }, [pathname]);

  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          <AuthenticateRoutes />
          {children}
          <Alert />
          <Notification />
          <TourComponent
            steps={currentUser === 'CFO' ? tourStepsCFO : tourStepsAdmin}
          />
        </ReduxProvider>
      </body>
    </html>
  );
}
