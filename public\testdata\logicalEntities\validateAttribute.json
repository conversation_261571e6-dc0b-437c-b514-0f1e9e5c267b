[{"id": "RAW_SAP_NA", "selected": true, "components": [{"value": "RAW_SAP_NA", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "LFB1", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "VENDOR_CODE", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "10", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_CODE", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}, {"id": "RAW_SAP_NA", "selected": true, "components": [{"value": "RAW_SAP_NA", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "LFA1", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "VENDOR_NAME", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "45", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_NAME ", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}, {"id": "RAW_SAP_NA", "selected": true, "components": [{"value": "RAW_SAP_NA", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "LFB1", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "VENDOR_COMPANY_CODE", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "04", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_COMPANY_CODE", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}, {"id": "RAW_ORA_PROD", "selected": true, "components": [{"value": "RAW_ORA_PROD", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "AP_SUPPLIERS", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "SEGMENT1", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "10", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_NAME", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}, {"id": "RAW_ORA_PROD", "selected": true, "components": [{"value": "RAW_ORA_PROD", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "AP_SUPPLIERS", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "VENDOR_NAME", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "45", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype 05", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_NAME", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}, {"id": "RAW_ORA_PROD", "selected": true, "components": [{"value": "RAW_ORA_PROD", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "GL_CODE_COMBINATIONS", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "SEGMENT1", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "INT", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "10", "type": "text", "disabled": false, "userAvatarLink": null}, {"value": "Int", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Int"}, {"name": "String"}, {"name": "boolean"}]}, {"value": "VENDOR_COMPANY_CODE", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Test Description"}]}]