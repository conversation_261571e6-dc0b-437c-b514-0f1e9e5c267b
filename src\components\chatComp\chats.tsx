/* eslint-disable react/no-danger */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable func-names */
/* eslint-disable react/no-array-index-key */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

'use client';

/* eslint-disable tailwindcss/no-custom-classname */
import React, { useEffect, useRef, useState } from 'react';
import Markdown from 'react-markdown';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { Tooltip } from 'react-tooltip';

import { FeedBackModal } from '../ui/FeedbackModal';
import { Tabs } from '../ui/Tabs';
import ChatGraph from './chatGraph';
import ChatTable from './chatTable';

interface ChatProps {
  onFeedback?: (feedback: any) => void;
  chatData?: any;
  isLoading?: any;
  isChatModalExpanded?: boolean;
  userName?: string;
  sendMessage?: string;
  setSendMessage?: React.Dispatch<React.SetStateAction<string>>;
  apiService?: any;
}

const Chats: React.FC<ChatProps> = ({
  isChatModalExpanded,
  chatData,
  isLoading,
  onFeedback,
}) => {
  const isCodeViewVisible = false;
  const [isSidePannelOpen, setIsSidePannelOpen] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [code, setCode] = useState<string>('');
  const [like, setLike] = useState<any>([]);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState<any>(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const getTabData: any = (chartData: any, tableData: any) => {
    const tabData: any[] = [];
    if (tableData) {
      tabData.push({
        title: 'Table',
        component: <ChatTable tableData={tableData} />,
      });
    }
    if (chartData) {
      tabData.push({
        title: 'Chart',
        component: <ChatGraph data={chartData} />,
      });
    }

    return tabData;
  };

  const sendFeedback = (feedback: any) => {
    if (onFeedback) {
      onFeedback(feedback);
    }
  };

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chatData]);
  return (
    <div className="relative flex size-full flex-row bg-white-200 ">
      <div
        className="flex size-full flex-col space-y-4 overflow-y-auto bg-white-200 px-4 pb-4 pt-1"
        ref={chatContainerRef}
      >
        {chatData?.map((chat: any, index: number) => (
          <div
            key={index}
            className={`flex ${
              chat.name === 'Archimedes' ? 'justify-start' : 'justify-end'
            }`}
          >
            <div className="flex flex-col space-y-1">
              <span
                className={`font-sans text-[12px] font-semibold capitalize text-gray-400 ${
                  chat.name === 'Archimedes' ? 'text-left' : 'text-right'
                }`}
              >
                {chat.name}
              </span>
              <div
                className={`relative flex flex-col space-y-2 rounded bg-gray-300 px-2 pb-2 pt-1 ${
                  chat.name === 'Archimedes' && chat.type !== 'chart'
                    ? 'w-4/5'
                    : 'w-full'
                }`}
              >
                {chat.name === 'Archimedes' &&
                  chat.message !==
                    'Thank you for taking the time to share your feedback, your insights are incredibly valuable to us.' && (
                    <div className=" absolute -left-[2px] -top-[22px] flex w-full flex-row justify-end space-x-2">
                      {isCodeViewVisible && chat.code && (
                        <div className="mr-[2px] w-[25px] border-r border-lightgray-100">
                          <img
                            onClick={() => {
                              setCode(chat.code);
                              if (chat.code) {
                                setIsSidePannelOpen(true);
                              }
                            }}
                            src="/assets/images/copy.svg"
                            className="size-[15px] cursor-pointer"
                            alt="copy"
                          />
                        </div>
                      )}
                      {like[index]?.like ? (
                        <img
                          onClick={() => {
                            setLike((prevState: any) => {
                              const newData = [...prevState];
                              newData[index] = {
                                ...newData[index],
                                like: false,
                                dislike: false,
                              };
                              return newData;
                            });
                          }}
                          src="/assets/images/like-fill.svg"
                          className="mx-[2px] size-[15px] cursor-pointer"
                          alt="like"
                        />
                      ) : (
                        <img
                          onClick={() => {
                            setLike((prevState: any) => {
                              const newData = [...prevState];
                              newData[index] = {
                                ...newData[index],
                                like: true,
                                dislike: false,
                              };
                              return newData;
                            });
                          }}
                          src="/assets/images/like.svg"
                          className="mx-[2px] size-[15px] cursor-pointer"
                          alt="like"
                        />
                      )}

                      {like[index]?.dislike ? (
                        <img
                          src="/assets/images/like-fill.svg"
                          className="mx-[2px] size-[15px] rotate-180  cursor-pointer"
                          alt="like"
                          onClick={() => {
                            setLike((prevState: any) => {
                              const newData = [...prevState];
                              newData[index] = {
                                ...newData[index],
                                like: false,
                                dislike: false,
                              };
                              return newData;
                            });
                          }}
                        />
                      ) : (
                        <img
                          src="/assets/images/like.svg"
                          className="mx-[2px] size-[15px] rotate-180  cursor-pointer"
                          alt="like"
                          onClick={() => {
                            setLike((prevState: any) => {
                              const newData = [...prevState];
                              newData[index] = {
                                ...newData[index],
                                like: false,
                                dislike: true,
                              };
                              return newData;
                            });
                            setIsFeedbackOpen(true);
                          }}
                        />
                      )}
                    </div>
                  )}

                <span className="font-sans text-[12px] font-semibold text-gray-400">
                  <pre className="text-pretty">
                    <Markdown>{chat.message}</Markdown>
                  </pre>
                </span>
                <div>
                  {getTabData(chat?.chartData, chat?.table).length > 0 && (
                    <div
                      className={`relative flex ${
                        isChatModalExpanded ? 'w-[70vw]' : 'w-[50vw]'
                      }  flex-col rounded bg-white-200 p-2`}
                    >
                      {/* <div className="absolute right-4 top-4">
                        <img
                          onClick={() => {}}
                          src="/assets/images/copy.svg"
                          className="h-5 w-5 cursor-pointer"
                          alt="copy"
                          data-tooltip-id="table-tooltip"
                          data-tooltip-content={"Copy To Clipboard"}
                        />
                      </div> */}
                      <Tabs
                        data={getTabData(chat?.chartData, chat?.table)}
                        selectedIndex={selectedTab}
                        onChange={(e: number) => {
                          setSelectedTab(e);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="loading-span show absolute bottom-1 left-4 bg-transparent p-1">
            {' '}
            <span className=" font-sans text-[12px] font-semibold capitalize text-gray-400 ">
              {isLoading}{' '}
            </span>
          </div>
        )}
        <Tooltip
          className="custom-tooltip"
          id="table-tooltip"
          place="left"
          variant="info"
          positionStrategy="fixed"
        />
        <FeedBackModal
          isOpen={isFeedbackOpen}
          sendFeedback={sendFeedback}
          removePannelBorder
          closeModal={() => {
            setIsFeedbackOpen(false);
          }}
          panelWidth="w-[40vw] h-[50vh]"
          isActionButtonVisible
          actionbuttonText="Save"
        />
      </div>
      {isSidePannelOpen && (
        <div className="absolute right-0 z-50 flex h-full w-3/5 flex-col space-y-2 bg-white-200 p-2 shadow-sidebar">
          <div className="flex  flex-row items-center justify-between border-b border-lightgray-100 p-1">
            <span className="font-sans text-sm font-semibold text-gray-400 ">
              Abstract Code
            </span>
            <img
              onClick={() => {
                setCode('');
                setIsSidePannelOpen(false);
              }}
              src="/assets/images/cross-gray.svg"
              className="size-4 cursor-pointer"
              alt="close"
            />
          </div>

          <div className="size-full overflow-auto">
            <SyntaxHighlighter
              className="h-full text-xs"
              height="full"
              language="javascript"
              style={docco}
            >
              {code}
            </SyntaxHighlighter>
          </div>
        </div>
      )}
    </div>
  );
};
export default Chats;
