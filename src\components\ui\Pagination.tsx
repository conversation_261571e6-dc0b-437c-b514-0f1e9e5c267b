import React from 'react';

const Pagination: React.FC<{
  itemsPerPage: any;
  totalItems: any;
  currentPage: any;
  paginate: any;
}> = ({ itemsPerPage, totalItems, currentPage, paginate }) => {
  const pageNumbers: number[] = [];
  for (let i = 1; i <= Math.ceil(totalItems / itemsPerPage); i += 1) {
    pageNumbers.push(i);
  }

  return (
    <nav
      className={`items-center justify-center space-x-1 bg-white-200 ${
        pageNumbers.length === 0 ? 'hidden' : 'flex'
      }`}
    >
      <button
        type="button"
        onClick={() => {
          if (currentPage !== 0) {
            paginate(currentPage - 1);
          }
        }}
        className={`border-none bg-transparent outline-none focus:outline-none ${
          currentPage === 1 ? '' : 'cursor-pointer'
        }`}
        disabled={currentPage === 1}
      >
        {currentPage !== 1 ? (
          <img
            className="h-[24px] w-[24px] cursor-pointer"
            src="/assets/images/arrow-blue.svg"
            alt="back"
          />
        ) : (
          <img
            className="h-[24px] w-[24px]"
            src="/assets/images/arrow-grey.svg"
            alt="back"
          />
        )}
      </button>
      <span className="font-sans text-sm font-semibold text-gray-500">
        Page {currentPage} of{' '}
        {pageNumbers.length === 0 ? 1 : pageNumbers.length}
      </span>
      <button
        type="button"
        onClick={() => {
          if (currentPage < pageNumbers.length) {
            paginate(currentPage + 1);
          }
        }}
        className={` border-none bg-transparent outline-none focus:outline-none ${
          currentPage === pageNumbers.length || pageNumbers.length === 0
            ? ''
            : 'cursor-pointer'
        }`}
        disabled={
          currentPage === pageNumbers.length || pageNumbers.length === 0
        }
      >
        {currentPage !== pageNumbers.length && pageNumbers.length !== 0 ? (
          <img
            className="h-[24px] w-[24px] rotate-180 cursor-pointer"
            src="/assets/images/arrow-blue.svg"
            alt="back"
          />
        ) : (
          <img
            className="h-[24px] w-[24px] rotate-180"
            src="/assets/images/arrow-grey.svg"
            alt="back"
          />
        )}
      </button>
    </nav>
  );
};

export default Pagination;
