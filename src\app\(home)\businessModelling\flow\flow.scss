.col {
  display: flex;
  align-items: center;
  justify-content: start;
  font-size: 6px;
  border: none;
  color: #474D67;

  // background-color: rgba(47, 128, 237,0.1);
  .react-flow__handle {
    opacity: 0;
  }

  &.visible-handles {
    .react-flow__handle {
      opacity: 1;
    }
  }

  &.visible-left-handle {
    .react-flow__handle-left {
      opacity: 1;
    }
  }

  &.visible-right-handle {
    .react-flow__handle-right {
      opacity: 1;
    }
  }

}



.highlighted-col {
  border: 1px solid #2F80ED;
  z-index: 1 !important;
}

.react-flow__handle {
  margin: auto;
  background: #2F80ED;
  border-radius: 15px;
  border: 2px solid #2F80ED;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 3px 0px, rgba(0, 0, 0, 0.14) 0px 1px 1px 0px, rgba(0, 0, 0, 0.12) 0px 2px 1px -1px;
}

.table {
  border: 0px solid rgb(187, 187, 187);
}

.react-flow__node-input,
.react-flow__node-default,
.react-flow__node-output {
  padding: 0;
}