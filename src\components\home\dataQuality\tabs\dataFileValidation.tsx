/* eslint-disable jsx-a11y/anchor-is-valid */
import moment from 'moment';
import { useRouter } from 'next/navigation';
import dataFileValidationTables from 'public/testdata/dataQuality/datafileValidation.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Radio } from '@/components/ui/Radio';
import SimpleTable from '@/components/ui/SimpleTable';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

const DataFileValidation: React.FC<{ selectedYear?: any }> = ({
  selectedYear,
}) => {
  const localService = new LocalService();
  const [selectedYearForView, setSelectedYearForView] =
    useState<any>(selectedYear);
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const [tableData, setTableData] = useState<any>(null);
  const [selectedRecon, setelectedRecon] = useState<string>(
    localService.getItem('selectedFileValidationStatus') ||
      'FILE_PROCESSING_FAILED',
  );

  const formatTableData = (data: any) => {
    return data.map((item: any) => {
      return {
        'Data File': {
          id: item.fileId,
          fileDetail: item,
          data: item.dataFile,
          isLink: true,
        },
        Dataset: {
          data: item.dataset,
        },
        'Business Segment': {
          data: item.businessSegment,
        },
        Month: {
          data: Number(item.month) > 0 ? Number(item.month) : 'N/A',
        },
        Quarter: {
          data: Number(item.quarter) > 0 ? Number(item.quarter) : 'N/A',
        },
        Year: {
          data: Number(item.year) > 0 ? Number(item.year) : 'N/A',
        },
        'Upload date/time': {
          data: item.uploadedAt
            ? moment(item.uploadedAt).format('YYYY-MM-DD')
            : '',
        },
        'Times Replaced': {
          data: item.timesReplaced ? item.timesReplaced : '',
        },
        'Reason for failure': {
          data: item.reasonOfFailure ? item.reasonOfFailure : '',
        },
      };
    });
  };

  const fetchDataValidationTableData = async (selectedStatus: string) => {
    try {
      const payload = {
        status: selectedStatus,
      };
      const url = `
      ${ApiUtilities.getApiServerUrlBsm}${ApiUtilities.apiPath.getDataValidationTableData.url}${selectedYearForView} 
      `;
      dispatch(setIsLoading(true));
      apiService
        .postRequest(url, payload)
        .then((res) => {
          if (res.status === 200) {
            let header: any = [];
            if (selectedRecon === 'FILE_PROCESSING_FAILED') {
              header = [
                { data: 'Data File' },
                { data: 'Dataset' },
                { data: 'Business Segment' },
                { data: 'Month' },
                { data: 'Quarter' },
                { data: 'Year' },
                { data: 'Reason for failure' },
              ];
              const filteredItem: any = res.data.filter(
                (item: any) => item.fixed === false,
              );
              const tempTableData = {
                tableData: {
                  headers: header,
                  rows: formatTableData(filteredItem),
                },
              };
              setTableData(tempTableData);
            } else {
              header = [
                { data: 'Data File' },
                { data: 'Dataset' },
                { data: 'Business Segment' },
                { data: 'Month' },
                { data: 'Quarter' },
                { data: 'Year' },
                { data: 'Upload date/time' },
                { data: 'Times Replaced' },
              ];
              const tempTableData = {
                tableData: {
                  headers: header,
                  rows: formatTableData(res.data),
                },
              };
              setTableData(tempTableData);
            }
          }
        })
        .catch((error) => {
          console.error('Error fetching data:', error);
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  useEffect(() => {
    setSelectedYearForView(selectedYear);
  }, [selectedYear]);

  useEffect(() => {
    if (!isTourOpen) {
      if (selectedRecon) {
        fetchDataValidationTableData(selectedRecon);
      }
    } else {
      setTableData(dataFileValidationTables);
    }
  }, [selectedYearForView, selectedRecon]);

  return (
    <div className="flex h-[calc(100vh-250px)] w-full flex-col space-y-4 overflow-auto px-2 pt-3 xl:h-[calc(100vh-200px)]">
      <div className="mt-2 flex w-full flex-row items-center justify-between ">
        <div className="flex flex-row items-center space-x-2">
          <div className="min-w-[200px]">
            <Radio
              label="File processing failures"
              name="reconType"
              value="FILE_PROCESSING_FAILED"
              checked={selectedRecon === 'FILE_PROCESSING_FAILED'}
              id="selectedRecon"
              onChange={(event: any) => {
                if (event.target.checked) {
                  setelectedRecon('FILE_PROCESSING_FAILED');
                  localService.setItem(
                    'selectedFileValidationStatus',
                    'FILE_PROCESSING_FAILED',
                  );
                }
              }}
            />
          </div>
          <div className="w-full">
            <Radio
              label="Files successfully processed"
              name="reconType"
              value="FILE_PROCESSING_SUCCEEDED"
              checked={selectedRecon === 'FILE_PROCESSING_SUCCEEDED'}
              id="selectedRecon"
              onChange={(event: any) => {
                if (event.target.checked) {
                  setelectedRecon('FILE_PROCESSING_SUCCEEDED');
                  localService.setItem(
                    'selectedFileValidationStatus',
                    'FILE_PROCESSING_SUCCEEDED',
                  );
                }
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex h-full w-full flex-row ">
        {tableData && (
          <div className="h-full w-full">
            <SimpleTable
              isValidationTable
              hasPagination={tableData?.tableData?.rows.length > 10}
              isAvailabilityTable
              headers={tableData?.tableData?.headers}
              tableRows={tableData?.tableData?.rows}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DataFileValidation;
