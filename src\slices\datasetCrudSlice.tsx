import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface datasetCrudState {
  datasetTableResponseFetched: boolean;
  datasets: any;
  selectedDataset: any;
}

const initialState: datasetCrudState = {
  datasetTableResponseFetched: false,
  datasets: [],
  selectedDataset: null,
};

export const datasetCrudSlice = createSlice({
  name: 'datasetCrud',
  initialState,
  reducers: {
    setDatasetTableResponseFetched: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.datasetTableResponseFetched = action.payload;
    },
    setDatasets: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.datasets = action.payload;
    },
    setSelectedDataset: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.selectedDataset = action.payload;
    },
    resetState: () => {
      return initialState;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setDatasets,
  setSelectedDataset,
  resetState,
  setDatasetTableResponseFetched,
} = datasetCrudSlice.actions;

export default datasetCrudSlice.reducer;
