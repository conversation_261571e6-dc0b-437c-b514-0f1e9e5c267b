'use client';

import { Formik } from 'formik';
import { useRouter } from 'next/navigation';
import React from 'react';

import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Link } from '../ui/Link';

const Register: React.FC = () => {
  // Essentials
  const router = useRouter();
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div className="flex min-h-[30vh] w-[30vw] flex-col">
        <div className="flex flex-col space-y-1">
          <span className="font-sans text-6xl font-normal leading-[70.81px] text-gray-800">
            Register
          </span>
          <span className="font-sans text-base font-normal leading-5 text-gray-900">
            Create an account to access AstRai
          </span>
        </div>
        <div className="mt-4 flex flex-col space-y-3">
          <button
            type="button"
            className="flex h-[54px] w-[30vw] items-center justify-center rounded-md border-[1px] border-lightgray-50 hover:border-purple-200 focus:border-purple-200 focus:shadow-blue"
          >
            <div className="flex flex-row items-center justify-center space-x-3">
              <img src="/assets/images/google.svg" alt="google" />

              <span className="font-sans text-base font-normal leading-5 text-gray-800">
                Login with Google
              </span>
            </div>
          </button>
          <button
            type="button"
            className="flex h-[54px] w-[30vw] items-center justify-center rounded-md border-[1px] border-lightgray-50 hover:border-purple-200 focus:border-purple-200 focus:shadow-blue"
          >
            <div className="flex flex-row items-center justify-center space-x-3">
              <img src="/assets/images/microsoft.svg" alt="microsoft" />
              <span className="font-sans text-base font-normal leading-5 text-gray-800">
                Login with Microsoft
              </span>
            </div>
          </button>
        </div>
        <div className="flex w-full flex-row justify-between py-8 ">
          <img src="/assets/images/line.svg" alt="line" />
          <span>Or</span>
          <img src="/assets/images/line.svg" alt="line" />
        </div>
        <Formik
          initialValues={{ email: '' }}
          validate={(values) => {
            const errors: any = {};
            if (!values.email) {
              errors.email = 'Required.';
            } else if (
              !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
            ) {
              errors.email = 'Invalid email address.';
            }
            return errors;
          }}
          onSubmit={(values, { setSubmitting }) => {
            setTimeout(() => {
              console.log(values);
              // can show loader if wanted
              router.push('createpassword');
              setSubmitting(false);
            }, 400);
          }}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            isSubmitting,
            handleSubmit,
          }) => (
            <form className="flex flex-col" onSubmit={handleSubmit}>
              <div className="mb-6 w-full">
                <Input
                  label="Email ID"
                  name="email"
                  className="w-full"
                  placeholder="<EMAIL>"
                  type="email"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.email}
                  intent={
                    errors.email && touched.email ? 'hasError' : 'enabled'
                  }
                  error={errors.email && touched.email && errors.email}
                />
              </div>
              <Button
                intent="primary"
                disabled={
                  isSubmitting ||
                  Object.keys(errors).length > 0 ||
                  !values.email
                }
              >
                Verify Mail Address
              </Button>
            </form>
          )}
        </Formik>
        <div className="flex w-full items-center justify-center py-6">
          <span className="font-sans text-base font-normal leading-5 text-gray-800">
            Already have an account?
            <Link className="px-1" href="login" content="Login" />
          </span>
        </div>
      </div>
    </div>
  );
};

export default Register;
