'use client';

/* eslint-disable class-methods-use-this */
import type { AxiosInstance } from 'axios';
import axios from 'axios';

import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

export class APIService {
  private dispatch: any;

  private router: any;

  private axiosInstance: AxiosInstance;

  headers = {
    'X-API-KEY': ApiUtilities.apiKey,
  };

  constructor(dispatch: any, router: any) {
    this.axiosInstance = axios.create();
    this.dispatch = dispatch;
    this.router = router;

    // Interceptor for response errors
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          if (error.response.status === 401 || error.response.status === 403) {
            this.dispatch(
              setToastAlert({
                isToastOpen: true,
                intent: 'error',
                title: 'Something Went Wrong!!',
                content: 'Unauthorized access - perhaps you need to log in',
              }),
            );
            localStorage.clear();
            this.router.push('/login');
            console.error('Unauthorized access - perhaps you need to log in?');
          }
        }
        return Promise.reject(error);
      },
    );
  }

  getTokenHeader() {
    const accessToken = localStorage.getItem('access_token');
    if (accessToken) {
      return { Authorization: `Bearer ${accessToken}` };
    }
    return {};
  }

  getRequestHeaders(additionalHeaders: any = {}, isGenPro = false) {
    const tokenHeader = this.getTokenHeader();
    let baseHeaders: any = this.headers;
    
    // For GenPro endpoints, don't include X-API-KEY
    if (isGenPro) {
      baseHeaders = {};
    }
    
    const mergedHeaders = {
      ...baseHeaders,
      ...tokenHeader,
      ...additionalHeaders,
    };
    const filteredHeaders: any = Object.fromEntries(
      Object.entries(mergedHeaders).filter(([_, value]) => value != null),
    );
    return filteredHeaders;
  }

  getRequest(url: string) {
    return this.axiosInstance.get<any>(url, {
      headers: this.getRequestHeaders(),
    });
  }

  deleteRequest(url: string) {
    return this.axiosInstance.delete<any>(url, {
      headers: this.getRequestHeaders(),
    });
  }

  postRequest(url: string, data: any) {
    return this.axiosInstance.post<any>(url, data, {
      headers: this.getRequestHeaders(),
    });
  }

  putRequest(url: string, data: any) {
    return this.axiosInstance.put<any>(url, data, {
      headers: this.getRequestHeaders(),
    });
  }

  customGetRequest(url: string, head: any) {
    return this.axiosInstance.get<any>(url, {
      headers: this.getRequestHeaders(head),
    });
  }

  customPutRequest(url: string, data: any, head: any) {
    return this.axiosInstance.put<any>(url, data, {
      headers: this.getRequestHeaders(head),
    });
  }

  unauthorizedPostRequest(url: string, data: any) {
    return axios.post<any>(url, data, { headers: this.headers });
  }

  unauthorizedGetRequest(url: string) {
    return axios.get<any>(url, { headers: this.headers });
  }

  unauthorizedGetRequestBsm(url: string) {
    return axios.get<any>(url);
  }

  postRequestBsmFile(url: string, data: any) {
    const customHeaders = {
      'Content-Type': 'multipart/form-data',
    };
    return this.axiosInstance.post<any>(url, data, {
      headers: this.getRequestHeaders(customHeaders),
    });
  }

  // GenPro specific methods
  genproGetRequest(url: string) {
    return this.axiosInstance.get<any>(url, {
      headers: this.getRequestHeaders({}, true),
    });
  }

  genproPostRequest(url: string, data: any) {
    return this.axiosInstance.post<any>(url, data, {
      headers: this.getRequestHeaders({}, true),
    });
  }

  genproPutRequest(url: string, data: any) {
    return this.axiosInstance.put<any>(url, data, {
      headers: this.getRequestHeaders({}, true),
    });
  }

  genproDeleteRequest(url: string) {
    return this.axiosInstance.delete<any>(url, {
      headers: this.getRequestHeaders({}, true),
    });
  }

  genproFileUploadRequest(url: string, data: any) {
    const customHeaders = {
    };
    console.log("uploading files")
    return this.axiosInstance.post<any>(url, data, {
      headers: this.getRequestHeaders(customHeaders, true),
    });
  }

  genproUnauthorizedPostRequest(url: string, data: any) {
    return axios.post<any>(url, data, {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // Helper method to get the appropriate base URL
  getBaseUrl(apiPath: any) {
    if (apiPath.server === 'genpro') {
      return ApiUtilities.getApiServerUrlGenPro;
    }
    return ApiUtilities.getApiServerUrlBsm;
  }

  // Helper method to build full URL with parameter replacement
  buildUrl(apiPath: any, params: any = {}) {
    let url = apiPath.url;
    
    // Replace URL parameters like {id}
    Object.keys(params).forEach(key => {
      url = url.replace(`{${key}}`, params[key]);
    });
    
    return this.getBaseUrl(apiPath) + url;
  }
}
