[{"id": 1, "source_system_id": 1, "field_name": "Host Name", "sort_id": 3, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Port Range", "sort_id": 8, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Language", "sort_id": 7, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Client", "sort_id": 6, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Password", "sort_id": 5, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Username", "sort_id": 4, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Description", "sort_id": 2, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 1, "source_system_id": 1, "field_name": "Connection Name", "sort_id": 1, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Textbox"}, {"id": 5, "source_system_id": 1, "field_name": "HTTPS Connection", "sort_id": 9, "field_default": null, "sdc01": "5", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "Radiobutton"}, {"id": 10, "source_system_id": 1, "field_name_second": "No", "field_name_first": "Yes", "field_name": "ERP", "label": "Do you have ERP used as system of records?", "sort_id": 10, "field_default": null, "sdc01": "1", "created_at": "2023-09-22T00:00:00", "created_by": "SYSTEM", "updated_at": null, "updated_by": null, "deleted_at": null, "deleted_by": null, "is_active": true, "sdc01_desc": "RadioGroup", "placeholder": "Enter the password"}]