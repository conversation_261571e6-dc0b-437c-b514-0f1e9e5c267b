'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { setDatasetTableResponseFetched } from '@/slices/datasetCrudSlice';
import {
  setIsWarningAlertOpen,
  setToastAlert,
  setWarningAlert,
} from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { WarningDialog } from '../../ui/Dialog';

interface UploadFileTableProps {
  scrollPosition?: any;
  onRowChange?: any;
  onActionChange?: any;
}
const UploadFileTable: React.FC<UploadFileTableProps> = ({
  scrollPosition,
  onRowChange,
  onActionChange,
}) => {
  // essentials
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const tableData = useSelector(
    (state: any) => state.fileUploadCred.processedFileTableData,
  );
  // States
  const [filteredData, setFilteredData] = useState(null);
  const [RowID, setRowID] = useState(null);

  // Constants
  const header: any = [
    {
      title: 'File Name',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Business Segment',
      optionsEnabled: true,
      options: [],
      width: 'min-w-[150px]',
    },
    {
      title: 'Dataset Identified',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'File Validation',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Year',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Quarter',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Month',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Status',
      optionsEnabled: true,
      options: [],
    },
  ];

  const deleteItem = () => {
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getDataSetTables.url
    }/${RowID}`;
    dispatch(setIsLoading(true));
    apiService
      .deleteRequest(url)
      .then((res) => {
        if (res.status === 200 || res.status === 204) {
          dispatch(setDatasetTableResponseFetched(false));
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: `Dataset ${RowID} deleted successfully`,
              content: `Dataset ${RowID} has been successfully deleted and all the items connected with the Dataset ${RowID} has been stopped working`,
            }),
          );
        }
      })
      .finally(() => {
        dispatch(setIsLoading(false));
        dispatch(setIsWarningAlertOpen(false));
      });
  };

  const menuData: any = [
    {
      option: 'View Data',
      clickFn: (key: any) => {
        console.log('key', key);
      },
    },
    {
      option: 'Edit File',
      clickFn: (key: any) => {
        console.log('key', key);
      },
    },
    {
      option: 'Upload Data',
      clickFn: (key: any) => {
        console.log('key', key);
      },
    },
    {
      option: 'Delete File',
      clickFn: (key: any) => {
        setRowID(key);
        dispatch(
          setWarningAlert({
            isWarningOpen: true,
            headerTitle: `Delete File ${key}`,
            message: `Are you sure? Do you want to delete the File ${key} Please note this action cannot be reverted`,
            actionbuttonText: 'Yes, Delete',
          }),
        );
      },
    },
  ];

  useEffect(() => {
    if (tableData) {
      setFilteredData(tableData);
    }
  }, [tableData]);

  useEffect(() => {
    if (filteredData) {
      setFilteredData(filteredData);
    }
  }, [filteredData]);

  return (
    <div className=" flex h-full w-full flex-col">
      <div className="h-[calc(100vh-220px)] w-full overflow-auto">
        {filteredData && (
          <Table
            isDashTable
            menuData={menuData}
            isUploadTable
            header={header}
            data={filteredData}
            scrollPosition={scrollPosition}
            enableOptions={false}
            onRowChange={onRowChange}
            onActionChange={onActionChange}
          />
        )}
      </div>

      <WarningDialog onConfirm={deleteItem} />
    </div>
  );
};

export default UploadFileTable;
