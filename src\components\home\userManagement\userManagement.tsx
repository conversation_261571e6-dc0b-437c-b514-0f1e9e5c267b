/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable jsx-a11y/anchor-is-valid */

'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

import UsersTable from '@/components/tables/userManagement/usersTable';
import { Link } from '@/components/ui/Link';
import { createMenuName } from '@/constants/menuSvg';

import { Badge } from '../../ui/Badge';
import { Tabs } from '../../ui/Tabs';

const UserManagement: React.FC = () => {
  // Essentials
  const router = useRouter();

  // States
  const [filteredDataCount, setFilteredDataCount] = useState('0');
  const [selectedTab, setSelectedTab] = useState(0);

  // Methods
  const handleFilteredDataCount = (count: string) => {
    setFilteredDataCount(count);
  };
  const tabData: any = [
    {
      title: 'Users',
      icon: (
        <Badge
          intent={selectedTab === 0 ? 'counter' : 'neutral'}
          content={filteredDataCount}
        />
      ),
      component: (
        <UsersTable onFilteredDataCountChange={handleFilteredDataCount} />
      ),
    },
  ];
  const goToAddUser = () => {
    const parameter: any = {
      mode: 'Create',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/userManagement/addNewUser?${queryString}`);
  };
  return (
    <div className="flex w-full flex-col space-y-4">
      <div className="flex w-full flex-row items-center justify-between">
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          User Management
        </span>
        <div className="create-user-management">
          <Link content={createMenuName('Create New')} onClick={goToAddUser} />
        </div>
      </div>
      <div className="user-management-table flex h-fit min-h-[88vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
        <Tabs
          data={tabData}
          selectedIndex={selectedTab}
          onChange={(e: number) => {
            setSelectedTab(e);
          }}
        />
      </div>
    </div>
  );
};

export default UserManagement;
