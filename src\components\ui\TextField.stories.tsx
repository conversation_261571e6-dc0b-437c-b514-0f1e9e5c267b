import type { Meta, StoryObj } from '@storybook/react';

import { TextField } from './TextField';

const meta = {
  title: 'UI/TextField',
  component: TextField,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof TextField>;

export default meta;
type Story = StoryObj<typeof meta>;

const Enabled: Story = {
  args: {
    label: 'Label',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    name: 'Item',
  },
};
const Disabled: Story = {
  args: {
    label: 'Label',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    intent: 'disabled',
    name: 'Item',
  },
};

const HasError: Story = {
  args: {
    label: 'Label',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    intent: 'hasError',
    name: 'Item',
    error: 'Error message.',
  },
};

export { Disabled, Enabled, HasError };
