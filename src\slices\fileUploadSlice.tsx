import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface fileUploadCrudState {
  isFileFetched: boolean;
  processedFileList: any;
  processedFileTableData: any;
}

const initialState: fileUploadCrudState = {
  isFileFetched: false,
  processedFileList: [],
  processedFileTableData: [],
};

export const fileUploadCrudSlice = createSlice({
  name: 'fileUploadCrud',
  initialState,
  reducers: {
    setIsFileFetched: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.isFileFetched = action.payload;
    },
    setProcessedFile: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.processedFileList = action.payload;
    },
    setProcessedFileTableData: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.processedFileTableData = action.payload;
    },
    resetState: () => {
      return initialState;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setProcessedFile,
  setProcessedFileTableData,
  resetState,
  setIsFileFetched,
} = fileUploadCrudSlice.actions;

export default fileUploadCrudSlice.reducer;
