import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Heading } from './Heading';

const meta = {
  title: 'UI/Heading',
  component: Heading,
  args: {
    intent: 'h900',
  },
} satisfies Meta<typeof Heading>;

export default meta;
type Story = StoryObj<typeof meta>;

const H900: Story = {
  args: {
    content: 'This is Heading 900',
    intent: 'h900',
  },
};
const H800: Story = {
  args: {
    content: 'This is Heading 800',
    intent: 'h800',
  },
};
const H700: Story = {
  args: {
    content: 'This is Heading 700',
    intent: 'h700',
  },
};
const H600: Story = {
  args: {
    content: 'This is Heading 600',
    intent: 'h600',
  },
};
const H500: Story = {
  args: {
    content: 'This is Heading 500',
    intent: 'h500',
  },
};
const H400: Story = {
  args: {
    content: 'This is Heading 400',
    intent: 'h400',
  },
};
const H300: Story = {
  args: {
    content: 'This is Heading 300',
    intent: 'h300',
  },
};
const H200: Story = {
  args: {
    content: 'This is Heading 200',
    intent: 'h200',
  },
};
const H100: Story = {
  args: {
    content: 'This is Heading 100',
    intent: 'h100',
  },
};

export { H100, H200, H300, H400, H500, H600, H700, H800, H900 };
