import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const radio = cva(
  'h-[22px] w-[22px] cursor-pointer border-[1px] border-lightgray-100 text-blue-200 outline-none ',
  {
    variants: {
      intent: {
        enabled: [
          'checked:bg-blue-200',
          'checked:bg-[length:30px_30px]',
          'disabled:bg-blue-200/50',
          'focus:ring-0',
        ],
        disabled: [
          'checked:bg-[length:30px_30px]',
          'pointer-events-none',
          'opacity-50',
        ],
      },
    },
    compoundVariants: [
      {
        intent: 'enabled',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'enabled',
    },
  },
);

const containerDiv = cva(' flex w-full flex-col items-center', {
  variants: {
    intent: {
      enabled: [],
      disabled: ['opacity-50', 'text-gray-400'],
      hasError: [],
    },
  },
  compoundVariants: [
    {
      intent: 'enabled',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'enabled',
  },
});

export interface RadioProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof radio> {
  label?: string;
  error?: any;
}

export const Radio: React.FC<RadioProps> = ({
  className,
  intent,
  label,
  onChange,
  name,
  value,
  error,
  ...props
}) => (
  <div className={containerDiv({ className, intent })}>
    <div className="flex w-full flex-row items-center">
      <input
        type="radio"
        name={name}
        value={value}
        onChange={onChange}
        className={radio({ intent, className })}
        {...props}
      />
      {label && (
        <div className="pl-2 font-sans text-sm font-normal leading-5 text-gray-500 disabled:opacity-50">
          {label}
        </div>
      )}{' '}
    </div>
    {error && (
      <div className="pt-1 font-sans text-xs font-normal leading-4 text-red-200">
        {error}
      </div>
    )}{' '}
  </div>
);
