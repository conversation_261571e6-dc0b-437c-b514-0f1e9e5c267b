/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import moment from 'moment';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { Tooltip } from 'react-tooltip';

import { LocalService } from '@/service/local.service';
import {
  getLastAlphaNumericCharacter,
  getLighterBgColor,
  getMonthNumber,
} from '@/utils/utilityHelper';

import Pagination from './Pagination';

const SimpleTable: React.FC<{
  yearSelected?: any;
  isFreshnessTable?: boolean;
  isChatTable?: boolean;
  isMonthTable?: boolean;
  isReconTable?: boolean;
  selectedTab?: any;
  hasPagination?: boolean;
  isValidationTable?: boolean;
  isAvailabilityTable?: boolean;
  headers: any[];
  tableRows: any[];
}> = ({
  headers,
  yearSelected,
  isFreshnessTable,
  isChatTable,
  isMonthTable,
  isReconTable,
  tableRows,
  hasPagination,
  isAvailabilityTable,
  selectedTab,
  isValidationTable,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [currentItems, setCurrentItem] = useState<any>([]);
  const paginate = (pageNumber: any) => setCurrentPage(pageNumber);
  const localService = new LocalService();
  const router = useRouter();
  const [rows, setRows] = useState<any>(tableRows);
  const [sorting, setSorting] = useState<{
    header: string;
    order: 'asc' | 'desc';
  } | null>(null);
  const toggleSorting = (header: string) => {
    if (!sorting) {
      setSorting({ header, order: 'asc' });
    } else if (sorting.header === header) {
      setSorting({
        ...sorting,
        order: sorting.order === 'asc' ? 'desc' : 'asc',
      });
    } else {
      setSorting({ header, order: 'asc' });
    }
  };

  const openListOfPendingFiles = (rowData: any) => {
    const parameter: any = {
      id: rowData.Report.id,
      name: rowData.Report.data,
      year: yearSelected || 'All',
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`dataQuality/reportFreshnessPendingFiles?&${queryString}`);
  };

  const filePreview = (data: any) => {
    const fileData: any = {
      'File Name': data.dataFile,
      'Business Segment': data.businessSegment ? data.businessSegment : '-',
      'Dataset Name': data.dataset ? data.dataset : '-',
      Year: Number(data.year) > 0 ? data.year : 'N/A',
      Quarter: Number(data.quarter) > 0 ? data.quarter : 'N/A',
      Month: Number(data.month) > 0 ? data.month : 'N/A',
      'Uploaded Date': data.updatedDate,
    };
    localService.setItem('fileDetailForPreview', JSON.stringify(fileData));
    const parameter: any = {
      fileId: data.fileId,
      name: data.dataFile,
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/fileUpload/filePreview?${queryString}`);
  };

  const filePreviewForActive = (data: any) => {
    const fileData: any = {
      'File Name': data.originalFileName,
      'Business Segment': data.country ? data.country : '-',
      'Dataset Name': data.datasetName ? data.datasetName : '-',
      Year: Number(data.year) > 0 ? data.year : 'N/A',
      Quarter: Number(data.quarter) > 0 ? data.quarter : 'N/A',
      Month: Number(data.month) > 0 ? data.month : 'N/A',
      'Uploaded Date': moment(data.uploadedAt).format('YYYY-MM-DD'),
      'Is Replaced': data.isReplaced ? 'Yes' : 'No',
    };
    localService.setItem('fileDetailForPreview', JSON.stringify(fileData));
    const parameter: any = {
      fileId: data.id,
      name: data.originalFileName,
    };
    const queryString = new URLSearchParams(parameter).toString();
    router.push(`/fileUpload/filePreview?${queryString}`);
  };

  const navigateToRoute = (header: string, dataset: string, rowData: any) => {
    if (rowData.fileData.receivedFiles.length === 1) {
      filePreviewForActive(rowData.fileData.receivedFiles[0]);
    } else {
      const filter: any = {
        selectedYear: rowData.fileData.year,
        selectedMonth: 'All',
        selectedQuarter: 'All',
        selectedSource: dataset,
        selectedFileIsReplaced: 'All',
      };
      if (selectedTab === 4) {
        filter.selectedMonth = getMonthNumber(header);
      }
      if (selectedTab === 5) {
        filter.selectedQuarter =
          Number(getLastAlphaNumericCharacter(header)) + 1;
      }
      if (selectedTab === 6) {
        filter.selectedYear = header;
      }
      localStorage.setItem('fileUploadFilters', JSON.stringify(filter));
      localStorage.setItem('fileUploadTab', '1');
      router.push(`/fileUpload`);
    }
  };

  const sortValues = (a: any, b: any) => {
    if (!sorting) return 0;
    const { header, order } = sorting;
    const valueA = a[header]?.data;
    const valueB = b[header]?.data;
    if (order === 'asc') {
      if (valueA < valueB) return -1;
      if (valueA > valueB) return 1;
    } else {
      if (valueA > valueB) return -1;
      if (valueA < valueB) return 1;
    }
    return 0;
  };

  const openDashboard = (url: string, name: string) => {
    if (name !== '' && url !== '') {
      const parameter: any = {
        name,
        url,
      };
      const queryString = new URLSearchParams(parameter).toString();
      router.push(`/insight?${queryString}`);
    }
  };

  useEffect(() => {
    if (sorting) {
      setRows([...tableRows].sort(sortValues));
    } else {
      setRows([...tableRows]);
    }
  }, [sorting]);

  useEffect(() => {
    setRows(tableRows);
  }, [tableRows]);

  useEffect(() => {
    if (hasPagination) {
      if (rows.length > 0) {
        const totalItems = rows.length;
        const maxPage = Math.ceil(totalItems / itemsPerPage);
        if (currentPage > maxPage) {
          setCurrentPage(maxPage);
          return;
        }
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(currentPage * itemsPerPage, rows.length);
        setCurrentItem(rows.slice(startIndex, endIndex));
      } else {
        setCurrentItem([]);
      }
    } else {
      setCurrentItem(rows);
    }
  }, [rows, currentPage]);

  return (
    <div
      className={`flex ${
        isMonthTable && currentItems.length > 0
          ? 'border-[.5px] border-lightgray-100'
          : ''
      }  ${
        isValidationTable && currentItems.length > 0
          ? 'border-t-[.5px] border-lightgray-100'
          : ''
      } relative size-full flex-col`}
    >
      <div
        className={`flex ${
          isValidationTable ? 'h-[calc(100vh-300px)] ' : 'h-full'
        } ${
          isReconTable ? 'min-h-[17vh]' : 'min-h-[27vh]'
        }  w-full  flex-col overflow-auto bg-white-200`}
      >
        {currentItems?.length !== 0 && (
          <table
            className={` ${isChatTable ? '' : 'has-table-border'} ${
              currentItems.length < 7 ? 'h-fit' : 'h-full'
            } w-full ${isAvailabilityTable ? 'table-auto' : 'table-fixed'}`}
          >
            <thead className={`sticky top-0 z-10 bg-white-200 `}>
              <tr className="">
                {headers.map((header: any, index: any) => (
                  <th
                    key={index}
                    className={`${
                      isAvailabilityTable && index === 0
                        ? 'w-[300px]'
                        : 'min-w-[100px]'
                    } ${
                      index === 0 && !isChatTable
                        ? 'sticky left-0 z-20 bg-white-200'
                        : ''
                    } border border-lightgray-100 p-3 text-left`}
                  >
                    {typeof header === 'object' ? (
                      <div className="flex flex-row space-x-3">
                        {!header.hasSorting && (
                          <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                            {header.data}
                          </span>
                        )}
                        {header.hasSorting && (
                          <button
                            type="button"
                            onClick={() => toggleSorting(header.data)}
                            className="flex flex-row items-center justify-between space-x-2"
                          >
                            <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                              {header.data}
                            </span>
                            {sorting?.order === 'asc' &&
                              sorting.header === header.data && (
                                <img
                                  className="cursor-pointer"
                                  src="/assets/images/sort.svg"
                                  alt="sort"
                                />
                              )}
                            {sorting?.order === 'desc' &&
                              sorting.header === header.data && (
                                <img
                                  className="rotate-180 cursor-pointer"
                                  src="/assets/images/sort.svg"
                                  alt="sort"
                                />
                              )}
                            {sorting?.header !== header.data && (
                              <img
                                className="cursor-pointer"
                                src="/assets/images/sort.svg"
                                alt="sort"
                              />
                            )}
                          </button>
                        )}
                      </div>
                    ) : (
                      <span className="font-sans text-sm font-semibold capitalize text-gray-500">
                        {header}
                      </span>
                    )}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {currentItems.map((row: any, rowIndex: number) => (
                <tr key={rowIndex}>
                  {headers.map((header, colIndex) => (
                    <td
                      onClick={() => {
                        if (
                          typeof row[header.data] === 'object' &&
                          (row[header.data]?.score === 4 ||
                            row[header.data]?.score === 3)
                        ) {
                          navigateToRoute(
                            header.data,
                            row.Dataset.data,
                            row[header.data],
                          );
                        }
                        if (row[header.data].isLink) {
                          if (row[header.data].fileDetail) {
                            filePreview(row[header.data].fileDetail);
                          }
                          if (row[header.data].url) {
                            openDashboard(
                              row[header.data].url,
                              row[header.data].tableName,
                            );
                          }
                          if (
                            row[header.data].data === 'Pending' ||
                            (row[header.data].data === 'pending' &&
                              isFreshnessTable)
                          ) {
                            openListOfPendingFiles(row);
                          }
                        }
                      }}
                      key={colIndex}
                      className={`
                       ${
                         row[header.data]?.score === 4 ||
                         row[header.data]?.score === 3
                           ? 'cursor-pointer'
                           : ''
                       } ${getLighterBgColor(
                         row[header.data]?.score,
                       )}  border border-lightgray-100 px-3 py-2 text-left font-sans text-sm font-semibold  capitalize text-gray-500`}
                    >
                      {typeof row[header.data] === 'object' ? (
                        <div className="flex flex-row items-center justify-between">
                          {row[header.data].isLink ? (
                            <span className=" cursor-pointer text-blue-200">
                              {row[header.data].data}
                            </span>
                          ) : (
                            <span>
                              {row[header.data].data
                                ? row[header.data].data
                                : '-'}
                            </span>
                          )}
                          {row[header.data].tooltip && (
                            <div className="mx-1 size-[18px]">
                              <img
                                data-tooltip-id="info-tooltip"
                                data-tooltip-html={row[header.data].tooltip}
                                className="size-[18px] cursor-pointer"
                                src="/assets/images/information.svg"
                                alt="tooltip"
                              />
                            </div>
                          )}
                        </div>
                      ) : (
                        row[header]
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
        {currentItems?.length === 0 && (
          <div
            className={`relative ${
              isReconTable ? 'top-[7vh]' : 'top-[10vh]'
            }  ml-[45%] inline-block items-center justify-center`}
          >
            <span className="font-sans text-sm font-normal text-gray-400 ">
              No Data Available
            </span>
          </div>
        )}

        <Tooltip
          className="info-tooltip"
          id="info-tooltip"
          place={isMonthTable ? 'top' : 'left'}
          variant="info"
          positionStrategy="fixed"
        />
      </div>
      {currentItems?.length !== 0 && hasPagination && (
        <div className="sticky bottom-0 z-50 flex w-full items-center justify-center bg-white-200 px-3 pb-2 pt-4">
          <Pagination
            itemsPerPage={itemsPerPage}
            totalItems={rows.length}
            currentPage={currentPage}
            paginate={paginate}
          />
        </div>
      )}
    </div>
  );
};

export default SimpleTable;
