import './rightBottomHandleNode.scss';

import { Handle, Position } from 'reactflow';

const RightBottomHandleNode = ({ data }: any) => {
  return (
    <div className="right-bottom-handle-node">
      <Handle
        id="right"
        className="right-handle"
        type="source"
        position={Position.Right}
      />
      <Handle
        id="bottom"
        className="bottom-handle"
        type="source"
        position={Position.Bottom}
      />
      <div>{data.label}</div>
    </div>
  );
};

export default RightBottomHandleNode;
