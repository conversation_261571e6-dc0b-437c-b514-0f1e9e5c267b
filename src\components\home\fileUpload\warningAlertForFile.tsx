import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

import { Button } from '@/components/ui/Button';

export const WarningAlertForFile: React.FC<{
  isProceedButtonVisible?: boolean;
  isOpen: boolean;
  onClose?: any;
  onConfirm?: any;
  message?: any;
  header?: any;
  data?: any;
  cancelButtonText?: any;
  actionbuttonText?: any;
}> = ({
  isProceedButtonVisible,
  cancelButtonText,
  isOpen,
  onClose,
  onConfirm,
  message,
  header,
  actionbuttonText,
  data,
}) => {
  // Methods
  const closeModal = () => {
    onClose();
  };
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeModal}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-blueGray-200 opacity-60" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="min-h-[20vh] w-[53vw] overflow-hidden  rounded bg-white-200 py-5 shadow-xl transition-all">
                <div className="flex size-full flex-col items-center justify-center py-3">
                  <img
                    className="mb-5"
                    src="/assets/images/warning.svg"
                    alt="warning"
                  />
                  <div className="mb-4">
                    <span className="font-sans text-xl font-semibold text-blueGray-300">
                      {header}
                    </span>
                  </div>
                  <span className="font-sans text-sm font-semibold text-gray-500 ">
                    {data.length === 1 ? 'File' : 'Files'}
                  </span>
                  <div className="my-2 flex h-fit max-h-[calc(100vh-450px)] min-h-[50px] max-w-[580px] items-center overflow-auto border-y border-lightgray-100 p-2 ">
                    <div className="flex flex-col space-y-1">
                      {data.map((item: any) => (
                        <span
                          key={item.originalFileName}
                          className="font-sans text-xs font-semibold text-gray-400"
                        >
                          {item.originalFileName}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex max-w-[580px] justify-center">
                    <span className="font-sans text-sm font-semibold text-gray-500">
                      {message}
                    </span>
                  </div>
                  <div className="mt-6 flex w-full flex-row items-center justify-center space-x-4 ">
                    <Button
                      type="button"
                      onClick={closeModal}
                      intent="secondary"
                    >
                      {cancelButtonText || ' No, Discard'}
                    </Button>
                    {isProceedButtonVisible && (
                      <Button
                        type="button"
                        onClick={onConfirm}
                        className="ml-4"
                      >
                        {actionbuttonText || ' Yes, Proceed'}
                      </Button>
                    )}
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};
