import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface sourceSystemCrudState {
  onlineConnectionName: string;
  allSystemIntegrationTableDataFetched: boolean;
  selectedSourceSystem: any;
  sourceSystemConfig: any;
  sourceSystemConfigData: any;
  selectedDomains: any;
  selectedTables: any;
  tableSchedules: any;
  isTestingConnection: any;
  isEditing: any;
  isTestingConnectionValid: boolean;
  schedulingConfig: any;
}

const initialState: sourceSystemCrudState = {
  onlineConnectionName: '',
  allSystemIntegrationTableDataFetched: false,
  isTestingConnectionValid: false,
  selectedSourceSystem: null,
  sourceSystemConfig: [],
  sourceSystemConfigData: {},
  selectedDomains: [],
  selectedTables: [],
  tableSchedules: {},
  isTestingConnection: false,
  isEditing: false,
  schedulingConfig: '',
};

export const sourceSystemCrudSlice = createSlice({
  name: 'sourceSystemsCrud',
  initialState,
  reducers: {
    setOnlineConnectionName: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.onlineConnectionName = action.payload;
    },
    setAllSystemIntegrationTableDataFetched: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.allSystemIntegrationTableDataFetched = action.payload;
    },
    setIsTestingConnectionValid: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.isTestingConnectionValid = action.payload;
    },
    setSelectedSourceSystem: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.selectedSourceSystem = action.payload;
    },
    setSourceSystemConfig: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.sourceSystemConfig = action.payload;
    },
    setSourceSystemConfigData: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.sourceSystemConfigData = action.payload;
    },
    setSelectedDomains: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.selectedDomains = action.payload;
    },
    setSelectedTables: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.selectedTables = action.payload;
    },
    setTableSchedules: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.tableSchedules = action.payload;
    },
    setIsTestingConnection: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.isTestingConnection = action.payload;
    },
    setIsEditing: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.isEditing = action.payload;
    },
    setSchedulingConfig: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.schedulingConfig = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setSelectedSourceSystem,
  setSourceSystemConfig,
  setSourceSystemConfigData,
  setSelectedDomains,
  setSelectedTables,
  setTableSchedules,
  setIsTestingConnection,
  setIsEditing,
  setAllSystemIntegrationTableDataFetched,
  setIsTestingConnectionValid,
  setOnlineConnectionName,
  setSchedulingConfig,
} = sourceSystemCrudSlice.actions;

export default sourceSystemCrudSlice.reducer;
