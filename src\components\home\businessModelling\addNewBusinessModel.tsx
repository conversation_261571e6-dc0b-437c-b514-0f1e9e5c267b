/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-restricted-syntax */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import eData from 'public/testdata/businessModelling/editBusinessModelling.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import SelectExistingAttribute from '@/components/tables/businessModelling/businessConsumption/existingAttributesTable';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import {
  resetState,
  setBusinessModelDescription,
  setBusinessModelName,
  setCustomAttributes,
  setExistingAttributes,
  setJoinConfig,
} from '@/slices/businessModelCrudSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

import { Badge } from '../../ui/Badge';
import { Button } from '../../ui/Button';
import { Tabs } from '../../ui/Tabs';
import BusinessConsumptionSchema from './addNewBusinessModelTabs/businessConsumptionSchema';
import DataLoadSchedule from './addNewBusinessModelTabs/dataLoadSchedule';
import SchemaDetails from './addNewBusinessModelTabs/schemaDetails';

const AddNewBusinessModel: React.FC = () => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const apiService = useMemo(() => new APIService(dispatch, router), []);

  // Derivatives
  const mode: any = searchParams?.get('mode');
  const id: any = searchParams?.get('id');

  // States
  const [completedTabs, setCompletedTabs] = useState([-1]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [initialCustom, setInitialCustom] = useState([]);
  const [initialExisting, setInitialExisting] = useState([]);

  // Selectors
  const businessModelName = useSelector(
    (state: any) => state.businessModelCrud.businessModelName,
  );
  const customAttributes = useSelector(
    (state: any) => state.businessModelCrud.customAttributes,
  );
  const existingAttributes = useSelector(
    (state: any) => state.businessModelCrud.existingAttributes,
  );
  const joinConfig = useSelector(
    (state: any) => state.businessModelCrud.joinConfig,
  );
  const businessModelDescription = useSelector(
    (state: any) => state.businessModelCrud.businessModelDescription,
  );
  const schedulingConfig = useSelector(
    (state: any) => state.businessModelCrud.scheduling_config,
  );
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  // const aggregationTable = useSelector(
  //   (state: any) => state.businessModelCrud.aggregationTable,
  // );
  // const selectedBusinessSchema = useSelector(
  //   (state: any) => state.businessModelCrud.selectedBusinessSchema,
  // );
  // const aggregateTo = useSelector(
  //   (state: any) => state.businessModelCrud.aggregateTo,
  // );
  // const aggregateDataType = useSelector(
  //   (state: any) => state.businessModelCrud.aggregateDataType,
  // );

  // Methods
  const goBack = () => {
    dispatch(resetState());
    router.push('/businessModelling');
  };

  const tabData: any = [
    {
      title: mode === 'View' ? 'Schema Details' : 'Define Schema',
      completed: completedTabs.includes(0),
      // isForm: mode !== 'View',
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(0)
                ? 'success'
                : selectedTab === 0
                ? 'counter'
                : 'neutral'
            }
            content="1"
          />
        ),
      component: <SchemaDetails mode={mode} />,
    },
    {
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(1)
                ? 'success'
                : selectedTab === 1
                ? 'counter'
                : 'neutral'
            }
            content="2"
          />
        ),
      completed: completedTabs.includes(1),
      // isForm: mode !== 'View',
      title: 'Confirm Business Schema',
      component: <BusinessConsumptionSchema mode={mode} />,
    },
    // {
    //   icon:
    //     mode === 'View' ? (
    //       ''
    //     ) : (
    //       <Badge
    //         intent={
    //           completedTabs.includes(2)
    //             ? 'success'
    //             : selectedTab === 2
    //             ? 'counter'
    //             : 'neutral'
    //         }
    //         content="3"
    //       />
    //     ),
    //   title: 'Aggregation',
    //   // isForm: mode !== 'View',
    //   completed: completedTabs.includes(2),
    //   component: <Aggregation mode={mode} />,
    // },
    {
      icon:
        mode === 'View' ? (
          ''
        ) : (
          <Badge
            intent={
              completedTabs.includes(2)
                ? 'success'
                : selectedTab === 2
                ? 'counter'
                : 'neutral'
            }
            content="3"
          />
        ),
      title: mode === 'View' ? 'Data Load Schedule' : 'Schedule Data Load',
      component: <DataLoadSchedule mode={mode} />,
      // isForm: mode !== 'View',
      completed: completedTabs.includes(2),
    },
  ];

  const handleCompleteTab = (currentTab: number) => {
    if (!completedTabs.includes(currentTab)) {
      setCompletedTabs([currentTab, ...completedTabs]);
    }
    if (completedTabs.length >= 6) {
      goBack();
    }
    setSelectedTab(currentTab + 1);
  };

  function convertCustomAttributesArray(ca: any) {
    const newCa = structuredClone(ca);
    let customAttributesArray;
    if (newCa) {
      customAttributesArray = newCa.map((attribute: any) => {
        return {
          attribute_id: attribute.id,
          attribute_name: attribute.components[0].value,
          attribute_datatype: attribute.components[1].value,
          attribute_config: {
            description: attribute.components[2].value,
            formula: attribute.components[3].value,
          },
        };
      });
    }
    return customAttributesArray;
  }

  function convertExistingAttributesToArray(ea: any) {
    const newEa = structuredClone(ea);
    let existingAttributesArray;
    if (newEa) {
      existingAttributesArray = Object.keys(newEa).map((attributeName) => {
        const attribute = newEa[attributeName];
        return {
          attribute_name: attribute.attribute_name,
          attribute_id:
            attribute?.attribute_config?.attribute_id ?? attribute.attribute_id,
          attribute_datatype: 'VARCHAR(255)',
          attribute_config: attribute?.attribute_config ?? {
            attribute_id:
              attribute?.attribute_config?.attribute_id ??
              attribute?.attribute_id,
            attribute_name: attribute?.attribute_name,
            entity_id: attribute?.entity_id,
            entity_name: attribute?.entity_name,
          },
        };
      });
    }
    return existingAttributesArray;
  }

  const createBusinessModel = () => {
    dispatch(setIsLoading(true));
    const customAttributesArray =
      convertCustomAttributesArray(customAttributes);
    const existingAttributesArray =
      convertExistingAttributesToArray(existingAttributes);
    const url =
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getBusinessModels.url;
    const params = {
      business_model_name: businessModelName,
      business_model_description: businessModelDescription,
      custom_attributes: customAttributesArray,
      existing_attributes: existingAttributesArray,
      join_config: joinConfig,
      scheduling_config: schedulingConfig,
      // aggregation_config: {
      //   aggregationTable,
      //   selectedBusinessSchema,
      //   aggregateTo,
      //   aggregateDataType,
      // },
    };
    // if (modalHeading === 'Create Connection') {
    apiService
      .postRequest(url, params)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 201) {
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'Business Schema Created successfully',
              content: 'New Business Schema has been created successfully',
            }),
          );
          goBack();
        } else {
          alert('something went wrong');
        }
        // handleCompleteTab(selectedTab);
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
    // } else {
    //   apiService.putRequest(url, params).then((res) => {
    //     if (res.status === 200) {
    //       console.log('connection updated');
    //     } else {
    //       alert('something went wrong');
    //     }
    //     closeModal();
    //   });
    // }
  };

  function deepEqual(x: { [x: string]: any }, y: { [x: string]: any }): any {
    const ok = Object.keys;
    const tx = typeof x;
    const ty = typeof y;
    return x && y && tx === 'object' && tx === ty
      ? ok(x).length === ok(y).length &&
          ok(x).every((key: any) => deepEqual(x[key], y[key]))
      : x === y;
  }

  const checkSameArrayAttributes = (arr1: any, arr2: any) => {
    const addedObjects = arr2.filter(
      (obj2: any) =>
        !arr1.some(
          (obj1: any) =>
            (obj1.attribute_id || obj1.entity_id) ===
            (obj2.attribute_id || obj2.entity_id),
        ),
    );
    const deletedObjects = arr1.filter(
      (obj2: any) =>
        !arr2.some(
          (obj1: any) =>
            (obj1.attribute_id || obj1.entity_id) ===
            (obj2.attribute_id || obj2.entity_id),
        ),
    );

    const unChangedObjects = arr1.filter((obj2: any) =>
      arr2.some((obj1: any) => deepEqual(obj1, obj2)),
    );

    const updatedObjects = arr1.filter((obj2: any) =>
      arr2.some(
        (obj1: any) =>
          (obj1.attribute_id || obj1.entity_id) ===
            (obj2.attribute_id || obj2.entity_id) && !deepEqual(obj1, obj2),
      ),
    );

    return [
      ...addedObjects.map((obj2: any) => ({ ...obj2, action_flag: 'I' })),
      ...deletedObjects.map((obj1: any) => ({ ...obj1, action_flag: 'D' })),
      ...unChangedObjects,
      ...updatedObjects.map((obj1: any) => ({ ...obj1, action_flag: 'U' })),
    ];
  };

  const editBusinessModel = () => {
    dispatch(setIsLoading(true));
    const url = `${
      ApiUtilities.getApiServerUrl + ApiUtilities.apiPath.getBusinessModels.url
    }/${id}`;
    const params = {
      business_model_name: businessModelName,
      business_model_description: businessModelDescription,
      custom_attributes: checkSameArrayAttributes(
        convertCustomAttributesArray(initialCustom),
        convertCustomAttributesArray(customAttributes),
      ),
      existing_attributes: checkSameArrayAttributes(
        convertExistingAttributesToArray(initialExisting),
        convertExistingAttributesToArray(existingAttributes),
      ),
      join_config: joinConfig,
      scheduling_config: schedulingConfig,
      // aggregation_config: {
      //   aggregationTable,
      //   selectedBusinessSchema,
      //   aggregateTo,
      //   aggregateDataType,
      // },
    };
    apiService
      .putRequest(url, params)
      .then((res) => {
        dispatch(setIsLoading(false));
        if (res.status === 201 || res.status === 200) {
          dispatch(resetState());
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'success',
              title: 'Business Schema Updated successfully',
              content: 'Business Schema has been updated successfully',
            }),
          );
          goBack();
        } else {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content: 'Something went wrong!',
            }),
          );
        }
        // handleCompleteTab(selectedTab);
      })
      .catch((err) => {
        console.log(err);
        dispatch(setIsLoading(false));
      });
  };

  const arrayHasNonEmptyObject = (ob: any) => {
    if (Object.keys(ob).length > 0) {
      const hasNonEmptyProperty = Object.values(ob).some((obj: any) => {
        return Object.values(obj).every((value, index) => {
          if (index === 3) {
            return true;
          }
          return value !== null && value !== '';
        });
      });
      return hasNonEmptyProperty;
    }
    return false;
  };

  function replaceKeyWithAttributeId(obj: any) {
    if (typeof obj === 'object' && obj !== null) {
      const updatedObj: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const entry = obj[key];
          const attributeId = entry.attribute_config.attribute_id;
          updatedObj[attributeId] = { ...entry };
        }
      }
      return updatedObj;
    }
    return obj;
  }

  const formatCustomForTable = (result: any) => {
    const dataRows: any = [];
    if (typeof result === 'object' && result !== null) {
      for (const key in result) {
        if (result.hasOwnProperty(key)) {
          const entry: any = result[key];
          const pushObj: any = {
            id: entry.attribute_id,
            selected: true,
            components: [],
          };
          pushObj.components.push({
            value: entry.attribute_name,
            disabled: false,
            userAvatarLink: null,
            type: 'text',
          });
          pushObj.components.push({
            value: entry.attribute_datatype,
            type: 'text',
            disabled: false,
            userAvatarLink: null,
          });
          pushObj.components.push({
            value: entry.attribute_config.description,
            type: 'text',
            disabled: false,
            userAvatarLink: null,
          });
          pushObj.components.push({
            value: entry.attribute_config.formula,
            type: 'text',
            disabled: false,
            userAvatarLink: null,
          });
          dataRows.push(pushObj);
        }
      }
    }
    return dataRows;
  };

  // Effects
  useEffect(() => {
    if (!isTourOpen) {
      if (id) {
        const url = `${
          ApiUtilities.getApiServerUrl +
          ApiUtilities.apiPath.getBusinessModels.url
        }/${id}`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            dispatch(setBusinessModelName(res.data[0].business_entity_name));
            dispatch(
              setBusinessModelDescription(
                res.data[0].business_entity_description,
              ),
            );
            dispatch(setJoinConfig(res.data[0].join_config));
            dispatch(
              setExistingAttributes(
                replaceKeyWithAttributeId(res.data[0].mapped_attributes),
              ),
            );
            dispatch(
              setCustomAttributes(
                formatCustomForTable(res.data[0].custom_attributes),
              ),
            );
            setInitialExisting(res.data[0].mapped_attributes);
            setInitialCustom(
              formatCustomForTable(res.data[0].custom_attributes),
            );
          })
          .finally(() => dispatch(setIsLoading(false)));
      }
    } else {
      const apiData: any = eData;
      dispatch(setBusinessModelName(apiData.data[0].business_entity_name));
      dispatch(
        setBusinessModelDescription(
          apiData.data[0].business_entity_description,
        ),
      );
      dispatch(setJoinConfig(apiData.data[0].join_config));
      dispatch(
        setExistingAttributes(
          replaceKeyWithAttributeId(apiData.data[0].mapped_attributes),
        ),
      );
      dispatch(
        setCustomAttributes(
          formatCustomForTable(apiData.data[0].custom_attributes),
        ),
      );
      setInitialExisting(apiData.data[0].mapped_attributes);
      setInitialCustom(formatCustomForTable(apiData.data[0].custom_attributes));
    }
  }, [id]);

  return (
    <div className="view-business-modelling view-business-modelling-table edit-business-modelling-table flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {mode} Business Schema{' '}
          {businessModelName && mode !== 'Create' && (
            <span>- {businessModelName}</span>
          )}
        </span>
      </div>
      {mode !== 'View' ? (
        <div className="flex h-[82vh] w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4">
          <Tabs
            data={tabData}
            iconPosition="FIRST"
            selectedIndex={selectedTab}
            onChange={(e: number) => {
              setSelectedTab(e);
            }}
          />
        </div>
      ) : (
        <div
          className={`flex w-full flex-col rounded border-[1px] border-lightgray-100 bg-white-200 p-4 ${
            mode === 'View' ? 'h-[calc(100vh-93px)]' : 'h-[82vh]'
          }`}
        >
          <SelectExistingAttribute mode="View" id={id} />
        </div>
      )}

      {mode !== 'View' && (
        <div className="flex h-[40px] w-full flex-row items-center justify-between">
          <Button
            className="flex h-[40px] items-center"
            type="button"
            onClick={goBack}
            intent="secondary"
          >
            Save as Draft
          </Button>
          <div className="flex flex-row space-x-4">
            {' '}
            <Button
              className="flex h-[40px] items-center"
              type="button"
              onClick={goBack}
              intent="secondary"
            >
              Cancel
            </Button>
            <Button
              className="flex h-[40px] items-center"
              type="button"
              disabled={
                selectedTab === 0
                  ? !businessModelName ||
                    !businessModelDescription ||
                    !arrayHasNonEmptyObject(joinConfig)
                  : selectedTab === 1
                  ? !(
                      Object.keys(existingAttributes).length > 0 ||
                      customAttributes.length > 0
                    )
                  : // : selectedTab === 2
                    // ? !(
                    //     aggregationTable.length > 0 &&
                    //     selectedBusinessSchema !== undefined &&
                    //     Object.keys(aggregateTo).length > 0 &&
                    //     Object.keys(aggregateDataType).length > 0
                    //   )
                    false
              }
              onClick={() => {
                if (selectedTab !== 2) {
                  handleCompleteTab(selectedTab);
                } else if (mode === 'Edit') {
                  editBusinessModel();
                } else {
                  createBusinessModel();
                }
              }}
            >
              {selectedTab === 2
                ? mode !== 'Create'
                  ? 'Update'
                  : 'Create'
                : 'Next'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddNewBusinessModel;
