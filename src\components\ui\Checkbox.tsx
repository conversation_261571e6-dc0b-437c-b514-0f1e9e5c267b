import React from 'react';

export enum CheckBoxStates {
  Checked = 'Checked',
  Indeterminate = 'Indeterminate',
  Empty = 'Empty',
}
export interface CheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  labelText?: string;
  value?: CheckBoxStates;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  labelText,
  value,
  ...props
}) => {
  const checkboxRef = React.useRef<HTMLInputElement | null>(null);

  React.useEffect(() => {
    if (!checkboxRef.current) return;
    if (value === CheckBoxStates.Checked) {
      checkboxRef.current.checked = true;
      checkboxRef.current.indeterminate = false;
    } else if (value === CheckBoxStates.Empty) {
      checkboxRef.current.checked = false;
      checkboxRef.current.indeterminate = false;
    } else if (value === CheckBoxStates.Indeterminate) {
      checkboxRef.current.checked = false;
      checkboxRef.current.indeterminate = true;
    }
  }, [value]);
  return (
    <div
      className={`${props?.disabled ? 'pointer-events-none opacity-50' : ''}`}
    >
      <input
        className="relative float-left  h-[24px] w-[24px] cursor-pointer appearance-none rounded border-[1px] border-lightgray-100 bg-center bg-no-repeat text-blue-200 outline-none checked:bg-blue-200 indeterminate:bg-blue-200 hover:cursor-pointer focus:shadow-none focus:ring-0 focus:transition-[border-color_0.2s] disabled:opacity-50"
        type="checkbox"
        value=""
        id={props.id}
        ref={checkboxRef}
        {...props}
      />
      {labelText ? (
        <label
          className="inline-block pl-2 hover:cursor-pointer"
          htmlFor={props.id}
        >
          {labelText}
        </label>
      ) : null}
    </div>
  );
};
