'use client';

export class LocalService {
  ls: any;

  constructor() {
    if (typeof localStorage !== 'undefined') {
      this.ls = localStorage;
    } else {
      console.error('localStorage is not available in this environment');
    }
  }

  getItem = (key: string) => {
    if (this.ls) {
      return this.ls.getItem(key);
    }
    console.error('localStorage is not available in this environment');
    return null;
  };

  getData = (key: string) => {
    const storedData: any = this.ls?.getItem(key) || null;
    if (storedData) {
      try {
        return JSON.parse(storedData);
      } catch (e) {
        console.error('Error parsing JSON from localStorage', e);
        return null;
      }
    } else {
      return null;
    }
  };

  setItem = (key: string, value: string) => {
    if (this.ls) {
      this.ls.setItem(key, value);
    } else {
      console.error('localStorage is not available in this environment');
    }
  };
}
