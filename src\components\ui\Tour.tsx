/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { LocalService } from '@/service/local.service';
// import Tour from 'reactour';
import { setIsTourOpen } from '@/slices/appSlice';
import type { RootState } from '@/store/store';

const Tour = dynamic(() => import('reactour'), { ssr: false });
const TourComponent: React.FC<{
  steps: any;
}> = ({ steps }) => {
  // Essentials
  const dispatch = useDispatch();
  const router = useRouter();
  const localService = new LocalService();

  // Selectors
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );

  // Constants
  const currentUser: string = localService.getItem('user_type') || '';

  // States
  const [currentstep, setCurrentStep] = useState(0);
  const [prevstep, setPrevStep] = useState(0);
  const [routeAdmin] = useState<any>({
    '6_5': '/datasetDashboard',
    '6_7': '/home',
    '8_7': '/businessViews',
    '7_6': '/datasetDashboard',
    '8_9': '/datasetDashboard',
    '9_8': '/businessViews',
    '10_9': '/fileUpload',
    '12_11': '/fileUpload?history=1',
    '11_12': '/fileUpload?history=0',
    '11_10': '/fileUpload?history=0',
    '13_14': '/fileUpload?history=1',
    '10_11': '/businessViews',
    '13_12': '/dataQuality',
    '14_13': '/dataQuality',
  });
  const [routeMapCFO] = useState<any>({
    '4_5': '/home',
  });

  // Methods
  const closeTour = () => {
    setCurrentStep(1);
    router.push('/home');
    dispatch(setIsTourOpen(false));
  };

  const navigateToRoute = () => {
    const routeKey = `${currentstep}_${prevstep}`;
    const route =
      currentUser === 'CFO' ? routeMapCFO[routeKey] : routeAdmin[routeKey];
    if (route) {
      router.push(route);
    }
  };

  useEffect(() => {
    navigateToRoute();
    if (
      (currentstep === 15 && prevstep === 14 && currentUser !== 'CFO') ||
      (currentstep === 4 && prevstep === 3 && currentUser === 'CFO')
    ) {
      setTimeout(() => {
        closeTour();
      }, 2000);
    }
  }, [currentstep]);

  return (
    <Tour
      startAt={0}
      steps={steps}
      isOpen={isTourOpen}
      badgeContent={(curr, tot) => `${curr}/${tot}`}
      onRequestClose={closeTour}
      getCurrentStep={(curr) => {
        setPrevStep(currentstep);
        setCurrentStep(curr);
      }}
      goToStep={currentstep}
      disableDotsNavigation
      disableInteraction
      showNavigationNumber={false}
      showNavigation={false}
      closeWithMask
      showCloseButton={false}
    />
  );
};

export default TourComponent;
