import type { Meta, StoryObj } from '@storybook/react';

import { TableInput } from './TableInput';

const meta = {
  title: 'UI/TableInput',
  component: TableInput,
  args: {
    intent: 'enabled',
  },
} satisfies Meta<typeof TableInput>;

export default meta;
type Story = StoryObj<typeof meta>;

const Enabled: Story = {
  args: {
    label: 'Label',
    type: 'email',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    name: 'Item',
  },
};
const Disabled: Story = {
  args: {
    label: 'Label',
    type: 'email',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    intent: 'disabled',
    name: 'Item',
  },
};

const HasError: Story = {
  args: {
    label: 'Label',
    content: 'Sonika Sharan',
    placeholder: 'Placeholder',
    intent: 'hasError',
    name: 'Item',
    type: 'email',
    error: 'Error message.',
  },
};

export { Disabled, Enabled, HasError };
