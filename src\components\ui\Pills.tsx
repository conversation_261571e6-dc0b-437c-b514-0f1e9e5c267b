import type { VariantProps } from 'class-variance-authority';
import { cva } from 'class-variance-authority';
import React from 'react';

const pills = cva('inline-flex h-4  items-center rounded-lg  px-[6px]', {
  variants: {
    intent: {
      success: ['bg-green-100/10', 'text-green-100'],
      error: ['bg-red-100/10', 'text-red-100'],
      warning: ['bg-yellow-100/10', 'text-yellow-100'],
      info: ['bg-blue-200/10', 'text-blue-200'],
      neutral: ['bg-gray-500/10 text-gray-500'],
    },
  },
  compoundVariants: [
    {
      intent: 'success',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'success',
  },
});

export interface PillsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof pills> {}

export const Pills: React.FC<PillsProps> = ({
  className,
  title,
  intent,
  ...props
}) => (
  <div className={pills({ intent, className })} {...props}>
    <span className="font-sans text-xxs font-semibold  uppercase  leading-4 tracking-[0.2px]">
      {title}
    </span>
  </div>
);
