/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable no-nested-ternary */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-plusplus */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

import { setToastAlert } from '@/slices/metaDataSlice';

interface UploadFileProp {
  onFileUpload: (AllFiles: any) => void;
  isUploadCancled: boolean;
}
const UploadFile: React.FC<UploadFileProp> = ({
  onFileUpload,
  isUploadCancled,
}) => {
  const dispatch = useDispatch();
  const [highlighted, setHighlighted] = useState(false);
  const [droppedFiles, setDroppedFiles] = useState<any>([]);
  const [conflictedFiles, setConflictedFiles] = useState<any>([]);
  const [borderColor, setBorderColor] = useState('border-red-200');
  const fileInputRef = useRef<any>(null);

  const handleDragOver = (e: any) => {
    e.preventDefault();
    setHighlighted(true);
  };

  const handleDragLeave = () => {
    setHighlighted(false);
  };

  const validateAndAddFile = (files: any) => {
    const acceptedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
      'application/vnd.ms-excel.template.macroEnabled.12',
      'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
    ];

    const acceptedExtensions = [
      '.csv',
      '.xls',
      '.xlsx',
      '.xlsm',
      '.xltx',
      '.xltm',
      '.xlsb',
    ];
    const filteredFiles = files.filter((file: any) => {
      const fileType = file.type;
      const fileName = file.name;
      const fileExtension = fileName
        .substring(fileName.lastIndexOf('.') + 1)
        .toLowerCase();

      return (
        acceptedTypes.includes(fileType) ||
        acceptedExtensions.includes(`.${fileExtension}`)
      );
    });
    if (filteredFiles.length !== 0) {
      filteredFiles.forEach((file: any) => {
        const fileExists: any = droppedFiles.some(
          (existingFile: any) => existingFile.name === file.name,
        );
        if (fileExists) {
          setConflictedFiles((prevState: any) => [...prevState, file.name]);
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'File Already Exists!!',
              content:
                'The file you tried to add already exists. Please select a different file or rename the existing one.',
            }),
          );
        } else {
          setDroppedFiles((prevState: any) => [...prevState, file]);
        }
      });
    } else {
      dispatch(
        setToastAlert({
          isToastOpen: true,
          intent: 'error',
          title: 'Invalid File',
          content: 'Only Microsoft Excel file format are supported.',
        }),
      );
    }
  };

  const handleDrop = (e: any) => {
    setConflictedFiles([]);
    e.preventDefault();
    setHighlighted(false);
    const files: any = Array.from(e.dataTransfer.files);
    validateAndAddFile(files);
  };

  const handleInputChange = (file: any) => {
    setConflictedFiles([]);
    const files = Array.from(file);
    validateAndAddFile(files);
  };

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const deleteFile = (index: any) => {
    setDroppedFiles((prevState: any) => {
      const newData = [...prevState];
      newData.splice(index, 1);
      return newData;
    });
  };

  useEffect(() => {
    onFileUpload(droppedFiles);
  }, [droppedFiles]);

  useEffect(() => {
    if (isUploadCancled) {
      setDroppedFiles([]);
      setConflictedFiles([]);
    }
  }, [isUploadCancled]);

  useEffect(() => {
    if (conflictedFiles.length > 0) {
      setBorderColor('border-red-200');
      setTimeout(() => {
        setBorderColor('border-lightgray-100');
      }, 5000);
    }
  }, [conflictedFiles]);

  return (
    <div className="flex size-full flex-col max-h-full">
      <div className="flex h-full w-full flex-col min-h-0">
        <div className="flex-shrink-0 p-2">
          <div
            className={`file-drop-area bg-blue-200/10 rounded-lg border-2 border-dashed ${
              highlighted ? 'highlighted border-blue-400 bg-blue-50' : 'border-blue-200'
            } h-[200px]`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleClick}
          >
            <div className="flex h-full flex-col items-center justify-center space-y-3">
              <img
                className="size-[32px]"
                src="/assets/images/upload_drop.svg"
              />
              <div className="text-center">
                <p className="text-sm font-medium text-blue-600 mb-1">
                  Drag and Drop your Microsoft Excel files here
                </p>
                <p className="text-xs text-gray-500">
                  or click to browse files
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                style={{ display: 'none' }}
                multiple
                accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel.sheet.macroEnabled.12, application/vnd.openxmlformats-officedocument.spreadsheetml.template, application/vnd.ms-excel.template.macroEnabled.12, application/vnd.ms-excel.sheet.binary.macroEnabled.12"
                onChange={(e: any) => {
                  handleInputChange(e.target.files);
                  e.target.value = '';
                }}
              />
            </div>
          </div>
        </div>
        {droppedFiles.length > 0 && (
          <div className="flex-1 overflow-auto p-2 min-h-0">
            <div className="flex flex-wrap gap-2">
              {droppedFiles.map((file: any, index: number) => (
                <div
                  className={`flex h-[36px] min-w-[160px] flex-row items-center justify-between space-x-2 rounded border ${
                    conflictedFiles.includes(file.name)
                      ? borderColor
                      : 'border-lightgray-100'
                  } px-2 bg-white shadow-sm`}
                  key={index}
                >
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <img
                      className="size-[12px] flex-shrink-0"
                      src="/assets/images/dataSet-blue.svg"
                    />
                    <span className="text-xs font-normal text-gray-700 truncate">
                      {file?.name}
                    </span>
                  </div>
                  <img
                    className="size-[14px] cursor-pointer hover:opacity-70 flex-shrink-0"
                    src="/assets/images/delete.svg"
                    onClick={() => deleteFile(index)}
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadFile;
