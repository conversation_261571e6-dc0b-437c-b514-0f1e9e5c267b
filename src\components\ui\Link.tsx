/* eslint-disable no-param-reassign */
/* eslint-disable react/no-danger */
import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const link = cva(
  'text-blue-200 no-underline transition duration-150 ease-in-out',
  {
    variants: {
      intent: {
        enabled: [
          'active:text-blue-200',
          'hover:text-blue-300',
          'hover:border-0',
          'focus:text-blue-400',
          'disabled:text-blue-200/50',
          'cursor-pointer',
        ],
        disabled: ['pointer-events-none', 'text-blue-200/80', 'opacity-90'],
      },
    },
    compoundVariants: [
      {
        intent: 'enabled',
        class: 'smallCase',
      },
    ],
    defaultVariants: {
      intent: 'enabled',
    },
  },
);

export interface LinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof link> {}

export const Link: React.FC<LinkProps> = ({
  className,
  intent,
  content,
  ...props
}) => {
  content = content || '';
  return (
    <a
      className={link({ intent, className })}
      {...props}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};
