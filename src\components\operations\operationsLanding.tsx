'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  ArrowRight
} from 'lucide-react';

const OperationsLanding = () => {
  const router = useRouter();

  const operationalFeatures = [
    {
      id: 'genpro',
      title: 'GenPro',
      description: 'Brokerage Fee Allocation Workflow System',
      longDescription: 'Automated solution for structured ingestion, transformation, and export of financial allocation files with built-in flexibility for manual and automated execution modes.',
      route: '/genpro',
      color: 'bg-blue-500',
      status: 'active',
      subRoutes: [
        { name: 'Workflow', path: '/genpro/workflow' },
        { name: 'Mapping', path: '/genpro/mapping' },
        { name: 'Logs', path: '/genpro/logs' }
      ]
    },
    // Placeholder for future operations
    {
      id: 'coming-soon-1',
      title: 'Coming Soon',
      description: 'Additional operational features will be added here',
      longDescription: 'Future operational workflows and automation tools will be available in upcoming releases.',
      icon: Plus,
      route: null,
      color: 'bg-gray-400',
      status: 'coming-soon',
    }
  ];

  const handleFeatureClick = (feature: any) => {
    if (feature.status === 'active' && feature.route) {
      router.push(feature.route);
    }
  };

  const handleSubRouteClick = (path: string, e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(path);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Operations</h1>
        <p className="text-sm text-gray-600">
          Operational workflows and automation tools for business processes
        </p>
      </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-stretch">
          {operationalFeatures.map((feature) => {
            const isActive = feature.status === 'active';
            const isComingSoon = feature.status === 'coming-soon';

            return (
              <div
                key={feature.id}
                className={`bg-white-200 rounded-lg border border-lightgray-100 p-4 transition-all duration-200 flex flex-col ${
                  isActive
                    ? 'hover:shadow-lg cursor-pointer hover:border-blue-200'
                    : 'opacity-60 cursor-not-allowed'
                }`}
                onClick={() => handleFeatureClick(feature)}
              >
                {/* Card Content - Flex grow to push action button to bottom */}
                <div className="flex-grow">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <h2 className="text-lg font-bold text-gray-800 mb-1">
                          {feature.title}
                        </h2>
                        <p className="text-xs text-gray-600">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                    {isActive && (
                      <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
                    )}
                    {isComingSoon && (
                      <span className="bg-blue-400 text-white-200 text-xs px-2 py-1 rounded-md font-medium border border-blue-200">
                        Coming Soon
                      </span>
                    )}
                  </div>

                  {/* Description */}
                  <div className="mb-3">
                    <p className="text-xs text-gray-700 leading-relaxed">
                      {feature.longDescription}
                    </p>
                  </div>

                  {/* Quick Access Links */}
                  {isActive && feature.subRoutes && (
                    <div className="mb-3">
                      <h3 className="text-xs font-semibold text-gray-800 mb-1">Quick Access:</h3>
                      <ul className="space-y-0.5">
                        {feature.subRoutes.map((subRoute, index) => (
                          <li key={index} className="flex items-center text-xs text-gray-600">
                            <button
                              onClick={(e) => handleSubRouteClick(subRoute.path, e)}
                              className="flex items-center hover:text-blue-600 transition-colors"
                            >
                              <div className="w-1 h-1 rounded-full bg-black mr-2"></div>
                              {subRoute.name}
                            </button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Action Button - Always at bottom */}
                <div className="pt-2 border-t border-gray-100 mt-auto">
                  {isActive ? (
                    <div className="flex items-center justify-between">
                      {/* <span className="text-xs text-gray-600">
                        Click to access {feature.title}
                      </span> */}
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      {/* <span className="text-xs text-gray-500">
                        Feature under development
                      </span>
                      <span className="text-xs text-gray-400">
                        Available soon
                      </span> */}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Info Section */}
        {/* <div className="mt-8 bg-blue-50 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">About Operations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Current Features</h3>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• <strong>GenPro:</strong> Complete brokerage fee allocation workflow</li>
                <li>• Automated data processing and transformation</li>
                <li>• Multi-format export capabilities</li>
                <li>• Comprehensive audit trails and logging</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-700 mb-2">Upcoming Features</h3>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Additional workflow automation tools</li>
                <li>• Enhanced reporting and analytics</li>
                <li>• Custom workflow builders</li>
                <li>• Advanced integration capabilities</li>
              </ul>
            </div>
          </div>
        </div> */}
    </div>
  );
};

export default OperationsLanding;
