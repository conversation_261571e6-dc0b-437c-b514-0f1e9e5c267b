/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable import/no-cycle */
/* eslint-disable jsx-a11y/control-has-associated-label */
/* eslint-disable no-param-reassign */
/* eslint-disable react/jsx-no-bind */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';

import { Checkbox, CheckBoxStates } from './Checkbox';

const TableWithBorder: React.FC<{
  header: any[];
  data: any;
  isEdit?: boolean;
  isView?: boolean;
}> = ({ header, data, isEdit, isView }) => {
  // States
  const [headerCheckBoxState, setHeaderCheckBoxState] = useState(
    CheckBoxStates.Empty,
  );
  const [rows, setRows] = useState(data);

  // Methods
  const handleRowCheckboxChange = (_cellIndex: number, rowIndex: number) => {
    const newRows = rows.map((row: any, index: any) => {
      if (index === rowIndex) {
        row.selected = !row.selected;
      }
      return row;
    });
    setRows(newRows);
  };

  const handleAllCheckboxChange = () => {
    const newRows = rows.map((row: any) => {
      row.selected =
        headerCheckBoxState === CheckBoxStates.Empty ||
        headerCheckBoxState === CheckBoxStates.Indeterminate;
      return row;
    });
    setRows(newRows);
  };

  const renderHeader = () => {
    return (
      <tr className="">
        {header.map((item: any, index: number) => (
          <th className="border-x-[1px] border-lightgray-100 p-2" key={index}>
            <div className="flex flex-row items-center justify-start space-x-3">
              {item.inputType === 'checkbox' && (
                <Checkbox
                  value={headerCheckBoxState}
                  onChange={() => handleAllCheckboxChange()}
                  disabled={item.disable || isView}
                />
              )}
              {item.inputType === 'text' && (
                <span className="font-sans text-sm font-normal uppercase leading-4 text-gray-400">
                  {item.title}
                </span>
              )}
            </div>
          </th>
        ))}
        {isEdit && <th />}
        {isView && <th />}
      </tr>
    );
  };

  const renderValueCell = (
    isSelected: boolean,
    cellData: any,
    index: number,
    rowIndex: number,
  ) => {
    return (
      <td key={index} className="border-[1px] border-lightgray-100 py-2">
        <div className="flex flex-row items-center justify-start space-x-3 px-2">
          {cellData.type === 'checkbox' && (
            <Checkbox
              value={isSelected ? CheckBoxStates.Checked : CheckBoxStates.Empty}
              onChange={() => handleRowCheckboxChange(index, rowIndex)}
              disabled={cellData.disable || isView}
            />
          )}
          {cellData.value !== '' && cellData.type === 'text' && (
            <span className=" font-sans text-sm font-semibold capitalize leading-4 tracking-[0.2px] text-gray-500">
              {cellData.value}
            </span>
          )}
        </div>
      </td>
    );
  };

  // Effects
  useEffect(() => {
    setRows(data);
  }, [data]);

  useEffect(() => {
    let newCheckBoxState;
    let selected = 0;
    let unSelected = rows.length;
    rows.forEach((row: any) => {
      if (row.selected) selected += 1;
      else unSelected -= 1;
    });
    if (selected === rows.length) {
      newCheckBoxState = CheckBoxStates.Checked;
    } else if (unSelected === 0) {
      newCheckBoxState = CheckBoxStates.Empty;
    } else {
      newCheckBoxState = CheckBoxStates.Indeterminate;
    }
    setHeaderCheckBoxState(newCheckBoxState);
  }, [rows]);

  return (
    <div className=" h-full overflow-auto  border-[1px] border-lightgray-100 ">
      <table className="w-full table-auto divide-y-[1px] divide-lightgray-100">
        <thead className="sticky top-0 z-10 w-full bg-gray-300 ">
          {renderHeader()}
        </thead>
        <tbody className="">
          {rows.map((rowData: any, rowIndex: any) => (
            <tr key={rowIndex}>
              {rowData.components.map((cellData: any, cellIndex: number) =>
                renderValueCell(
                  rowData.selected,
                  cellData,
                  cellIndex,
                  rowIndex,
                ),
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
export default TableWithBorder;
