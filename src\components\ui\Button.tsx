import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

const button = cva(' rounded px-6 py-3 text-center', {
  variants: {
    intent: {
      primary: [
        'bg-blue-200',
        'border-transparent',
        'text-white-100',
        'text-sm',
        'leading-4',
        'font-bold',
        'active:bg-blue-200',
        'hover:bg-blue-300',
        'focus:bg-blue-400',
        'disabled:bg-blue-200/50',
      ],
      secondary: [
        'bg-transparent',
        'text-gray-400',
        'text-sm',
        'leading-4',
        'border',
        'font-bold',
        'border-lightgray-100',
        'active:text-gray-400',
        'active:bg-transparent',
        'hover:text-gray-600',
        'focus:text-gray-700',
        // 'focus:bg-lightgray-200',
        // 'focus:border-lightgray-300',
        'disabled:border-lightgray-100/50',
        'disabled:text-gray-400/50',
      ],
    },
  },
  compoundVariants: [
    {
      intent: 'primary',
      class: 'smallCase',
    },
  ],
  defaultVariants: {
    intent: 'primary',
  },
});

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof button> {}

export const Button: React.FC<ButtonProps> = ({
  className,
  intent,
  ...props
}) => (
  <button type="submit" className={button({ intent, className })} {...props} />
);
