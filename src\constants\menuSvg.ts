export const downloadMenuTitle: string = `<div style="
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
">
<img src='/assets/images/download.svg'>
</div>`;

export const createMenuName = (name: string): string => `
<div style="
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
">
  <img src='/assets/images/add.svg'>
  <span style="
    font-family: OpenSans, sans;
    font-size: 0.875rem;
    font-weight: 700;
    line-height: 1rem;
    color:#2F80ED;
    margin-left:9px;
  ">
    ${name}
  </span>
</div>`;

export const createMenuNameForUpload = (name: string): string =>
  `<div style="
display: flex;
flex-direction: row;
align-items: center;
cursor: pointer;
">
<img src='/assets/images/upload.svg'>
<span style="
font-family: OpenSans, sans;
font-size: 0.875rem;
font-weight: 700;
line-height: 1rem;
color:#2F80ED;
margin-left:9px;
" >
${name}
</span>
</div>`;

export const surveyCompletedPage: string = `<div class="h-[50vh] w-full items-center flex justify-center">  <span style="
font-family: OpenSans, sans;
font-size: 24px;
font-weight: 700;
line-height: 1rem;
" >
Please Relax while we are configuring your system!
</span></div>`;

export const notificationIcon = (strokeColor: any) => `
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.02 2.91C8.71 2.91 6.02 5.6 6.02 8.91V11.8C6.02 12.41 5.76 13.34 5.45 13.86L4.3 15.77C3.59 16.95 4.08 18.26 5.38 18.7C9.69 20.14 14.34 20.14 18.65 18.7C19.86 18.3 20.39 16.87 19.73 15.77L18.58 13.86C18.28 13.34 18.02 12.41 18.02 11.8V8.91C18.02 5.61 15.32 2.91 12.02 2.91Z"
        stroke="${strokeColor}"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
      />
      <path
        d="M13.87 3.2C13.56 3.11 13.24 3.04 12.91 3C11.95 2.88 11.03 2.95 10.17 3.2C10.46 2.46 11.18 1.94 12.02 1.94C12.86 1.94 13.58 2.46 13.87 3.2Z"
        stroke="${strokeColor}"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.02 19.06C15.02 20.71 13.67 22.06 12.02 22.06C11.2 22.06 10.44 21.72 9.9 21.18C9.36 20.64 9.02 19.88 9.02 19.06"
        stroke="${strokeColor}"
        stroke-width="1.5"
        stroke-miterlimit="10"
      />
    </svg>
  `;

export const crossIcon = (
  strokeColor: any,
) => `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M14.115 12L19.05 7.065C19.335 6.795 19.5 6.42 19.5 6C19.5 5.175 18.825 4.5 18 4.5C17.58 4.5 17.205 4.665 16.935 4.935L12 9.885L7.065 4.935C6.795 4.665 6.42 4.5 6 4.5C5.175 4.5 4.5 5.175 4.5 6C4.5 6.42 4.665 6.795 4.935 7.065L9.885 12L4.95 16.935C4.665 17.205 4.5 17.58 4.5 18C4.5 18.825 5.175 19.5 6 19.5C6.42 19.5 6.795 19.335 7.065 19.065L12 14.115L16.935 19.05C17.205 19.335 17.58 19.5 18 19.5C18.825 19.5 19.5 18.825 19.5 18C19.5 17.58 19.335 17.205 19.065 16.935L14.115 12Z" fill=${strokeColor} />
  </svg>`;
