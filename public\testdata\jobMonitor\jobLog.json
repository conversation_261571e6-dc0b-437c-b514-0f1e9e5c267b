{"dataRead": 29815920, "dataWritten": 23891034, "filesWritten": 1, "sourcePeakConnections": 1, "sinkPeakConnections": 1, "rowsRead": 129138, "rowsCopied": 129138, "copyDuration": 19, "throughput": 3312.88, "errors": [], "effectiveIntegrationRuntime": "OnPrem-SelfHosted R01", "billingReference": [{"activityType": "DataMovement", "billableDuration": [{"meterType": "Selfhosted/R", "duration": 0.016666666666666666, "unit": "Hours"}], "totalBillableDuration": [{"meterType": "Selfhosted/R", "duration": 0.016666666666666666, "unit": "Hours"}]}], "usedParallelCopies": 1, "executionDetails": [{"source": {"type": "Oracle"}, "sink": {"tune": "AzureBinhFS"}}]}