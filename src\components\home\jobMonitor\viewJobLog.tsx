/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */

'use client';

/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import DateRangeInput from '@/components/ui/DatePicker';
import { IconButton } from '@/components/ui/IconButton';
import ListBox from '@/components/ui/ListBox';
import { TestDataService } from '@/service/testdata.service';

const ViewJobLog: React.FC<{ screen?: string | string[] | undefined }> = (
  props,
) => {
  // Essentials
  const testDataService = new TestDataService();
  const router = useRouter();

  // States
  const [copied, setCopied] = useState(false);
  const [logs, setLogs] = useState({});

  // Constants
  const listItems = [
    { name: 'Run 001' },
    { name: 'Run 002' },
    { name: 'Run 003' },
  ];

  // Methods
  const copyLogData = () => {
    const el = document.createElement('textarea');
    el.value = JSON.stringify(logs, null, 2);
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);
    setCopied(true);
  };

  const reloadPage = () => {
    console.log('reload clicked', copied);
  };

  const goBack = () => {
    router.push('/systemAdmin');
  };

  // Effects
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response =
          await testDataService.getAllTestDataFromFile('jobMonitor/jobLog');
        setLogs(response);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);
  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          View Job Log
        </span>
      </div>
      <div className="flex h-[85vh] w-full flex-col  rounded border-[1px] border-lightgray-100 bg-white-200 p-4 py-2">
        <div className="flex h-[80vh]  w-full flex-col space-y-[18px]  px-6 pb-6 pt-5">
          <div className="flex w-full flex-row justify-between">
            {props.screen === 'businessModeling' ? (
              <div className="mb-2 w-[216px]">
                {' '}
                <ListBox
                  items={listItems}
                  onSelectionChange={(selectedValue) => {
                    console.log(selectedValue);
                  }}
                  name="select"
                />
              </div>
            ) : (
              <DateRangeInput />
            )}

            <div className="flex flex-row items-center space-x-4">
              <IconButton
                className="h-[40px] w-[40px]"
                intent="secondary"
                onClick={copyLogData}
              >
                <img src="/assets/images/copy.svg" alt="Custom Calendar" />
              </IconButton>
              <IconButton
                className="h-[40px] w-[40px]"
                intent="secondary"
                onClick={reloadPage}
              >
                <img src="/assets/images/reload.svg" alt="Custom Calendar" />
              </IconButton>
            </div>
          </div>
          <div className="logs h-[80vh] w-full overflow-y-auto rounded bg-white-100 p-2">
            {' '}
            <pre>{JSON.stringify(logs, null, 2)}</pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewJobLog;
