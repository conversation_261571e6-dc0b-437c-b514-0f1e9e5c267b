/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import moment from 'moment';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { LocalService } from '@/service/local.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';

const NotificationBox = () => {
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const notification = useSelector(
    (state: RootState) => state.metadata.notification,
  );
  const [notifications, setNotifications] = useState<any>([]);

  const handleClick = (id: number) => {
    // Logic to handle card click and navigate to the page
    console.log(`Card with ID ${id} clicked`);
  };

  const navigateToFilePreview = (fileName: string, detail: any) => {
    if (detail.status === 'FILE_PROCESSING_SUCCEEDED') {
      const fileData: any = {
        'File Name': detail.originalFileName,
        'Business Segment': detail.country ? detail.country : '-',
        'Dataset Name': detail.datasetName ? detail.datasetName : '-',
        Year: Number(detail.year) > 0 ? detail.year : 'N/A',
        Quarter: Number(detail.quarter) > 0 ? detail.quarter : 'N/A',
        Month: Number(detail.month) > 0 ? detail.month : 'N/A',
        // 'Uploaded By': detail.uploadedBy,
        'Uploaded Date': detail.uploadedAt,
      };
      localService.setItem('fileDetailForPreview', JSON.stringify(fileData));
      const parameter: any = {
        fileId: detail.id,
        name: fileName,
      };
      const queryString = new URLSearchParams(parameter).toString();
      router.push(`/fileUpload/filePreview?${queryString}`);
    } else {
      dispatch(
        setToastAlert({
          isToastOpen: true,
          intent: 'error',
          title: 'Error',
          content: 'Unable to display the file preview.',
        }),
      );
    }
  };

  useEffect(() => {
    if (notification.length > 0) {
      const sortedNotifications: any = [...notification].sort(
        (a, b) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      );
      setNotifications(sortedNotifications.slice().reverse());
    }
  }, [notification]);

  return (
    <div className="h-full w-full overflow-auto ">
      {notifications.length > 0 ? (
        <div className="flex flex-col">
          {notifications.map((notificationInfo: any) => (
            <div
              key={notificationInfo.id}
              className="my-1 max-w-[350px] rounded bg-slate-100"
              onClick={() => handleClick(notificationInfo.id)}
            >
              <p className="p-2 pb-0 text-xs text-gray-400">
                File{' '}
                <span
                  className="cursor-pointer text-blue-200"
                  onClick={() =>
                    navigateToFilePreview(
                      notificationInfo.fileName,
                      notificationInfo.fileDetail,
                    )
                  }
                >
                  {notificationInfo.fileName}
                </span>{' '}
                {notificationInfo.text}
              </p>
              <div className="flex justify-end p-2 pb-1 text-xxxs text-gray-400">
                {moment(notificationInfo.timestamp).fromNow()}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex h-[calc(100vh-300px)] flex-col  items-center justify-center">
          <img src="/assets/images/empty.svg" alt="No data" />

          <div className="flex w-full flex-col items-center justify-center">
            <span className="text-center font-sans text-sm font-normal text-gray-400 ">
              No Notification
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBox;
