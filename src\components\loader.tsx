import React from 'react';

interface PageLoaderProps {
  isLoading: boolean;
  message: string;
}

const PageLoader: React.FC<PageLoaderProps> = ({ isLoading, message }) => {
  return (
    isLoading && (
      <div className="page-loader flex flex-col space-y-6">
        <div className="loader" />
        <span className="font-sans text-base font-normal leading-5 text-gray-900">
          {message}
        </span>
      </div>
    )
  );
};

export default PageLoader;
