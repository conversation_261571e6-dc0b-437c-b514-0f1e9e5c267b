'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Tooltip } from 'react-tooltip';

import { Button } from '@/components/ui/Button';
import ListBoxTable from '@/components/ui/ListBoxTable';
import Table from '@/components/ui/Table';
import { monthList, quarterList, yearList } from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import { setIsLoading } from '@/slices/appSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

const ReportFreshnessPendingFiles: React.FC = () => {
  // essentials
  const searchParams = useSearchParams();
  const preSelectedYear: any = searchParams?.get('year');
  const name: any = searchParams?.get('name');
  const id: any = searchParams?.get('id');
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  // States
  const [filters] = useState<any>({
    selectedYear: preSelectedYear || 'All',
    selectedMonth: 'All',
    selectedQuarter: 'All',
    selectedSource: 'All',
  });
  const [source, setSource] = useState<any>([]);
  const [frequency, setFrequency] = useState('');
  const [selectedSource, setSelectedSource] = useState(
    filters?.selectedSource ? filters?.selectedSource : 'All',
  );
  const [selectedYear, setSelectedYear] = useState<any>(
    filters?.selectedYear ? filters?.selectedYear : 'All',
  );
  const [selectedMonth, setSelectedMonth] = useState<any>(
    filters?.selectedMonth ? filters?.selectedMonth : 'All',
  );
  const [selectedQuarter, setSelectedQuarter] = useState<any>(
    filters?.selectedQuarter ? filters?.selectedQuarter : 'All',
  );
  const [year] = useState<any>([{ name: 'All' }, ...yearList]);
  const [month] = useState<any>([{ name: 'All' }, ...monthList]);
  const [quarter] = useState<any>([{ name: 'All' }, ...quarterList]);
  const [filteredData, setFilteredData] = useState<any>([]);
  const [allData, setAllData] = useState<any>([]);
  const header: any = [
    {
      title: 'Dataset Name',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Business Segment',
      optionsEnabled: true,
      options: [],
      width: 'min-w-[150px]',
    },
    {
      title: 'Year',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Quarter',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Month',
      optionsEnabled: true,
      options: ['sort'],
    },
  ];

  const data: any = {
    values: allData,
    enableOptions: false,
  };

  // Constants

  // Methods
  const goBack = () => {
    router.back();
  };

  const applyFilter = (tableData: any) => {
    if (tableData.length !== 0) {
      const tempSelectedSource: any =
        selectedSource === 'All' ? '' : selectedSource;
      const tempSelectedYear: any = selectedYear === 'All' ? '' : selectedYear;
      const tempSelectedQuarter: any =
        selectedQuarter === 'All' ||
        frequency === 'Monthly' ||
        frequency === 'Yearly'
          ? ''
          : selectedQuarter;
      const tempSelectedMonth: any =
        selectedMonth === 'All' ||
        frequency === 'Quarterly' ||
        frequency === 'Yearly'
          ? ''
          : selectedMonth;
      const filter: { [key: number]: any } = {};
      if (tempSelectedSource !== '')
        filter[0] = tempSelectedSource === ' ' ? '' : tempSelectedSource;
      if (tempSelectedYear !== '')
        filter[2] = Number(tempSelectedYear)
          ? Number(tempSelectedYear)
          : tempSelectedYear;
      if (tempSelectedQuarter !== '')
        filter[3] = Number(tempSelectedQuarter)
          ? Number(tempSelectedQuarter)
          : tempSelectedQuarter;
      if (tempSelectedMonth !== '')
        filter[4] = Number(tempSelectedMonth)
          ? Number(tempSelectedMonth)
          : tempSelectedMonth;
      const tempFilteredData = allData.filter((item: any) =>
        Object.entries(filter).every(
          ([key, value]) => item.components[key].value === value,
        ),
      );
      setFilteredData(tempFilteredData);
    } else {
      setFilteredData([]);
    }
  };

  const resetFilter = () => {
    setFrequency('All');
    setSelectedSource('All');
    setSelectedMonth('All');
    setSelectedYear('All');
    setSelectedQuarter('All');
    setFilteredData(allData);
  };

  useEffect(() => {
    if (filteredData.length > 0) {
      setFilteredData(filteredData);
    }
  }, [filteredData]);

  const resolveData = (fileData: any) => {
    if (!fileData || !Array.isArray(fileData) || fileData.length === 0) {
      return [];
    }
    return fileData.map((row) => {
      const components = [
        {
          value: row.dataset ? row.dataset : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.businessSegment ? row.businessSegment : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.year) > 0 ? Number(row.year) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.quarter) > 0 ? Number(row.quarter) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.month) > 0 ? Number(row.month) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
      ];
      return {
        id: row.dataset,
        components,
      };
    });
  };

  useEffect(() => {
    const fetchTableData = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsm +
          ApiUtilities.apiPath.getReportFreshnessPendingDatasets.url
        }/${id}`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const formattedData: any = resolveData(res.data.missingFilesInfo);
              setAllData(formattedData);
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    fetchTableData();
  }, [id]);

  const extractIdAndName = (sourceData: any) => {
    return sourceData.map((item: any) => {
      return {
        id: item.id,
        name: item.datasetName,
        frequency: item.frequency,
      };
    });
  };

  const findFrequency = (reportName: any, listData: any) => {
    for (const report of listData) {
      if (report.name.toLowerCase() === reportName.toLowerCase()) {
        return report.frequency;
      }
    }
    return null;
  };

  useEffect(() => {
    if (selectedSource && source.length !== 0) {
      const freq: any = findFrequency(selectedSource, source);
      setFrequency(freq || 'All');
    }
  }, [selectedSource, source]);

  useEffect(() => {
    const fetchSource = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsm +
          ApiUtilities.apiPath.getBsmDataSets.url
        }`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const transformedData: any = extractIdAndName(res.data);
              transformedData.push({ id: 0, name: 'All', frequency: 'All' });
              setSource(transformedData.slice().reverse());
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    fetchSource();
  }, []);

  useEffect(() => {
    if (allData.length > 0) {
      setFilteredData(allData);
    }
  }, [allData]);

  useEffect(() => {
    if (preSelectedYear && allData.length > 0) {
      applyFilter(allData);
    }
  }, [preSelectedYear, allData]);

  return (
    <div className="flex w-full flex-col space-y-4">
      <div
        className="flex w-fit flex-row items-center space-x-2"
        onClick={goBack}
      >
        <Image
          className="cursor-pointer"
          src="/assets/images/arrow-left.svg"
          width={24}
          height={24}
          alt="back"
        />
        <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
          {name || 'View Pending Files'}
        </span>
      </div>
      <div className="flex h-[calc(100vh-120px)] w-full flex-col rounded border border-lightgray-100 bg-white-200 p-4">
        <div className="flex w-full items-end justify-between px-2 py-3">
          <div className="flex flex-row space-x-4">
            <div className="flex flex-col space-y-1">
              <span className="font-sans text-sm font-semibold text-gray-500">
                Select Dataset
              </span>
              <div className="mt-3 w-[20vw] rounded border border-lightgray-100 py-1">
                <ListBoxTable
                  placeholder="Select Dataset"
                  isInTable
                  items={source}
                  selectedValue={selectedSource}
                  hasValue
                  onSelectionChange={(selectedValue) => {
                    if (selectedValue.name === '') {
                      setSelectedSource(' ');
                    } else {
                      setSelectedSource(selectedValue.name);
                    }
                    if (frequency === 'Quarterly' || frequency === 'Yearly') {
                      setSelectedMonth('All');
                    } else if (
                      frequency === 'Monthly' ||
                      frequency === 'Yearly'
                    ) {
                      setSelectedQuarter('All');
                    }
                  }}
                  name="select"
                />
              </div>
            </div>
            <div className="flex flex-col space-y-1">
              <span className="font-sans text-sm font-semibold text-gray-500">
                Select Year
              </span>
              <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
                <ListBoxTable
                  isInTable
                  hasValue
                  items={year}
                  placeholder="Select Year"
                  selectedValue={selectedYear}
                  onSelectionChange={(selectedValue) => {
                    setSelectedYear(selectedValue.name);
                  }}
                  name="year"
                />
              </div>
            </div>
            {(frequency === 'All' || frequency === 'Monthly') && (
              <div className="flex flex-col space-y-1">
                <span className="font-sans text-sm font-semibold text-gray-500">
                  Select Month
                </span>
                <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
                  <ListBoxTable
                    isInTable
                    hasValue
                    items={month}
                    placeholder="Select Month"
                    selectedValue={selectedMonth}
                    onSelectionChange={(selectedValue) => {
                      setSelectedMonth(selectedValue.name);
                    }}
                    name="month"
                  />
                </div>
              </div>
            )}
            {(frequency === 'All' || frequency === 'Quarterly') && (
              <div className="flex flex-col space-y-1">
                <span className="font-sans text-sm font-semibold text-gray-500">
                  Select Quarter
                </span>
                <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
                  <ListBoxTable
                    isInTable
                    hasValue
                    items={quarter}
                    placeholder="Select Quarter"
                    selectedValue={selectedQuarter}
                    onSelectionChange={(selectedValue) => {
                      setSelectedQuarter(selectedValue.name);
                    }}
                    name="quarter"
                  />
                </div>
              </div>
            )}
          </div>
          <div className=" flex flex-row space-x-2">
            <Button intent="primary" onClick={() => applyFilter(allData)}>
              Apply
            </Button>
            <Button intent="secondary" onClick={resetFilter}>
              Reset
            </Button>
          </div>
        </div>
        {filteredData && (
          <div className="relative h-[calc(100vh-200px)] w-full overflow-auto">
            <Table
              hasPagination
              isDashTable
              header={header}
              data={filteredData}
              enableOptions={data.enableOptions}
            />
          </div>
        )}
      </div>
      <Tooltip
        className="info-tooltip"
        id="download-tooltip"
        place="left"
        variant="info"
        positionStrategy="fixed"
      />
    </div>
  );
};

export default ReportFreshnessPendingFiles;
