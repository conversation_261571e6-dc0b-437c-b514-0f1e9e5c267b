export const currentEnv: string = 'prod';
export const sidebarJsonDataAdmin = [
  {
    name: 'Home',
    route: '/home',
    activeIcon: 'home-active.svg',
    inActiveIcon: 'home.svg',
    hasDropdown: false,
    dropDownData: [],
    tour: 'home',
  },
  {
    name: 'Datasets',
    activeIcon: 'dataset-active.svg',
    route: '/datasetDashboard',
    inActiveIcon: 'dataset.svg',
    tour: 'dataset',
    hasDropdown: false,
  },
  {
    name: 'Business Views',
    activeIcon: 'referenceData-active.svg',
    route: '/businessViews',
    inActiveIcon: 'referenceData.svg',
    tour: 'business-views',
    hasDropdown: false,
  },
  {
    name: 'File Upload',
    route: '/fileUpload?home=true',
    activeIcon: 'upload-new-file-active.svg',
    inActiveIcon: 'upload-new-file.svg',
    hasDropdown: false,
    dropDownData: [],
    tour: 'upload-file',
  },
  // {
  //   name: 'Data Completeness',
  //   route: '/dataCompletenessDashboard',
  //   activeIcon: 'dataCompleteness-active.svg',
  //   inActiveIcon: 'dataCompleteness.svg',
  //   hasDropdown: false,
  //   dropDownData: [],
  //   tour: 'data-completeness-dashboard',
  // },
  {
    name: 'Data Quality',
    route: '/dataQuality',
    activeIcon: 'dataCompleteness-active.svg',
    inActiveIcon: 'dataCompleteness.svg',
    hasDropdown: false,
    dropDownData: [],
    tour: 'data-quality-dashboard',
  },
  {
    name: 'Operations',
    route: '/operations',
    activeIcon: 'operations-new.svg',
    inActiveIcon: 'operations-new.svg',
    hasDropdown: false,
    dropDownData: [],
    tour: 'operations',
  },
  // {
  //   name: 'GenPro',
  //   route: '/genpro',
  //   activeIcon: 'dataset-active.svg',
  //   inActiveIcon: 'dataset.svg',
  //   hasDropdown: false,
  //   dropDownData: [],
  //   tour: 'genpro',
  // },
];

export const sidebarJsonDataCFO = [
  {
    name: 'Home',
    route: '/home',
    activeIcon: 'home-active.svg',
    inActiveIcon: 'home.svg',
    hasDropdown: false,
    dropDownData: [],
    tour: 'home',
  },
];

export const sidebarRoutes = {
  Home: ['/home', '/insight', '/model-flow', '/conceptual-model'],
  // 'Data Completeness': ['/dataCompletenessDashboard'],
  'Business Views': ['/businessViews'],
  'Data Quality': ['/dataQuality'],
  'File Upload': ['/fileUpload', '/fileUpload/filePreview'],
  'System Integration': [
    '/systemIntegration',
    '/systemIntegration/addNewOnlineConnection',
    '/systemIntegration/viewLog',
  ],
  Datasets: ['/datasetDashboard', '/datasets/datasetDetails'],
  'Logical Entities': ['/logicalEntities', '/logicalEntities/addNewEntity'],
  'Business Modelling': [
    '/businessModelling',
    '/businessModelling/addNewBusinessModel',
    '/businessModelling/viewDataLog',
    '/businessModelling/flow',
    '/businessModelling/viewLog',
  ],
  // 'User Management': ['/userManagement', '/userManagement/addNewUser'],
  'WCO-OTC': '/ar-insights',
  'WCO-PTP': '/ap-insights',
  Enterprise: '/insights/enterprise',
  'Customer Focus': '/insights/customerFocus',
  'Supplier Focus': '/insights/supplierFocus',
  'Financial Focus': '/insights/financialFocus',
  'Data Collaboration': [
    '/dataCollaboration',
    '/dataCollaboration/addNewCollab',
    '/dataCollaboration/viewCollab',
  ],
  'Configuration Cockpit': ['/configurationCockpit'],
  'System Admin': [
    '/systemAdmin',
    '/jobMonitor/viewJobStatus',
    '/jobMonitor/viewJobLog',
    '/userManagement',
    '/userManagement/addNewUse',
    'dataQualityDashboard',
    '/dataQualityDashboard/viewErrorLog',
  ],
  'Operations': ['/operations'],
  'GenPro': ['/genpro'],
};

export const imagePaths = [
  '/assets/images/sidebar/dataset-active.svg',
  '/assets/images/sidebar/dataset.svg',
  '/assets/images/sidebar/logicalEntity-active.svg',
  '/assets/images/sidebar/logicalEntity.svg',
  '/assets/images/sidebar/enterprise.svg',
  '/assets/images/sidebar/enterprise-active.svg',
  '/assets/images/sidebar/ar-active.svg',
  '/assets/images/sidebar/ar.svg',
  '/assets/images/sidebar/ap.svg',
  '/assets/images/sidebar/ap-active.svg',
  '/assets/images/sidebar/financial-active.svg',
  '/assets/images/sidebar/financial.svg',
  '/assets/images/sidebar/operations-new.svg',
];

export const selectListForUploadDatasets = [
  { id: '00', name: 'All' },
  { id: '01', name: 'Consolidated Variance Report' },
  { id: '02', name: 'YTD Consolidated Report before group cost allocation' },
  { id: '03', name: 'YTD Consolidated Report after group cost allocation' },
  { id: '04', name: 'YTD Consolidated Report additional information' },
  { id: '05', name: 'YTD Net profitability report' },
  { id: '06', name: 'Vessel under management report' },
  { id: '07', name: 'YTD Management Fee' },
  { id: '08', name: 'YTD Crew Lumpsum Fee' },
  { id: '09', name: 'YTD Termination fees' },
  { id: '10', name: 'Designation Mapping' },
  { id: '11', name: 'HR shore tile' },
  { id: '12', name: 'Revaluation report' },
  { id: '13', name: 'Budget Management Fees' },
  { id: '14', name: 'Group Cost Allocations Map' },
  { id: '15', name: 'Budget Consolidated Report before group cost allocation' },
  { id: '16', name: 'Budget Consolidated Report after group cost allocation' },
  { id: '17', name: 'Budget Consolidated Report additional information' },
  { id: '18', name: 'Budget Crew Lumpsum Fees' },
  { id: '19', name: 'Budget Termination Fees' },
  {
    id: '20',
    name: 'Forecast Consolidated Report before group cost allocation',
  },
];

export const quarterList = [
  { name: 'N/A' },
  { name: '1' },
  { name: '2' },
  { name: '3' },
  { name: '4' },
];

export const monthList = [
  { name: 'N/A' },
  { name: '1' },
  { name: '2' },
  { name: '3' },
  { name: '4' },
  { name: '5' },
  { name: '6' },
  { name: '7' },
  { name: '8' },
  { name: '9' },
  { name: '10' },
  { name: '11' },
  { name: '12' },
];

export const yearList = [
  { name: 'N/A' },
  { name: '2022' },
  { name: '2023' },
  { name: '2024' },
  { name: '2025' },
];

export const alreadyExistsList = [{ name: 'Discard' }, { name: 'Replace' }];

export const conflictedFilesList = [{ name: 'Discard' }, { name: 'Accept' }];

export const countryListItems = [
  {
    id: 0,
    name: 'N/A',
  },
  {
    id: 1,
    name: 'Singapore',
  },
  {
    id: 2,
    name: 'Cyprus',
  },
  {
    id: 3,
    name: 'Deutschland',
  },
  {
    id: 4,
    name: 'India',
  },
  {
    id: 5,
    name: 'Hong Kong',
  },
  {
    id: 6,
    name: 'China',
  },
  {
    id: 7,
    name: 'Hellas',
  },
  {
    id: 8,
    name: 'British Isles',
  },
  {
    id: 9,
    name: 'Cruise',
  },
  {
    id: 10,
    name: 'Mexico',
  },
  {
    id: 11,
    name: 'Pronav',
  },
  {
    id: 12,
    name: 'Yachting',
  },
  {
    id: 13,
    name: 'YPI CREW',
  },
];

export const codeString: string = `
function createStyleObject(classNames: string[], style: any): any {
  return classNames.reduce((styleObject, className) => {
    return {...styleObject, ...style[className]};
  }, {});
}

function createClassNameString(classNames: string[]): string {
  return classNames.join(' ');
}

function createChildren(style: any, useInlineStyles: boolean) {
  let childrenCount = 0;
  return (children: any[]) => {
    childrenCount += 1;
    return children.map((child, i) => createElement({
      node: child,
      style,
      useInlineStyles,
      key: \`code-segment-\${childrenCount}-\${i}\`
    }));
  }
}

function createElement({ node, style, useInlineStyles, key }: { 
  node: any; 
  style: any; 
  useInlineStyles: boolean; 
  key: string;
}): any {
  const { properties, type, tagName, value } = node;
  if (type === "text") {
    return value;
  } else if (tagName) {
    const TagName = tagName;
    const childrenCreator = createChildren(style, useInlineStyles);
    const props = (
      useInlineStyles
      ? { style: createStyleObject(properties.className, style) }
      : { className: createClassNameString(properties.className) }
    );
    const children = childrenCreator(node.children);
    return <TagName key={key} {...props}>{children}</TagName>;
  }
}
`;

export const monthStringList: any = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const quarterStringList: any = [
  'Forecast 1',
  'Forecast 2',
  'Forecast 3',
];

export const frequencylist: any = [
  { name: 'Monthly' },
  { name: 'Quarterly' },
  { name: 'Yearly' },
];

const startYear = 2022;
const currentYear = new Date().getFullYear();

export const dqYearList = Array.from(
  { length: currentYear - startYear + 1 },
  (_, i) => ({ name: (startYear + i).toString() }),
);
