'use client';

import React, { useState } from 'react';
import { 
  Save, 
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Toggle } from '@/components/ui/Toggle';

const GenProConfiguration = () => {

  const [config, setConfig] = useState({
    // System Settings
    autoProcessing: true,
    emailNotifications: true,
    retentionDays: 90,
    maxFileSize: 50,
    
    // Processing Rules
    defaultExecutionMode: 'manual',
    validationStrictness: 'high',
    allowUnknownEntities: false,
    autoMapSolarToCyp: true,
    
    // Email Settings
    smtpServer: 'smtp.bsm.com',
    smtpPort: 587,
    fromEmail: '<EMAIL>',
    adminEmail: '<EMAIL>',
    
    // Storage Settings
    s3Bucket: 'genpro-data',
    s3Region: 'eu-west-1',
    backupEnabled: true,
    compressionEnabled: true,
    
    // Business Rules
    seachefSplitPercentage: 50,
    frontlineExclusion: true,
    bsVesselsHandling: 'special',
    taxCalculationRate: 12.5
  });

  const [hasChanges, setHasChanges] = useState(false);

  const handleConfigChange = (key: string, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = () => {
    alert('Configuration saved successfully!');
    setHasChanges(false);
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset to default settings?')) {
      // Reset logic would go here
      setHasChanges(false);
    }
  };

  const configSections = [
    {
      title: 'Business Rules',
      color: 'bg-purple-500',
      settings: [
        {
          key: 'seachefSplitPercentage',
          label: 'SEACHEF Split Percentage',
          description: 'Percentage split for SEACHEF allocations',
          type: 'number',
          value: config.seachefSplitPercentage,
          suffix: '%'
        },
        {
          key: 'frontlineExclusion',
          label: 'Exclude Frontline from Distribution',
          description: 'Exclude Frontline from percentage-based distribution calculations',
          type: 'toggle',
          value: config.frontlineExclusion
        },
        {
          key: 'taxCalculationRate',
          label: 'Tax Calculation Rate',
          description: 'Tax rate for estimated tax calculations',
          type: 'number',
          value: config.taxCalculationRate,
          suffix: '%',
          step: 0.1
        }
      ]
    }
  ];

  const renderSetting = (setting: any) => {
    switch (setting.type) {
      case 'toggle':
        return (
          <Toggle
            checked={setting.value}
            onChange={(value) => handleConfigChange(setting.key, value)}
          />
        );
      case 'number':
        return (
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              value={setting.value}
              onChange={(e) => handleConfigChange(setting.key, Number(e.target.value))}
              className="w-24"
              step={setting.step || 1}
            />
            {setting.suffix && (
              <span className="text-sm text-gray-500">{setting.suffix}</span>
            )}
          </div>
        );
      case 'select':
        return (
          <select
            value={setting.value}
            onChange={(e) => handleConfigChange(setting.key, e.target.value)}
            className="border border-gray-300 rounded px-3 py-2 text-sm"
          >
            {setting.options?.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      default:
        return (
          <Input
            value={setting.value}
            onChange={(e) => handleConfigChange(setting.key, e.target.value)}
          />
        );
    }
  };

  return (
    <div className="h-screen flex flex-col bg-white">
      <div className="px-6 py-4 bg-white border-b border-gray-200">
        <h1 className="text-xl font-bold text-gray-800">
          Configuration
        </h1>
      </div>

      <div className="flex-1 flex flex-col px-6 py-4 overflow-hidden">
        {/* Save Banner */}
        {hasChanges && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <div>
                  <h3 className="font-semibold text-yellow-800">Unsaved Changes</h3>
                  <p className="text-sm text-yellow-700">
                    You have unsaved configuration changes. Save to apply them.
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button intent="secondary" onClick={handleReset}>
                  Reset
                </Button>
                <Button onClick={handleSave} className='flex justify-center items-center'>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Configuration Sections */}
        <div className="flex-1 bg-white overflow-hidden">
          <div className="space-y-4">
            {configSections.map((section, sectionIndex) => {
              return (
                <div
                  key={sectionIndex}
                  className="bg-white-200 rounded-lg border border-lightgray-100 p-4"
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <h2 className="text-base font-semibold text-gray-800">{section.title}</h2>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {section.settings.map((setting, settingIndex) => (
                      <div key={settingIndex} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <label className="text-sm font-medium text-gray-700">
                              {setting.label}
                            </label>
                            <p className="text-xs text-gray-500 mt-1">
                              {setting.description}
                            </p>
                          </div>
                          <div className="ml-4">
                            {renderSetting(setting)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>

          {/* System Status */}
          <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-800">System Status</h3>
                <p className="text-sm text-green-700">
                  All systems operational. Last configuration update: 2025-01-15 14:30:25
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenProConfiguration;
