/* eslint-disable no-nested-ternary */

'use client';

import { Listbox, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { isEqual } from 'lodash';
import React, { Fragment, useEffect, useRef, useState } from 'react';
import { Tooltip } from 'react-tooltip';

import { Checkbox, CheckBoxStates } from './Checkbox';

interface IItem {
  name: string;
}

interface IListBoxProps {
  items: Array<IItem>;
  name: string;
  label?: string;
  idName?: string;
  multiselect?: boolean;
  isInTable?: boolean;
  isPreselected?: boolean;
  objectIdentifier?: string;
  placeholder?: string;
  selectedItems?: any;
  refId?: any;
  customStyle?: string;
  scrollPosition?: any;
  onSelectionChange: (selectedValue: any) => void;
}

export default function ListBox({
  items,
  multiselect,
  name,
  label,
  idName,
  isInTable,
  placeholder,
  customStyle,
  selectedItems = [],
  isPreselected = true,
  objectIdentifier = 'id',
  refId,
  scrollPosition,
  onSelectionChange,
}: IListBoxProps) {
  // Essentials
  const selectRef: any = useRef(null);

  // Constants
  const id = idName || 'name';

  // States
  const [openBottom, setOpenBottom] = React.useState(false);
  const [firstLoad, setFirstLoad] = useState<any>(true);

  // Methods
  const calculatePosition = () => {
    const buttonPosition = selectRef.current.getBoundingClientRect();
    if (buttonPosition && scrollPosition / 10 + 490 < buttonPosition.bottom) {
      setOpenBottom(true);
    } else {
      setOpenBottom(false);
    }
  };
  const getSelectedOptions = () => {
    if (selectedItems.length > 0) {
      return multiselect ? selectedItems : selectedItems[0];
    }
    if (isPreselected) {
      return multiselect ? [items[0]] : items[0];
    }
    return [];
  };

  // States
  const [selectedOptions, setSelectedOptions] = useState<any>(
    getSelectedOptions(),
  );

  const handleSelectionChange = (newValue: any) => {
    setSelectedOptions(newValue);
    onSelectionChange(newValue);
  };

  // Effects
  useEffect(() => {
    if (firstLoad) {
      setFirstLoad(false);
      onSelectionChange(selectedOptions);
    }
  }, [firstLoad]);

  useEffect(() => {
    const newSelectedOptions = getSelectedOptions();
    if (!isEqual(newSelectedOptions, selectedOptions)) {
      setSelectedOptions(newSelectedOptions);
    }
  }, [selectedItems, selectedOptions]);

  useEffect(() => {
    calculatePosition();
    // Recalculate the position when the window is resized
    window.addEventListener('resize', calculatePosition);
    return () => {
      window.removeEventListener('resize', calculatePosition);
    };
  }, [refId, selectRef, scrollPosition]);

  return (
    <div className=" w-full" ref={selectRef}>
      <Listbox
        value={selectedOptions}
        onChange={handleSelectionChange}
        multiple={multiselect}
        name={name}
      >
        {label ? (
          <Listbox.Label className="pb-1 font-sans text-sm font-semibold leading-6 text-gray-500 disabled:opacity-50">
            {label}
          </Listbox.Label>
        ) : (
          ''
        )}
        <div className="relative">
          <Listbox.Button
            className={` relative  w-full max-w-[260px] cursor-pointer overflow-auto rounded border-[1px]  border-lightgray-100 bg-white-200 px-[11px] pr-8 text-left  text-sm font-normal text-blueGray-300 focus:outline-none focus-visible:border-blue-200 focus-visible:ring-2 focus-visible:ring-[white]/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm ${
              isInTable
                ? customStyle || 'min-h-[32px] py-[4px]'
                : customStyle || 'min-h-[52px] py-[14px]'
            }`}
          >
            <span
              className="block truncate"
              data-tooltip-id="select-tooltip"
              data-tooltip-content={
                multiselect
                  ? selectedOptions?.map((option: any) => option[id]).join(', ')
                  : selectedOptions?.[objectIdentifier]
              }
            >
              {multiselect
                ? selectedOptions.length > 0
                  ? `${selectedOptions?.length ?? '0'} Selected`
                  : ''
                : selectedOptions?.[objectIdentifier]}
            </span>

            <span className="absolute inset-y-0 left-0 flex items-center pl-2 placeholder:text-gray-500">
              {selectedOptions?.length === 0 ||
              selectedOptions[0] === '' ||
              JSON.stringify(selectedOptions) === JSON.stringify({}) ||
              selectedOptions?.name === '' ||
              selectedOptions?.dataset === ''
                ? placeholder || 'Select'
                : ''}
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon
                className="w-5  text-gray-400 ui-open:rotate-180"
                aria-hidden="true"
              />
            </span>
          </Listbox.Button>
          <Transition
            as={Fragment}
            leave="transition ease-in duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Listbox.Options
              className={` absolute z-50  max-h-60 w-full overflow-auto rounded bg-white-200 px-1 py-2 text-base shadow-list  focus:outline-none sm:text-sm ${
                openBottom ? 'bottom-8' : ''
              } `}
            >
              {items.map((item: any) => (
                <Listbox.Option
                  key={item[id]}
                  className={({ active }) =>
                    `cursor-pointer flex relative select-none  px-2 py-2 text-sm items-center  border-lightgray-100 font-normal [&:not(:last-child)]:border-b-[1px] ${
                      active ? 'text-gray-900 ' : 'text-gray-900'
                    }`
                  }
                  value={item}
                >
                  {({ selected }) =>
                    multiselect ? (
                      <Checkbox
                        labelText={item[id]}
                        value={
                          selected || item.selected
                            ? CheckBoxStates.Checked
                            : CheckBoxStates.Empty
                        }
                      />
                    ) : (
                      <span
                        data-tooltip-id="select-tooltip"
                        data-tooltip-content={item[id]}
                        className={`block truncate ${
                          selected ? 'font-medium' : 'font-normal'
                        }`}
                      >
                        {item[id]}
                      </span>
                    )
                  }
                </Listbox.Option>
              ))}
            </Listbox.Options>
          </Transition>
        </div>
      </Listbox>
      <Tooltip
        className="custom-tooltip"
        id="select-tooltip"
        place="top"
        variant="info"
        positionStrategy="fixed"
      />
    </div>
  );
}
