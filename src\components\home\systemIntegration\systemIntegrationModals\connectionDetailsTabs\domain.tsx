/* eslint-disable import/no-cycle */
import React, { useEffect, useState } from 'react';

import DomainTable from '@/components/tables/systemIntegration/domainTable';

const Domain: React.FC<any> = ({ servedData, viewOnly }) => {
  // States
  const [formattedTables, setFormattedTables] = useState([]);

  // Methods
  const formatTables = (result: any) => {
    const data: any = [];
    result.forEach((table: any) => {
      const row: any = {
        id: table.id,
        name: table.table_name,
        components: [],
      };
      row.components.push({
        value: table.table_name,
        type: viewOnly ? '' : 'checkbox',
        selected: false,
        enabled: null,
        userImageLink: null,
        inputType: null,
      });
      row.components.push({
        value: table.analytics_name || '',
        type: 'string',
        selected: false,
        enabled: null,
        userImageLink: null,
        inputType: null,
      });

      data.push(row);
    });
    return data;
  };

  // Effects
  useEffect(() => {
    setFormattedTables(formatTables(servedData?.connection_details?.tables));
  });

  return (
    <div className="flex h-[57vh] w-full flex-col space-y-6 pl-2 pt-6">
      <div className="flex flex-col space-y-2">
        <span className="font-sans text-xs font-medium text-gray-400">
          Domain
        </span>{' '}
        <span className="font-sans text-sm font-semibold text-gray-500">
          {servedData?.connection_details?.analytics[0]?.name}
        </span>
      </div>
      <DomainTable tableData={formattedTables} />
    </div>
  );
};

export default Domain;
