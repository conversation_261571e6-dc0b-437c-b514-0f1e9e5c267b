/* eslint-disable react/jsx-no-useless-fragment */

'use client';

import type { ReactNode } from 'react';
import React from 'react';

import AppLoader from '@/components/appLoader';
import { Alert } from '@/components/ui/Alert';
import { Button } from '@/components/ui/Button';
import { Notification } from '@/components/ui/Notification';
import ReduxProvider from '@/store/ReduxProvider';

interface HomeLayoutProps {
  children: ReactNode;
}

const External: React.FC<HomeLayoutProps> = ({ children }) => {
  return (
    <ReduxProvider>
      <div className="flex min-h-[100vh] w-[100vw] flex-col">
        <div className="flex h-[64px]">
          <div className="flex items-center bg-blue-200 p-2 text-center text-white-200">
            astRai
          </div>
          <div className="flex w-[94%] items-center justify-between px-8">
            <h3 className="font-semibold">Data Collaboration Suite</h3>
            <Button
              // disabled={disablePrimaryFooterBtn}
              type="button"
              // onClick={footerPrimarySubmitHandler}
              className="ml-4"
            >
              Analytics Dashboard
            </Button>
          </div>
        </div>
        <main className="bg-lightgray-100/40 p-10 pt-6 lg:w-[100vw]">
          {children}
        </main>
        <AppLoader />
      </div>
      <Alert />
      <Notification />
    </ReduxProvider>
  );
};

export default External;
