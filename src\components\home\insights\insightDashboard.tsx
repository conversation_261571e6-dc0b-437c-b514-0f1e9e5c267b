/* eslint-disable react/no-array-index-key */
/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-danger */

'use client';

/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import { startCase } from 'lodash';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

import { Dropdown } from '@/components/ui/Dropdown';
import { Searchbar } from '@/components/ui/Searchbar';

// import SelectData from './addLogicalEntitiesTabs/selectData';

const InsightDashboard: React.FC<{ dashboards: any; dashboardName: any }> = ({
  dashboards,
  dashboardName,
}) => {
  // States
  const [searchDashboardItem, setSearchDashboardItem] = useState('');
  const [dashboard, setDashboard] = React.useState<any>([]);
  const [filteredDashboard, setFilteredDashboard] = React.useState([]);
  const [selectedDashboard, setSelectedDashboard] = React.useState<any>({
    url: '',
    name: '',
  });

  // Constants
  const menuData: any = [
    {
      option: 'Edit',
    },
    {
      option: 'Delete',
    },
  ];

  // Methods

  const openDashboard = (url: string, name: string) => {
    setSelectedDashboard((prevDashboardValues: any) => ({
      ...prevDashboardValues,
      name,
      url,
    }));
  };

  const markFav = (rowIndex: any, cardId: any, isFav: boolean) => {
    const newCards: any = dashboard[rowIndex].dashboards?.map((card: any) => {
      if (card.id === cardId) {
        card.isFavourite = !card.isFavourite;
      }
      return card;
    });
    setDashboard(newCards);
    if (isFav === true) {
      // callApi  to add fav by sending id and screen
    } else {
      // callApi  to remove fav by sending id and screen
    }
  };

  const goBack = () => {
    setSelectedDashboard((prevDashboardValues: any) => ({
      ...prevDashboardValues,
      name: '',
      url: '',
    }));
  };

  const generateIframeElement = (url: string): any => {
    return `<iframe width=100% height=100% src=${url} frameBorder=0 allowFullScreen />`;
  };

  const handleDashboardSearch = (value: string) => {
    setSearchDashboardItem(value);
    const filteredResults = dashboard.filter((item: any) =>
      item.name.toLowerCase().includes(value.toLowerCase()),
    );
    setFilteredDashboard(filteredResults);
  };

  // Efects
  useEffect(() => {
    dashboards.forEach((item: any) => {
      item.dashboards.forEach((dash: any) => {
        dash.isFavourite = dash?.isFavourite ? dash?.isFavourite : false;
      });
    });
    setDashboard(dashboards);
    setFilteredDashboard(dashboards);
  });

  useEffect(() => {
    if (searchDashboardItem === '') {
      setFilteredDashboard(dashboard);
    }
    setFilteredDashboard(filteredDashboard);
  }, [searchDashboardItem, filteredDashboard]);

  return (
    <div>
      {(!selectedDashboard.name || !selectedDashboard.url) && (
        <div className="flex w-full flex-col space-y-4">
          <div className="flex w-full items-center justify-between">
            <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
              {startCase(dashboardName)} Insights
            </span>
          </div>
          <div className="flex h-[88vh] w-full flex-col border-lightgray-100 text-blueGray-300">
            <div className="flex size-full flex-col space-y-6">
              <div className="w-[40vw]">
                {' '}
                <Searchbar
                  value={searchDashboardItem}
                  placeholder="Search Dashboard"
                  onChange={(e) => handleDashboardSearch(e.target.value)}
                />
              </div>
              <div className="flex size-full max-h-[calc(100vh-100px)] flex-wrap overflow-auto">
                {filteredDashboard.map((cardData: any, index: any) => (
                  <div
                    className="insight-enterprise-card mb-4 mr-4 h-fit w-[305px]  rounded-md bg-white-200"
                    key={index}
                  >
                    <div
                      className="flex rounded-t-md border-b-[0.5px]  border-lightgray-100 bg-white-200 py-2"
                      style={{
                        backgroundColor: cardData.bgcolor,
                      }}
                    >
                      <span className="px-4 font-sans text-base font-semibold text-blueGray-300">
                        {cardData.name}
                      </span>
                    </div>
                    <div className="flex w-full flex-col px-4 pb-4">
                      <div className="flex h-[110px] flex-col justify-between py-2">
                        <span className="font-sans text-sm font-normal text-gray-500">
                          {cardData.heading}
                        </span>
                        <span className="mt-2 font-sans text-xs font-semibold text-gray-500">
                          Dashboards
                        </span>
                      </div>

                      <div className="flex h-[65%]  flex-col space-y-1 overflow-auto">
                        {cardData.dashboards.map(
                          (dashboardData: any, rowIndex: any) => (
                            <div
                              key={rowIndex}
                              className="flex min-h-[44px] w-full flex-row items-center justify-between rounded-sm bg-slate-100 px-2"
                            >
                              <button
                                onClick={() => {
                                  openDashboard(
                                    dashboardData.url,
                                    dashboardData.name,
                                  );
                                }}
                                type="button"
                                className="flex cursor-pointer text-left font-sans text-sm font-semibold  text-blue-200"
                              >
                                {dashboardData.name}
                              </button>
                              <div className="flex flex-row items-center space-x-1 pt-1">
                                <button
                                  type="button"
                                  className="size-[24px] cursor-pointer"
                                  onClick={() => {
                                    markFav(
                                      index,
                                      dashboardData.id,
                                      !dashboardData?.isFavourite,
                                    );
                                  }}
                                >
                                  {dashboardData?.isFavourite ? (
                                    <img
                                      src="/assets/images/star-active.svg"
                                      alt="clear"
                                    />
                                  ) : (
                                    <img
                                      src="/assets/images/star.svg"
                                      alt="clear"
                                    />
                                  )}
                                </button>
                                <Dropdown
                                  menuTitle={`<img src='/assets/images/menu.svg'>`}
                                  data={menuData}
                                  refId={dashboardData.id}
                                  // scrollPosition={scrollPosition}
                                />
                              </div>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      {selectedDashboard.name && selectedDashboard.url && (
        <div className="flex size-full flex-col space-y-4">
          <div
            className="flex w-fit flex-row items-center space-x-2"
            onClick={goBack}
          >
            <Image
              className="cursor-pointer"
              src="/assets/images/arrow-left.svg"
              width={24}
              height={24}
              alt="back"
            />
            <span className="font-sans text-2xl font-semibold leading-8 text-blueGray-300">
              {selectedDashboard.name}
            </span>
          </div>
          <div className="flex h-[84vh] w-full flex-col rounded border border-lightgray-100 bg-white-200 p-4">
            <div
              className="h-[calc(100vh-100px)] w-full flex-col rounded border border-lightgray-100 bg-white-200 p-4"
              dangerouslySetInnerHTML={{
                __html: generateIframeElement(selectedDashboard.url),
              }}
            />
          </div>
        </div>
      )}{' '}
    </div>
  );
};

export default InsightDashboard;
