{"data": [{"business_entity_id": 98, "business_entity_name": "Business1", "business_entity_description": "desc", "join_config": {"0": {"join_type": "Left Join", "child_table": "FACT_ACCOUNTING_DOCUMENT_HEADER", "parent_table": "FACT_ACCOUNTING_DOCUMENT_HEADER", "join_condition": "", "child_attribute": "COMPANY_CODE", "parent_attribute": "COMPANY_CODE"}}, "custom_attributes": {}, "mapped_attributes": {"798": {"logical_entity_id": 160, "attribute_id": 798, "attribute_name": "COMPANY_CODE", "attribute_datatype": "VARCHAR(255)", "attribute_type": "mapped_attribute", "attribute_config": {"entity_id": 160, "entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "attribute_id": 349, "attribute_name": "COMPANY_CODE"}}, "799": {"logical_entity_id": 160, "attribute_id": 799, "attribute_name": "POSTING_DATE", "attribute_datatype": "VARCHAR(255)", "attribute_type": "mapped_attribute", "attribute_config": {"entity_id": 160, "entity_name": "FACT_ACCOUNTING_DOCUMENT_HEADER", "attribute_id": 350, "attribute_name": "POSTING_DATE"}}}}], "status": 200, "statusText": "", "headers": {"content-length": "1018", "content-type": "application/json"}, "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": "xhr", "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "X-API-KEY": "F3C3ZWb4iD8VKSL3zi5FR3He4CxQnJ7y2vAxnx73"}, "method": "get", "url": "https://4fxepgdat0.execute-api.us-east-2.amazonaws.com/dev/v1/business_models/98"}, "request": {}}