{"name": "next-js-boilerplate", "version": "1.45.2", "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start", "build-stats": "cross-env ANALYZE=true npm run build", "clean": "rimraf .next .swc out coverage", "lint": "next lint", "format": "next lint --fix && prettier '**/*.{json,yaml}' --write --ignore-path .gitignore", "check-types": "tsc --noEmit --pretty && tsc --project cypress --noEmit --pretty", "test": "jest", "commit": "cz", "db:generate": "drizzle-kit generate:sqlite", "db:push": "drizzle-kit push:sqlite", "db:migrate": "tsx ./scripts/DbMigrate.ts", "db:studio": "dotenv -c -- drizzle-kit studio", "cypress": "cypress open", "cypress:headless": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress", "e2e:headless": "start-server-and-test dev http://localhost:3000 cypress:headless", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:serve": "http-server storybook-static --port 6006 --silent", "serve-storybook": "run-s storybook:*", "test-storybook:ci": "start-server-and-test serve-storybook http://localhost:6006 test-storybook", "prepare": "husky install", "postbuild": "next-sitemap"}, "dependencies": {"@azure/msal-browser": "^3.22.0", "@azure/msal-react": "^2.0.22", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^1.7.17", "@headlessui/tailwindcss": "^0.2.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.0", "@libsql/client": "^0.3.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@reduxjs/toolkit": "^1.9.5", "@stomp/stompjs": "^7.0.0", "@t3-oss/env-nextjs": "^0.6.1", "@types/xlsx": "^0.0.35", "@wojtekmaj/react-daterange-picker": "^5.4.2", "apexcharts": "^3.45.2", "axios": "^1.5.0", "class-variance-authority": "^0.7.0", "cron-parser": "^4.9.0", "csv-parse": "^5.5.5", "downloadjs": "^1.4.7", "drizzle-orm": "^0.28.5", "formik": "^2.4.3", "lodash": "^4.17.21", "lucide-react": "^0.263.1", "moment": "^2.30.1", "next": "^14.1.0", "next-nprogress-bar": "^2.1.2", "next-seo": "^6.1.0", "next-sitemap": "^4.2.2", "nextjs-progressbar": "^0.0.16", "nextra": "^2.13.2", "nextra-theme-docs": "^2.13.2", "papaparse": "^5.4.1", "powerbi-client-react": "^1.4.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-csv": "^2.2.2", "react-data-grid": "^7.0.0-beta.44", "react-data-table-component": "^7.6.2", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-js-cron": "^5.0.0", "react-loader-spinner": "^5.4.5", "react-loading-overlay-ts": "^2.0.2", "react-markdown": "^9.0.1", "react-papaparse": "^4.4.0", "react-redux": "^8.1.2", "react-spreadsheet": "^0.9.4", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.25.0", "reactflow": "^11.9.4", "reactour": "^1.19.2", "sass": "^1.66.1", "scheduler": "^0.23.0", "sharp": "^0.33.2", "styled-components": "^5.0.0", "survey-core": "^1.11.4", "survey-react-ui": "^1.9.115", "ws": "^8.17.0", "xlsx": "^0.18.5", "yup": "^1.3.2", "zod": "^3.22.2"}, "devDependencies": {"@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@commitlint/cz-commitlint": "^17.7.1", "@next/bundle-analyzer": "^13.5.3", "@percy/cli": "^1.26.3", "@percy/cypress": "^3.1.2", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@storybook/addon-essentials": "^7.6.14", "@storybook/addon-interactions": "^7.6.14", "@storybook/addon-links": "^7.6.14", "@storybook/blocks": "^7.6.14", "@storybook/nextjs": "^7.6.14", "@storybook/react": "^7.6.14", "@storybook/test-runner": "^0.16.0", "@storybook/testing-library": "^0.2.2", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.9", "@testing-library/cypress": "^9.0.0", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^14.0.0", "@types/downloadjs": "^1.4.6", "@types/jest": "^29.5.4", "@types/node": "^20.5.6", "@types/papaparse": "^5.3.9", "@types/react": "^18.2.21", "@types/react-csv": "^1.1.10", "@types/react-syntax-highlighter": "^15.5.13", "@types/reactour": "^1.18.5", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "autoprefixer": "^10.4.15", "commitizen": "^4.3.0", "cross-env": "^7.0.3", "cssnano": "^6.0.1", "cypress": "^12.17.4", "dotenv-cli": "^7.3.0", "drizzle-kit": "^0.19.13", "encoding": "^0.1.13", "eslint": "^8.48.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "^14.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.14.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-jest-formatting": "^3.1.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-storybook": "^0.6.13", "eslint-plugin-tailwindcss": "^3.13.0", "eslint-plugin-testing-library": "^6.0.1", "eslint-plugin-unused-imports": "^3.0.0", "http-server": "^14.1.1", "husky": "^8.0.3", "jest": "^29.6.4", "jest-environment-jsdom": "^29.6.4", "lint-staged": "^14.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.28", "prettier": "^3.0.2", "rimraf": "^5.0.1", "semantic-release": "^19.0.5", "start-server-and-test": "^2.0.0", "storybook": "^7.0.27", "tailwindcss": "^3.3.3", "tsx": "^3.12.7", "typescript": "5.1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false}], "@semantic-release/git", "@semantic-release/github"]}, "author": "Ixartz (https://github.com/ixartz)"}