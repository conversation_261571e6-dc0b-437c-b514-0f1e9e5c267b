import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface resetPasswordState {
  value: string;
}

const initialState: resetPasswordState = {
  value: '',
};

export const resetPasswordSlice = createSlice({
  name: 'resetPassword',
  initialState,
  reducers: {
    setEmailValue: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.value = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { setEmailValue } = resetPasswordSlice.actions;

export default resetPasswordSlice.reducer;
