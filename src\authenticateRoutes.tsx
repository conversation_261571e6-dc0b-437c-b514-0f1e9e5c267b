'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { setToastAlert } from './slices/metaDataSlice';

const AuthenticateRoutes = () => {
  // Essentials
  const pathname: any = usePathname();
  const router = useRouter();
  const dispatch = useDispatch();

  // Method
  const checkTokenExpiration = (routeAllowed: any) => {
    const loginTimeStr = localStorage.getItem('login_time');
    const expiresIn: any = localStorage.getItem('expires_in');
    const isGenProUser = localStorage.getItem('user_id') !== null;
    
    if (loginTimeStr) {
      const loginTime = parseInt(loginTimeStr, 10);
      const currentTime = new Date().getTime();
      const timeElapsed = (currentTime - loginTime) / 1000;
      
      // Only check expiration for BSM users who have expires_in
      // GenPro users don't have expires_in and use JWT token expiration
      if (!isGenProUser && expiresIn && timeElapsed >= parseInt(expiresIn, 10)) {
        localStorage.clear();
        const isAllowedRoute = routeAllowed.includes(pathname);
        if (isAllowedRoute) return;
        dispatch(
          setToastAlert({
            isToastOpen: true,
            intent: 'error',
            title: 'Error',
            content: 'Session Timeout! Please log in again.',
          }),
        );
        router.push('/login');
      }
    }
  };

  // Efects
  useEffect(() => {
    const isGenProUser = localStorage.getItem('user_id') !== null;
    
    // Different required items for GenPro vs BSM users
    const requiredItemsBSM = [
      'id_token',
      'refresh_token',
      'access_token',
      'expires_in',
      'login_time',
      'userName',
      'userEmail',
    ];
    
    const requiredItemsGenPro = [
      'access_token',
      'refresh_token',
      'login_time',
      'userName',
      'userEmail',
      'user_id',
    ];
    
    const requiredItems = isGenProUser ? requiredItemsGenPro : requiredItemsBSM;
    
    const routeAllowed = [
      '/create-new-password',
      '/reset-pw',
      '/register',
      '/login',
    ];
    
    checkTokenExpiration(routeAllowed);
    const itemsExist = requiredItems.every((item) =>
      localStorage.getItem(item),
    );
    const isAllowedRoute = routeAllowed.includes(pathname);

    if (!itemsExist && !isAllowedRoute) {
      router.push('/login');
    }
  }, [pathname, router]);

  return null;
};

export default AuthenticateRoutes;
