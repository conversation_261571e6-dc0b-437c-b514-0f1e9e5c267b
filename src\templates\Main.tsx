import Link from 'next/link';
import type { ReactNode } from 'react';

import { AppConfig } from '@/utils/AppConfig';

type IMainProps = {
  meta?: ReactNode;
  children: ReactNode;
};

const Main = (props: IMainProps) => (
  <div className="w-full px-1 text-gray-700 antialiased">
    {props.meta}

    <div className="mx-auto max-w-screen-md">
      <header className="border-b border-gray-300">
        <div className="pb-8 pt-16">
          <h1 className="text-3xl font-bold text-gray-900">
            {AppConfig.title}
          </h1>
          <h2 className="text-xl">{AppConfig.description}</h2>
        </div>

        <div className="flex justify-between">
          <nav>
            <ul className="flex flex-wrap text-xl">
              <li className="mr-6">
                <Link
                  href="/login/"
                  className="border-none text-gray-700 hover:text-gray-900"
                >
                  Sign in
                </Link>
              </li>

              <li className="mr-6">
                <Link
                  href="/register/"
                  className="border-none text-gray-700 hover:text-gray-900"
                >
                  Sign up
                </Link>
              </li>
              <li className="mr-6">
                <Link
                  href="/createpassword/"
                  className="border-none text-gray-700 hover:text-gray-900"
                >
                  Create Password
                </Link>
              </li>
              <li className="mr-6">
                <Link
                  href="/systemIntegration/"
                  className="border-none text-gray-700 hover:text-gray-900"
                >
                  System Integration
                </Link>
              </li>
            </ul>
          </nav>
        </div>
      </header>

      <footer className="mt-96 border-t border-gray-300 py-8 text-center text-sm">
        © Copyright {new Date().getFullYear()} {AppConfig.title}. Made with{' '}
        <a href="https://midofficedata.com">MidOfficeData</a>.
      </footer>
    </div>
  </div>
);

export { Main };
