[{"id": 1, "components": [{"value": "Additional Information View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 2, "components": [{"value": "Budget and Forecast Additional Info View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 3, "components": [{"value": "Budget and Forecast After Group Cost Allocation", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 4, "components": [{"value": "Budget and Forecast Before Group Cost Allocation", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 5, "components": [{"value": "Consolidated Group Cost Before Group Cost Allocation", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 6, "components": [{"value": "Consolidated Group Cost After Group Cost Allocation", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 7, "components": [{"value": "Vessel Transaction View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 8, "components": [{"value": "Budget and Forecast View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 9, "components": [{"value": "HR Shore Tile", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 10, "components": [{"value": "SMC Additional Information Budget and Forecast View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}, {"id": 11, "components": [{"value": "SMC Additional Details View", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "N/A", "userAvatarLink": null, "disabled": false, "type": "text"}]}]