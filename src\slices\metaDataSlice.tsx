import { createSlice } from '@reduxjs/toolkit';

// eslint-disable-next-line @typescript-eslint/naming-convention
export interface metaDataState {
  domains: any;
  sourceSystems: any;
  mappedDomain: any;
  mappedSourceSystems: any;
  sourceSystemForms: any;
  erpDomainTables: any;
  toastAlert: any;
  notification: any;
  notificationAlert: any;
  warningAlert: any;
}

const initialState: metaDataState = {
  domains: [],
  notification: [],
  sourceSystems: [],
  mappedDomain: {},
  mappedSourceSystems: {},
  sourceSystemForms: {},
  erpDomainTables: {},
  toastAlert: {
    isToastOpen: false,
    intent: '',
    title: '',
    content: '',
  },
  notificationAlert: {
    isNotificationOpen: false,
    intent: '',
    title: 'New Notification',
    content: '',
  },
  warningAlert: {
    isWarningOpen: false,
    headerTitle: '',
    message: '',
    actionbuttonText: '',
    cancelButtonText: '',
  },
};

export const metaDataSlice = createSlice({
  name: 'metaData',
  initialState,
  reducers: {
    setDomains: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.domains = action.payload;
    },
    setSourceSystems: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.sourceSystems = action.payload;
    },
    setMappedDomain: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.mappedDomain = action.payload;
    },
    setMappedSourceSystems: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.mappedSourceSystems = action.payload;
    },
    setSourceSystemForms: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.sourceSystemForms = action.payload;
    },
    setErpDomainTable: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.erpDomainTables = action.payload;
    },
    setToastAlert: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.toastAlert = action.payload;
    },
    setNotificationAlert: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.notificationAlert = action.payload;
    },
    setNotification: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.notification = action.payload;
    },
    setWarningAlert: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.warningAlert = action.payload;
    },
    setIsWarningAlertOpen: (state, action) => {
      // eslint-disable-next-line no-param-reassign
      state.warningAlert.isWarningOpen = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setDomains,
  setSourceSystems,
  setMappedDomain,
  setMappedSourceSystems,
  setSourceSystemForms,
  setErpDomainTable,
  setToastAlert,
  setWarningAlert,
  setNotificationAlert,
  setNotification,
  setIsWarningAlertOpen,
} = metaDataSlice.actions;

export default metaDataSlice.reducer;
