/* eslint-disable import/no-extraneous-dependencies */
import { Formik } from 'formik';
import React, { useState } from 'react';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import ListBox from '@/components/ui/ListBox';
import { TextField } from '@/components/ui/TextField';

const CreateCustomAttribute: React.FC<{
  isView?: boolean;
  data?: any;
  onFormSubmit: (values: any, errors: any) => void;
}> = ({ isView, onFormSubmit, data }) => {
  // Constants
  const selectList: any = [{ name: 'INT' }, { name: 'LONG' }];

  // States
  const [formErrors, setFormErrors] = useState<any>({});
  const [formulaError, setFormulaError] = useState<string | null>(null);
  const [DataType, setDataType] = useState<any>([]);

  // Methods
  const isRequired = (value: string) => {
    return value ? undefined : 'Required.';
  };

  const validateFormula = (values: any) => {
    if (!values || !values.formula) {
      setFormulaError('Invalid input');
      return;
    }
    const formulaSql = values.formula.toLowerCase();
    const sqlKeywords = [
      'select',
      'from',
      'where',
      'join',
      'group by',
      'having',
      'order by',
      'insert',
      'update',
      'delete',
    ];
    const containsSqlKeyword = sqlKeywords.some((keyword) =>
      formulaSql.includes(keyword),
    );
    if (containsSqlKeyword) {
      setFormulaError(null);
      const newValues = {
        ...values,
        dataType: DataType?.name,
      };
      onFormSubmit(newValues, formErrors);
    } else {
      setFormulaError('Invalid SQL query');
    }
  };

  return (
    <Formik
      initialValues={{
        attributeName: data?.attribute_name,
        dataType: data?.attribute_datatype,
        description: data?.description,
        formula: data?.formula,
      }}
      validate={(values) => {
        const errors: any = {};
        errors.attributeName = isRequired(values.attributeName);
        // errors.dataType = isRequired(values.dataType);
        // errors.description = isRequired(values.description);
        errors.formula = isRequired(values.formula);
        setFormErrors(errors);
        return errors;
      }}
      onSubmit={(values) => {
        console.log(values);
        // validateFormula(values.formula);
        // onFormSubmit(values, formErrors);
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
      }) => (
        <form
          className="flex h-full w-full flex-col space-y-4 px-4 py-7"
          onSubmit={handleSubmit}
        >
          <div className="flex h-[105px] w-full flex-row space-x-6">
            <div className="h-full w-[20%]">
              {' '}
              <Input
                label="Attribute Name"
                name="attributeName"
                type="text"
                className=""
                placeholder="Enter Attribute Name"
                onChange={handleChange}
                disabled={isView}
                onBlur={handleBlur}
                value={values.attributeName}
                intent={
                  errors.attributeName && touched.attributeName
                    ? 'hasError'
                    : 'enabled'
                }
                error={
                  errors.attributeName &&
                  touched.attributeName &&
                  errors.attributeName
                }
              />
            </div>
            <div className=" w-[20%] ">
              {' '}
              <div className="flex h-full w-full flex-col space-y-1">
                <span className="font-sans text-sm font-semibold text-gray-500">
                  Data Type
                </span>
                <div className="">
                  <ListBox
                    items={selectList}
                    // value={}
                    selectedItems={DataType}
                    objectIdentifier="name"
                    name="dataType"
                    onSelectionChange={(selectedValue) => {
                      setDataType(selectedValue);
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="w-[60%] ">
              <TextField
                label="Description"
                name="description"
                className="h-[75px]"
                placeholder="Enter Description"
                onChange={handleChange}
                disabled={isView}
                onBlur={handleBlur}
                value={values.description}
                intent={
                  errors.description && touched.description
                    ? 'hasError'
                    : 'enabled'
                }
                error={
                  errors.description &&
                  touched.description &&
                  errors.description
                }
              />
            </div>
          </div>
          <div className="h-[60%] w-full">
            <TextField
              label="Formula"
              name="formula"
              className="h-full"
              disabled={isView}
              placeholder="Enter formula"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.formula}
              intent={
                errors.formula && touched.formula ? 'hasError' : 'enabled'
              }
              error={errors.formula && touched.formula && errors.formula}
            />
            {formulaError && (
              <div className="py-1 font-sans text-xs font-normal leading-4 text-red-200">
                {formulaError}
              </div>
            )}
          </div>

          {!isView && (
            <div className="relative mt-8 flex  w-full justify-end pl-4">
              <Button
                className="w-[200px]"
                intent="primary"
                disabled={!values.formula}
                onClick={() => {
                  validateFormula(values);
                }}
              >
                Validate Formula
              </Button>
            </div>
          )}
        </form>
      )}
    </Formik>
  );
};

export default CreateCustomAttribute;
