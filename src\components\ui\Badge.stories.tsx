import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Badge } from './Badge';

const meta = {
  title: 'UI/Badge',
  component: Badge,
  args: {
    intent: 'success',
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;
const Success: Story = {
  args: {
    intent: 'success',
    content: 'green',
  },
};

const Info: Story = {
  args: {
    intent: 'info',
    content: 'blue',
  },
};

const Warning: Story = {
  args: {
    intent: 'warning',
    content: 'Yellow',
  },
};
const Error: Story = {
  args: {
    intent: 'error',
    content: 'red',
  },
};
const Neutral: Story = {
  args: {
    intent: 'neutral',
    content: 'Neutral',
  },
};
const Counter: Story = {
  args: {
    intent: 'counter',
    content: 'counter',
  },
};
export { Counter, Error, Info, Neutral, Success, Warning };
