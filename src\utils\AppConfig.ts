export const AppConfig = {
  site_name: 'Astrai',
  title: 'List of All Screens',
  description:
    'This page will only be used temporarily until we have a better solution to display all screens.',
  locale: 'en',
  network: {
    apiServer: {
      overrideTarget: true,
      protocol: 'https://',
      target: 'api.bsm.astrai.io',
      portNo: '',
      basePath: '/dev/v1',
    },
    apiServerBsm: {
      overrideTarget: true,
      protocol: 'https://',
      target: 'bsm-api.ap-south-1.elasticbeanstalk.com',
      portNo: '',
      basePath: '/api/v1',
    },
    apiServerGenPro: {
      overrideTarget: true,
      protocol: 'https://',
      target: 'api.genpro-dev.d4outcomes.com',
      portNo: '',
      basePath: '/api',
    },
    webServer: {
      protocol: 'https://bsm.astrai.io',
      basePath: '/',
    },
    chatServer: {
      protocol: 'https://',
      target: 'bsm.genai.astrai.io',
      portNo: '',
      basePath: '/chat/',
    },
    apiServerBsmNotification: {
      protocol: 'https://',
      target: 'api.bsm.astrai.io',
      portNo: '',
      basePath: '/api/v1',
    },
  },
};
