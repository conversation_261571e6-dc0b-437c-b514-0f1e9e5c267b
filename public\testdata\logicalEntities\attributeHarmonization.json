[{"id": "Vendor Master 1", "selected": true, "components": [{"value": "<PERSON><PERSON><PERSON> Master", "disabled": false, "userAvatarLink": null, "type": "text"}, {"value": "Table 12", "columnType": "table", "columnKey": "SAP", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Table 10"}, {"name": "Table 11"}, {"name": "Table 12"}]}, {"value": "Attribute 10", "columnType": "attribute", "columnKey": "SAP", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Attribute 10"}, {"name": "Attribute 11"}, {"name": "Attribute 12"}]}, {"value": "Table 10", "columnType": "table", "columnKey": "ORACLE", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Table 10"}, {"name": "Table 11"}, {"name": "Table 12"}]}, {"value": "Attribute 10", "columnType": "attribute", "columnKey": "ORACLE", "disabled": false, "userAvatarLink": null, "type": "select", "placeholder": "Datatype", "selectList": [{"name": "Attribute 10"}, {"name": "Attribute 11"}, {"name": "Attribute 12"}]}, {"value": "VENDOR", "columnType": "harmonizedAttribute", "disabled": false, "userAvatarLink": null, "type": "input", "placeholder": "Harmonised Attribute Name"}]}]