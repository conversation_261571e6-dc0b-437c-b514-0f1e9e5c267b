/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable jsx-a11y/anchor-is-valid */
import Papa from 'papaparse';
import eData from 'public/testdata/logicalEntities/customAttribute.json';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import CreateCustomAttribute from '@/components/home/<USER>/addLogicalEntitiesTabs/modal/createCustomAttribute';
import { Link } from '@/components/ui/Link';
import { Modal } from '@/components/ui/Modal';
import Table from '@/components/ui/Table';
import { createMenuName, downloadMenuTitle } from '@/constants/menuSvg';

import { Searchbar } from '../../ui/Searchbar';

const CustomAttribute: React.FC<{
  mode: string | string[] | undefined;
  onRowChange?: any;
  result?: any;
}> = (props) => {
  // Selectors
  const customAttributes = useSelector(
    (state: any) => state.logicalEntityCrud.customAttributes,
  );

  // States
  const [selectedCustomAttribute, setSelectedCustomAttribute] = useState<any>({
    attribute_name: '',
    attribute_datatype: '',
    formula: '',
    description: '',
  });
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [searchItem, setSearchItem] = useState();
  const [newValueToAdd, setNewValues] = useState({
    attributeName: '',
    dataType: '',
    description: '',
    formula: '',
  });
  const [data, setData]: any = useState(eData);
  const [filteredData, setFilteredData] = useState(data);
  const [isFormValid, setFormIsValid] = useState(false);

  const header: any = [
    {
      title: 'Attribute Name',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Data Type',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Description',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
    {
      title: 'Formula',
      optionsEnabled: false,
      options: [],
      inputType: 'text',
    },
  ];

  // Methods
  const resolveData = () => {
    const newData: any = [];
    const custAttr = customAttributes;
    Object.keys(custAttr || {}).forEach((key: any, index: any) => {
      if (!newData[index]) {
        newData[index] = {
          id: key,
          selected: true,
          components: [],
        };
      }
      newData[index].components.push({
        value: key,
        disabled: false,
        columnKey: 'attribute',
        userAvatarLink: null,
        type: 'text',
      });
      newData[index].components.push({
        value: custAttr[key].attribute_datatype,
        type: 'text',
        columnKey: 'attribute_datatype',
        disabled: false,
        userAvatarLink: null,
      });
      newData[index].components.push({
        value: custAttr[key].description,
        columnKey: 'description',
        type: 'text',
        disabled: false,
        userAvatarLink: null,
      });
      newData[index].components.push({
        value: custAttr[key].formula,
        columnKey: 'formula',
        type: 'formula',
        disabled: false,
        userAvatarLink: null,
      });
    });
    return newData;
  };

  const handleSearch = (value: any) => {
    setSearchItem(value);
    const filteredValues = data.filter((row: any) =>
      row.components.some((cell: any) => {
        if (typeof cell.value === 'string') {
          return cell.value.toLowerCase().includes(value.toLowerCase());
        }
        return false;
      }),
    );
    setFilteredData(filteredValues);
  };

  const downloadAttributesList = (filename: string) => {
    const csv = Papa.unparse(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };
  const handleFormSubmit = (values: any, errors: any) => {
    if (errors.formula === undefined && errors.attributeName === undefined) {
      setFormIsValid(true);
      setNewValues(values);
    }
  };
  const addNewRow = () => {
    const newRow = {
      id: newValueToAdd.attributeName,
      selected: true,
      components: [
        {
          value: newValueToAdd.attributeName,
          columnKey: 'attribute',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: 'INT',
          columnKey: 'attribute_datatype',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: newValueToAdd.description,
          columnKey: 'description',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
        {
          value: newValueToAdd.formula,
          columnKey: 'formula',
          type: 'text',
          disabled: false,
          userAvatarLink: null,
        },
      ],
    };
    const updatedData = [newRow, ...filteredData];
    setData(updatedData);
    setFilteredData(updatedData);
    props?.onRowChange(updatedData);
    setIsPopupOpen(false);
  };
  const addCustomeAttribute = (key: any) => {
    const ca = structuredClone(customAttributes[key]);
    setSelectedCustomAttribute(ca);
    setIsPopupOpen(true);
  };
  const closePopupModal = () => {
    setIsPopupOpen(false);
  };

  const editCustomAttribute = (key: any) => {
    const ca = structuredClone(customAttributes[key]);
    ca.attribute_name = key;
    setSelectedCustomAttribute(ca);
    setIsPopupOpen(true);
  };
  // Effects
  useEffect(() => {
    if (props?.result) {
      const temp = resolveData();
      setFilteredData(temp);
    }
  }, [props?.result]);
  return (
    <div className="flex  h-[66vh] w-full flex-col space-y-6 pt-6">
      <div className="flex w-full flex-row items-center justify-between">
        <div className="w-[40vw]">
          {' '}
          <Searchbar
            value={searchItem}
            placeholder="Search"
            onChange={(e) => handleSearch(e.target.value)}
          />
        </div>
        {props.mode !== 'View' && (
          <div className="flex flex-row items-center space-x-4">
            <Link
              content={createMenuName('Add Row')}
              onClick={addCustomeAttribute}
            />
            <Link
              content={downloadMenuTitle}
              onClick={() => downloadAttributesList('CustomAttributeList')}
            />
          </div>
        )}
      </div>

      <div className="h-[70vh] w-full overflow-auto">
        <Table
          isCondensedTable
          isDashTable
          header={header}
          data={filteredData}
          isEyeVisible={props.mode === 'View'}
          EyeFunction={addCustomeAttribute}
          enableOptions={false}
          isView={props.mode === 'View'}
          isEdit={props.mode !== 'View'}
          enabledCross={props.mode !== 'View'}
          onRowChange={props.onRowChange}
          onEdit={editCustomAttribute}
        />
      </div>
      <Modal
        isActionButtonVisible={props?.mode !== 'View'}
        cancelbuttonText="Cancel"
        actionbuttonText="Proceed"
        isOpen={isPopupOpen}
        headerTitle="Create Custom Attribute"
        disablePrimaryFooterBtn={props?.mode === 'View' || !isFormValid}
        isCancelVisible
        component={
          <CreateCustomAttribute
            data={selectedCustomAttribute}
            onFormSubmit={handleFormSubmit}
            isView={props?.mode === 'View'}
          />
        }
        footerPrimaryEventHandler={() => {
          addNewRow();
        }}
        closeModal={closePopupModal}
      />
    </div>
  );
};

export default CustomAttribute;
