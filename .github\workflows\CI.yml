name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    name: Build with ${{ matrix.node-version }}
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
      - run: npm ci
      - run: npx next build

  test:
    strategy:
      matrix:
        node-version: [16.x]

    name: Run all tests
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Retrieve Git history, needed to verify commits
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
      - run: npm ci

      - if: github.event_name == 'pull_request'
        name: Validate all commits from PR
        run: npx commitlint --from ${{ github.event.pull_request.base.sha }} --to ${{ github.event.pull_request.head.sha }} --verbose

      - name: Linter
        run: npm run lint

      - name: Type checking
        run: npm run check-types

      - name: Run unit tests
        run: npm run test

      - name: Run storybook tests
        run: npm run test-storybook:ci

      - name: Run e2e tests
        run: npx percy exec -- npm run e2e:headless
        env:
          PERCY_TOKEN: ${{ secrets.PERCY_TOKEN }}
