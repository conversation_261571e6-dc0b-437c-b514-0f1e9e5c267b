/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-unstable-nested-components */
import Box from "@mui/material/Box";
import FormControl from "@mui/material/FormControl";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import * as React from "react";

const V2ChipSelect: React.FC<any> = ({
  options,
  placeholder,
  value,
  onChange,
  disabled = false,
  noBorder = false,
  noSelect = false,
  onOpen,
  boxStyle,
}) => {
  return (
    <Box sx={{ minWidth: 0, width: "100%" }}>
      <FormControl className="select-core w-full">
        <Select
          IconComponent={() => (
            <img
              alt="select-box"
              src="/assets/images/arrow-grey.svg"
              style={{ height: "6px", paddingRight: 19, cursor: "pointer" }}
            />
          )}
          variant={noBorder ? "standard" : "outlined"}
          id="demo-simple-select"
          className={` ${boxStyle}`}
          value={value}
          onChange={(e: any) => {
            onChange(e.target.value.key);
          }}
          disabled={disabled}
          onOpen={onOpen}
          displayEmpty
          MenuProps={{ MenuListProps: { disablePadding: true } }}
          inputProps={{ "aria-label": "Without label" }}
          renderValue={(selected: string) => {
            if (!selected || noSelect) {
              return (
                <div className="text-xs font-normal leading-4 text-gray-400">
                  {placeholder}
                </div>
              );
            }
            switch (selected) {
              case "OPEN":
                return (
                  <div
                    key="OPEN"
                    className="py-1 px-4 overflow-hidden rounded-full bg-gray-500/10 text-gray-500 text-xs"
                  >
                    OPEN
                  </div>
                );
              case "APPROVED":
                return (
                  <div
                    key="APPROVED"
                    className="py-1 px-4 overflow-hidden rounded-full bg-green-100/10 text-green-100 text-xs"
                  >
                    APPROVED
                  </div>
                );
              case "REJECTED":
                return (
                  <div
                    key="REJECTED"
                    className="py-1 px-4 overflow-hidden rounded-full bg-red-100/10 text-red-100 text-xs"
                  >
                    REJECTED
                  </div>
                );
              case "CHANGE_REQUESTED":
                return (
                  <div
                    key="CHANGE_REQUESTED"
                    className="py-1 px-4 overflow-hidden rounded-full bg-yellow-100/10 text-yellow-100 text-xs"
                  >
                    CHANGE_REQUESTED
                  </div>
                );
              default:
                return (
                  <div
                    key="OPEN"
                    className="py-1 px-4 rounded-full bg-gray-500/10 text-gray-500 text-xs"
                  >
                    OPEN
                  </div>
                );
            }
          }}
        >
          {options?.map((item: any, index: number) => (
            <MenuItem
              key={`${item}_${index}`}
              sx={{
                color: "#7B7C7D",
                "&:hover": {
                  backgroundColor: "rgba(47, 128, 237, 0.1)",
                  color: "#2F80ED",
                },
                "&:focus": {
                  backgroundColor: "rgba(47, 128, 237, 0.1)",
                  color: "#2F80ED",
                },
                fontSize: 12,
                fontWeight: 400,
                lineHeight: "1rem",
              }}
              divider
              value={item}
            >
              {item}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};
export default V2ChipSelect;
