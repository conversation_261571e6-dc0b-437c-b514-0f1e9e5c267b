'use client';

import React, { useEffect, useState } from 'react';

import { LocalService } from '@/service/local.service';

import { Tabs } from '../../ui/Tabs';
import DataAvailability from './tabs/dataAvailability';
import DataFileValidation from './tabs/dataFileValidation';
import ReconTest from './tabs/reconTests';
import ReportFreshness from './tabs/reportFreshness';
import Summary from './tabs/summary';

const DataQualityTabs: React.FC<{
  selectedYear?: any;
  onTabClick?: any;
  selectedTabIndex?: any;
}> = ({ selectedYear, onTabClick, selectedTabIndex }) => {
  const localService = new LocalService();
  // States
  const [selectedTab, setSelectedTab] = useState(
    Number(localService.getItem('selectedDataQualityTab')) || 0,
  );
  useEffect(() => {
    setSelectedTab(selectedTabIndex);
  }, [selectedTabIndex]);
  // Constant
  const tabData: any = [
    {
      title: 'Summary',
      component: (
        <Summary selectedYear={selectedYear} setTab={setSelectedTab} />
      ),
    },
    {
      title: 'Recon Tests',
      component: <ReconTest selectedYear={selectedYear} />,
    },
    {
      title: 'Reports Freshness',
      component: <ReportFreshness selectedYear={selectedYear} />,
    },
    {
      title: 'Data File Validations',
      component: <DataFileValidation selectedYear={selectedYear} />,
    },
    {
      title: 'Data Availability - Monthly',
      component: <DataAvailability selectedYear={selectedYear} />,
    },
    {
      title: 'Data Availability - Quarterly',
      component: <DataAvailability selectedYear={selectedYear} />,
    },
    {
      title: 'Data Availability - Yearly',
      component: <DataAvailability selectedYear={selectedYear} />,
    },
  ];

  return (
    <div className="flex w-full flex-col overflow-auto rounded  bg-white-200  xl:h-[calc(100vh-155px)]">
      <Tabs
        data={tabData}
        selectedIndex={selectedTab}
        onChange={(e: number) => {
          setSelectedTab(e);
          localService.setItem('selectedDataQualityTab', String(e));
          onTabClick(e);
        }}
      />
    </div>
  );
};

export default DataQualityTabs;
