'use client';

import moment from 'moment';
import { useRouter } from 'next/navigation';
import eData from 'public/testdata/fileUpload/history.json';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@/components/ui/Button';
import ListBoxTable from '@/components/ui/ListBoxTable';
import Table from '@/components/ui/Table';
import {
  monthList,
  quarterList,
  selectListForUploadDatasets,
  yearList,
} from '@/constants/appConstants';
import { APIService } from '@/service/api.service';
import { LocalService } from '@/service/local.service';
import { setIsLoading } from '@/slices/appSlice';
import { setToastAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface HistoryTableProps {
  onHistoryCountChange: (AllData: any) => void;
  scrollPosition?: any;
}
const HistoryTable: React.FC<HistoryTableProps> = ({
  scrollPosition,
  onHistoryCountChange,
}) => {
  // essentials
  const localService = new LocalService();
  const router = useRouter();
  const dispatch = useDispatch();
  const apiService = useMemo(() => new APIService(dispatch, router), []);
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const statusMapper: any = {
    FILE_PROCESSING_SUCCEEDED: 'success',
    FILE_PROCESSING_FAILED: 'error',
    FORWARDED_TO_FILE_PROCESSING: 'info',
    INPROGRESS: 'info',
    UPLOAD_FAILED: 'error',
    UPLOAD_SUCCESSFULL: 'success',
    true: 'success',
    false: 'error',
  };

  const nameMapper: any = {
    INPROGRESS: 'Processing in progress',
    UPLOAD_SUCCESSFULL: 'Processing in progress',
    UPLOAD_FAILED: 'Processing failed',
    FORWARDED_TO_FILE_PROCESSING: 'Processing in progress',
    FILE_PROCESSING_SUCCEEDED: 'Processing Successful',
    FILE_PROCESSING_FAILED: 'Processing failed',
  };

  // States
  const [filters] = useState<any>(
    localService.getData('fileUploadFilters') || '',
  );
  const [source, setSource] = useState<any>([]);
  const [filteredData, setFilteredData] = useState([]);
  const [history, setHistory] = useState<any>([]);
  const [selectedFileIsReplaced, setSelectedFileIsReplaced] = useState(
    filters?.selectedFileIsReplaced ? filters?.selectedFileIsReplaced : 'All',
  );
  const [selectedSource, setSelectedSource] = useState(
    filters?.selectedSource ? filters?.selectedSource : 'All',
  );
  const [selectedYear, setSelectedYear] = useState<any>(
    filters?.selectedYear ? filters?.selectedYear : 'All',
  );
  const [selectedMonth, setSelectedMonth] = useState<any>(
    filters?.selectedMonth ? filters?.selectedMonth : 'All',
  );
  const [selectedQuarter, setSelectedQuarter] = useState<any>(
    filters?.selectedQuarter ? filters?.selectedQuarter : 'All',
  );
  const [year] = useState<any>([{ name: 'All' }, ...yearList]);
  const [month] = useState<any>([{ name: 'All' }, ...monthList]);
  const [quarter] = useState<any>([{ name: 'All' }, ...quarterList]);
  const [replace] = useState<any>([
    { name: 'All' },
    { name: 'Yes' },
    { name: 'No' },
  ]);
  const [frequency, setFrequency] = useState('');

  // Constants
  const header: any = [
    {
      title: 'File Name',
      optionsEnabled: false,
      options: [],
    },
    {
      title: 'Business Segment',
      optionsEnabled: true,
      options: [],
      width: 'min-w-[150px]',
    },
    {
      title: 'Dataset Name',
      optionsEnabled: true,
      options: [],
    },
    {
      title: 'Year',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Quarter',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Month',
      optionsEnabled: true,
      options: ['sort'],
    },
    {
      title: 'Uploaded Date',
      optionsEnabled: true,
      options: ['sort'],
      width: 'min-w-[100px]',
    },
    {
      title: 'Status',
      optionsEnabled: true,
      options: ['sort'],
      width: 'min-w-[150px]',
    },
    {
      title: 'Is Replaced',
      optionsEnabled: true,
      options: ['sort'],
      width: 'min-w-[100px]',
    },
    // {
    //   title: "Action",
    //   optionsEnabled: true,
    //   options: [],
    // },
    {
      title: '',
      optionsEnabled: true,
      options: [],
    },
  ];

  const data: any = {
    values: history,
    enableOptions: true,
  };

  const menuData: any = [
    {
      option: 'View File',
      clickFn: (key: any) => {
        const selectedData: any = filteredData.filter(
          (item: any) => item.id === key,
        );
        if (
          selectedData[0].components[7].badges[0].content ===
          'Processing Successful'
        ) {
          const fileData: any = {
            'File Name': selectedData[0].components[0].value,
            'Business Segment': selectedData[0].components[1].value
              ? selectedData[0].components[1].value
              : '-',
            'Dataset Name': selectedData[0].components[2].value,
            Year:
              Number(selectedData[0].components[3].value) > 0
                ? selectedData[0].components[3].value
                : 'N/A',
            Quarter:
              Number(selectedData[0].components[4].value) > 0
                ? selectedData[0].components[4].value
                : 'N/A',
            Month:
              Number(selectedData[0].components[5].value) > 0
                ? selectedData[0].components[5].value
                : 'N/A',
            'Uploaded Date': selectedData[0].components[6].value,
            'Is Replaced': selectedData[0].components[8].value,
          };
          localService.setItem(
            'fileDetailForPreview',
            JSON.stringify(fileData),
          );
          const parameter: any = {
            fileId: key,
          };
          const queryString = new URLSearchParams(parameter).toString();
          router.push(`/fileUpload/filePreview?${queryString}`);
        } else {
          dispatch(
            setToastAlert({
              isToastOpen: true,
              intent: 'error',
              title: 'Error',
              content:
                'Unable to display the file preview because the status is not success. Please try again later.',
            }),
          );
        }
      },
    },
  ];

  const extractIdAndName = (sourceData: any) => {
    return sourceData.map((item: any) => {
      return {
        id: item.id,
        name: item.datasetName,
        frequency: item.frequency,
      };
    });
  };

  const resolveData = (fileData: any) => {
    if (!fileData || !Array.isArray(fileData) || fileData.length === 0) {
      return [];
    }
    return fileData.map((row) => {
      const components = [
        {
          value: row.fileName ? row.fileName : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.country ? row.country : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.datasetName ? row.datasetName : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.year) > 0 ? Number(row.year) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.quarter) > 0 ? Number(row.quarter) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: Number(row.month) > 0 ? Number(row.month) : 'N/A',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: row.date ? moment(row.date).format('YYYY-MM-DD') : '',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        {
          value: nameMapper[row?.status],
          badges: [
            {
              intent: statusMapper[row?.status],
              content: nameMapper[row?.status],
              reasonOfFailure: row.reasonOfFailure ? row.reasonOfFailure : '',
            },
          ],
          disabled: false,
          userAvatarLink: null,
          type: 'badges',
        },
        {
          value: row?.replaced ? 'Yes' : 'No',
          disabled: false,
          userAvatarLink: null,
          type: 'text',
        },
        // {
        //   value: "lock",
        //   disabled: false,
        //   userAvatarLink: null,
        //   url: "/assets/images/lock-fill.svg",
        //   type: "image",
        // },
        {
          value: 'lock',
          disabled: false,
          userAvatarLink: null,
          url: '/assets/images/lock-fill.svg',
          type: 'image',
        },
      ];
      return {
        id: row.fileId,
        components,
      };
    });
  };

  const applyFilter = (allData: any) => {
    const tempFilter: any = {
      selectedYear,
      selectedMonth:
        frequency === 'Quarterly' || frequency === 'Yearly'
          ? 'All'
          : selectedMonth,
      selectedQuarter:
        frequency === 'Monthly' || frequency === 'Yearly'
          ? 'All'
          : selectedQuarter,
      selectedSource,
      selectedFileIsReplaced,
    };
    localStorage.setItem('fileUploadFilters', JSON.stringify(tempFilter));
    if (allData.length !== 0) {
      const tempSelectedSource: any =
        selectedSource === 'All' ? '' : selectedSource;
      const tempSelectedYear: any = selectedYear === 'All' ? '' : selectedYear;
      const tempSelectedQuarter: any =
        selectedQuarter === 'All' ||
        frequency === 'Monthly' ||
        frequency === 'Yearly'
          ? ''
          : selectedQuarter;
      const tempSelectedMonth: any =
        selectedMonth === 'All' ||
        frequency === 'Quarterly' ||
        frequency === 'Yearly'
          ? ''
          : selectedMonth;
      const tempSelectedFileIsReplaced: any =
        selectedFileIsReplaced === 'All' ? '' : selectedFileIsReplaced;
      const filter: { [key: number]: any } = {};
      if (tempSelectedSource !== '')
        filter[2] = tempSelectedSource === ' ' ? '' : tempSelectedSource;
      if (tempSelectedYear !== '')
        filter[3] = Number(tempSelectedYear)
          ? Number(tempSelectedYear)
          : tempSelectedYear;
      if (tempSelectedQuarter !== '')
        filter[4] = Number(tempSelectedQuarter)
          ? Number(tempSelectedQuarter)
          : tempSelectedQuarter;
      if (tempSelectedMonth !== '')
        filter[5] = Number(tempSelectedMonth)
          ? Number(tempSelectedMonth)
          : tempSelectedMonth;
      if (tempSelectedFileIsReplaced !== '')
        filter[8] = tempSelectedFileIsReplaced;
      const tempFilteredData = allData.filter((item: any) =>
        Object.entries(filter).every(
          ([key, value]) => item.components[key].value === value,
        ),
      );
      setFilteredData(tempFilteredData);
    } else {
      setFilteredData([]);
    }
  };

  const resetFilter = () => {
    setFrequency('All');
    setSelectedSource('All');
    setSelectedMonth('All');
    setSelectedYear('All');
    setSelectedQuarter('All');
    setSelectedFileIsReplaced('All');
    const tempFilter: any = {
      selectedYear: 'All',
      selectedMonth: 'All',
      selectedQuarter: 'All',
      selectedSource: 'All',
      selectedFileIsReplaced: 'All',
    };
    localStorage.setItem('fileUploadFilters', JSON.stringify(tempFilter));
    setFilteredData(history);
  };

  const fetchHistory = async () => {
    try {
      const url = `${
        ApiUtilities.getApiServerUrlBsm +
        ApiUtilities.apiPath.getFileHistory.url
      }`;
      dispatch(setIsLoading(true));
      apiService
        .getRequest(url)
        .then((res) => {
          if (res.status === 200) {
            const formattedData: any = resolveData(res.data.reverse());
            setHistory(formattedData);
          }
        })
        .finally(() => dispatch(setIsLoading(false)));
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const findFrequency = (reportName: any, listData: any) => {
    for (const report of listData) {
      if (report.name.toLowerCase() === reportName.toLowerCase()) {
        return report.frequency;
      }
    }
    return null;
  };

  useEffect(() => {
    if (filteredData.length > 0) {
      setFilteredData(filteredData);
    }
    onHistoryCountChange(String(filteredData.length));
  }, [filteredData]);

  useEffect(() => {
    const fetchSource = async () => {
      try {
        const url = `${
          ApiUtilities.getApiServerUrlBsm +
          ApiUtilities.apiPath.getBsmDataSets.url
        }`;
        dispatch(setIsLoading(true));
        apiService
          .getRequest(url)
          .then((res) => {
            if (res.status === 200) {
              const transformedData: any = extractIdAndName(res.data);
              transformedData.push({ id: 0, name: 'All', frequency: 'All' });
              setSource(transformedData.slice().reverse());
            }
          })
          .finally(() => dispatch(setIsLoading(false)));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    if (!isTourOpen) {
      fetchSource();
    } else {
      setSource(selectListForUploadDatasets);
    }
  }, []);

  useEffect(() => {
    if (source.length > 0) {
      if (!isTourOpen) {
        fetchHistory();
      } else {
        const formattedData: any = eData;
        setHistory(formattedData);
      }
    }
  }, [source]);

  useEffect(() => {
    if (history.length > 0) {
      applyFilter(history);
    }
  }, [history]);

  useEffect(() => {
    if (selectedSource && source.length !== 0) {
      const freq: any = findFrequency(selectedSource, source);
      setFrequency(freq || 'All');
    }
  }, [selectedSource, source]);

  return (
    <div className=" flex size-full flex-col">
      <div className="flex w-full items-end justify-between px-2 py-3">
        <div className="flex flex-row space-x-4">
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Select Dataset
            </span>
            <div className="mt-3 w-[20vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                placeholder="Select Dataset"
                isInTable
                items={source}
                selectedValue={selectedSource}
                hasValue
                onSelectionChange={(selectedValue) => {
                  if (selectedValue.name === '') {
                    setSelectedSource(' ');
                  } else {
                    setSelectedSource(selectedValue.name);
                  }
                  if (frequency === 'Quarterly' || frequency === 'Yearly') {
                    setSelectedMonth('All');
                  } else if (
                    frequency === 'Monthly' ||
                    frequency === 'Yearly'
                  ) {
                    setSelectedQuarter('All');
                  }
                }}
                name="select"
              />
            </div>
          </div>
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Select Year
            </span>
            <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                isInTable
                hasValue
                items={year}
                placeholder="Select Year"
                selectedValue={selectedYear}
                onSelectionChange={(selectedValue) => {
                  setSelectedYear(selectedValue.name);
                }}
                name="year"
              />
            </div>
          </div>
          {(frequency === 'All' || frequency === 'Monthly') && (
            <div className="flex flex-col space-y-1">
              <span className="font-sans text-sm font-semibold text-gray-500">
                Select Month
              </span>
              <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
                <ListBoxTable
                  isInTable
                  hasValue
                  items={month}
                  placeholder="Select Month"
                  selectedValue={selectedMonth}
                  onSelectionChange={(selectedValue) => {
                    setSelectedMonth(selectedValue.name);
                  }}
                  name="month"
                />
              </div>
            </div>
          )}
          {(frequency === 'All' || frequency === 'Quarterly') && (
            <div className="flex flex-col space-y-1">
              <span className="font-sans text-sm font-semibold text-gray-500">
                Select Quarter
              </span>
              <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
                <ListBoxTable
                  isInTable
                  hasValue
                  items={quarter}
                  placeholder="Select Quarter"
                  selectedValue={selectedQuarter}
                  onSelectionChange={(selectedValue) => {
                    setSelectedQuarter(selectedValue.name);
                  }}
                  name="quarter"
                />
              </div>
            </div>
          )}
          <div className="flex flex-col space-y-1">
            <span className="font-sans text-sm font-semibold text-gray-500">
              Is Replaced
            </span>
            <div className="mt-3 w-[10vw] rounded border border-lightgray-100 py-1">
              <ListBoxTable
                placeholder="Select Dataset"
                isInTable
                items={replace}
                selectedValue={selectedFileIsReplaced}
                hasValue
                onSelectionChange={(selectedValue) => {
                  if (selectedValue.name === '') {
                    setSelectedFileIsReplaced(' ');
                  } else {
                    setSelectedFileIsReplaced(selectedValue.name);
                  }
                }}
                name="select"
              />
            </div>
          </div>
        </div>
        <div className=" flex flex-row space-x-2">
          <Button intent="primary" onClick={() => applyFilter(history)}>
            Apply
          </Button>
          <Button intent="secondary" onClick={resetFilter}>
            Reset
          </Button>
        </div>
      </div>
      {filteredData && (
        <div className="relative h-[calc(100vh-310px)] w-full overflow-auto">
          <Table
            hasPagination
            isDashTable
            menuData={menuData}
            header={header}
            data={filteredData}
            scrollPosition={scrollPosition}
            enableOptions={data.enableOptions}
          />
        </div>
      )}
    </div>
  );
};

export default HistoryTable;
