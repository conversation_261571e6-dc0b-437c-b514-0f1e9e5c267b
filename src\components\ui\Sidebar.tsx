/* eslint-disable import/no-extraneous-dependencies */

'use client';

/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-restricted-syntax */
/* eslint-disable consistent-return */
/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */

// components/Sidebar.js
import { Client } from '@stomp/stompjs';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Tooltip } from 'react-tooltip';
import { setExpandedValue } from 'src/slices/sideBarExpandedSlice';

import {
  imagePaths,
  sidebarJsonDataAdmin,
  sidebarJsonDataCFO,
  sidebarRoutes,
} from '@/constants/appConstants';
import { LocalService } from '@/service/local.service';
import { setSidebarDropDownVisibility } from '@/slices/appSlice';
import { resetState as resetStateBusinessModal } from '@/slices/businessModelCrudSlice';
import { resetState as resetStateLogicalEntity } from '@/slices/logicalEntityCrudSlice';
import { setNotification, setNotificationAlert } from '@/slices/metaDataSlice';
import type { RootState } from '@/store/store';
import { formatNotification } from '@/utils/utilityHelper';

import Avatar from './Avatar';

interface SidebarProps {
  isExpanded: boolean;
  setIsExpanded: (isExpanded: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isExpanded, setIsExpanded }) => {
  // Essentials
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useDispatch();
  const localService = new LocalService();
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [showImages, setShowImages] = useState(true);

  // States
  const [isActiveMenu, setActiveMenu] = useState('System Integration');
  const [userType] = useState(localService.getItem('user_type') || '');

  // Selectors
  const isTourOpen = useSelector(
    (state: RootState) => state.appConfig.isTourOpen,
  );
  const sidebarDropdownVisibility = useSelector(
    (state: RootState) => state.appConfig.sidebarDropdownVisibility,
  );

  const notifications = useSelector(
    (state: RootState) => state.metadata.notification,
  );

  // Constants
  const sidebarData =
    userType === 'CFO' ? sidebarJsonDataCFO : sidebarJsonDataAdmin;
  const isHovered = isActiveMenu;

  const client = new Client({
    brokerURL: 'wss://api.test.bsm.astrai.io/astrai-websocket',
    onConnect: () => {
      console.log('connection created');
      client.subscribe('/topic/updates', (update: any) => {
        const notification = formatNotification(JSON.parse(update.body));
        if (notification) {
          dispatch(setNotification([...notifications, notification]));
          dispatch(
            setNotificationAlert({
              isNotificationOpen: true,
              intent: notification.status,
              title: 'New Notification',
              content: notification.alertMessage,
            }),
          );
        }
      });
    },
    onWebSocketError: (error: any) => {
      console.error('Error with websocket', error);
    },
    onStompError: (frame: any) => {
      console.error(`Broker reported error: ${frame.headers.message}`);
      console.error(`Additional details: ${frame.body}`);
    },
  });

  // Methods
  const logout = () => {
    localStorage.clear();
    router.push('/login');
  };

  const togglePinSidebar = () => {
    dispatch(setExpandedValue(!isExpanded));
    setIsExpanded(!isExpanded);
  };

  const setDropdownStateForInnerItem = (name: string) => {
    if (name === 'Datasets' || name === 'Logical Entities') {
      dispatch(
        setSidebarDropDownVisibility({
          Insights: sidebarDropdownVisibility.Insights,
          'Data Management': true,
          'Operations': sidebarDropdownVisibility.Operations,
        }),
      );
    }
    if (
      name === 'WCO-OTC' ||
      name === 'WCO-PTP' ||
      name === 'Enterprise' ||
      name === 'Customer Focus' ||
      name === 'Supplier Focus' ||
      name === 'financial Focus'
    ) {
      dispatch(
        setSidebarDropDownVisibility({
          Insights: true,
          'Data Management': sidebarDropdownVisibility['Data Management'],
          'Operations': sidebarDropdownVisibility.Operations,
        }),
      );
    }

    if (name !== 'Data Management' && name !== 'Insights') {
      setActiveMenu(name);
    }
  };

  const toggleVisibilityofDropDown = (name: string) => {
    if (name === 'Data Management') {
      dispatch(
        setSidebarDropDownVisibility({
          Insights: sidebarDropdownVisibility.Insights,
          'Data Management': !sidebarDropdownVisibility['Data Management'],
          'Operations': sidebarDropdownVisibility.Operations,
        }),
      );
    } else if (name === 'Insights') {
      dispatch(
        setSidebarDropDownVisibility({
          Insights: !sidebarDropdownVisibility.Insights,
          'Data Management': sidebarDropdownVisibility['Data Management'],
          'Operations': sidebarDropdownVisibility.Operations,
        }),
      );
    }
  };

  const clearStates = () => {
    dispatch(resetStateLogicalEntity());
    dispatch(resetStateBusinessModal());
  };

  const setSelectedMenu = (name: string, route: string) => {
    setDropdownStateForInnerItem(name);
    clearStates();
    router.push(route);
  };

  const renderMenu = (menu: any, isInnerItem = false) => {
    const {
      name,
      activeIcon,
      inActiveIcon,
      hasDropdown,
      dropDownData,
      route,
      tour,
    } = menu;

    return (
      <div
        key={name}
        className={` w-full flex-col space-y-8 ${`${tour}`} ${
          isInnerItem && isExpanded ? 'px-4 ' : 'items-center'
        }`}
      >
        {hasDropdown === false && (
          <button
            data-tooltip-id="menu-tooltip"
            data-tooltip-content={name}
            type="button"
            onClick={() => {
              setSelectedMenu(name, route);
            }}
            className={`flex w-full flex-row items-center space-x-2  rounded p-2 hover:bg-blue-200/10 ${
              isHovered === name && isActiveMenu === name
                ? 'bg-blue-200/10'
                : 'bg-transparent'
            }`}
          >
            <Image
              width={24}
              height={24}
              priority
              src={
                isHovered === name && isActiveMenu === name
                  ? `/assets/images/sidebar/${activeIcon}`
                  : `/assets/images/sidebar/${inActiveIcon}`
              }
              alt={name}
            />
            {!isExpanded && (
              <Tooltip
                id="menu-tooltip"
                place="right"
                variant="light"
                positionStrategy="fixed"
              />
            )}
            {isExpanded && (
              <span
                className={`overflow-hidden  whitespace-nowrap font-sans text-sm font-semibold transition-all duration-300
              ${isActiveMenu === name ? ' text-blue-200' : ' text-gray-500'}
                `}
              >
                {name}
              </span>
            )}
          </button>
        )}
        {hasDropdown === true && (
          <div
            className={`flex w-full flex-col space-y-2 rounded bg-lightgray-500 ${
              isExpanded ? '' : 'items-center'
            }`}
          >
            <button
              type="button"
              data-tooltip-id="dropdown-tooltip"
              data-tooltip-content={name}
              onClick={() => {
                toggleVisibilityofDropDown(name);
              }}
              className={`flex w-full flex-row items-center space-x-1 rounded p-2 hover:bg-blue-200/10 hover:text-blue-200 ${
                isHovered === name && isActiveMenu === name
                  ? 'bg-blue-200/10'
                  : 'bg-transparent'
              }`}
            >
              <Image
                width={24}
                height={24}
                src={
                  isHovered === name && isActiveMenu === name
                    ? `/assets/images/sidebar/${activeIcon}`
                    : `/assets/images/sidebar/${inActiveIcon}`
                }
                alt={name}
              />
              {!isExpanded && (
                <Tooltip
                  id="dropdown-tooltip"
                  place="right"
                  variant="light"
                  positionStrategy="fixed"
                />
              )}

              {isExpanded && (
                <div className="flex w-full flex-row justify-between pl-[4px]">
                  <span
                    className={`overflow-hidden whitespace-nowrap font-sans text-sm font-semibold
                   ${
                     isActiveMenu === name ? 'text-blue-200' : 'text-gray-500'
                   }`}
                  >
                    {name}
                  </span>
                  <Image
                    className={` ${
                      sidebarDropdownVisibility[name] ? '' : 'rotate-180'
                    }`}
                    width={24}
                    priority
                    height={24}
                    src="/assets/images/menuDrop-Arrow-black.svg"
                    alt="dropdown"
                  />
                </div>
              )}
            </button>

            {(sidebarDropdownVisibility[name] || isTourOpen) &&
              dropDownData && dropDownData.map((item: any) => renderMenu(item, true))}
          </div>
        )}
      </div>
    );
  };

  const getPinImage = () => {
    if (!isExpanded) {
      return `/assets/images/right-arrow.svg`;
    }
    return `/assets/images/left-arrow.svg`;
  };

  // Effects
  useEffect(() => {
    const currentRoute: any = pathname;
    const menuItems: any = sidebarRoutes;
    const uName =
      localStorage.getItem('name') || localStorage.getItem('userName') || '';
    const uEmail = localStorage.getItem('userEmail') || '';
    setUserName(uName);
    setUserEmail(uEmail);
    for (const [menuItem, routes] of Object.entries(menuItems)) {
      if (
        (Array.isArray(routes) &&
          routes.some(
            (route) => route === currentRoute || currentRoute.startsWith(route),
          )) ||
        routes === currentRoute
      ) {
        setDropdownStateForInnerItem(menuItem);
        break;
      }
    }
  }, [pathname]);

  useEffect(() => {
    setShowImages(false);
  }, []);

  useEffect(() => {
    client.activate();
    return () => {
      if (client) {
        client.deactivate();
      }
    };
  }, [notifications]);

  return (
    <div
      className={`z-40 h-full ${
        isExpanded ? 'min-w-[256px]' : 'min-w-[60px]'
      } bg-lightgray-100/40`}
    >
      <div
        className={`fixed z-10 flex h-screen flex-col  items-center overflow-hidden bg-white-200 shadow-sidebar transition-all duration-700 ${
          isExpanded || isExpanded ? 'w-[256px]' : 'w-[60px]'
        }`}
      >
        <div className="flex h-full w-full flex-col">
          <div
            className={` mb-6 flex h-[60px] items-center justify-start bg-blue-200 px-[5px] transition-all duration-500`}
          >
            <span className="font-sans text-lg font-semibold text-white-200 transition-opacity duration-300">
              <Image
                src="/assets/images/astrai-logo-white.svg"
                width={175}
                height={65}
                priority
                alt="astrai-logo"
                className="max-w-none"
              />
            </span>
          </div>
          <div className="flex w-full flex-col space-y-1 px-[10px] ">
            {sidebarData.map((menu: any) => renderMenu(menu))}
          </div>
          <div className="absolute bottom-0 block w-full items-center ">
            <div className="flex h-[55px] flex-row space-x-2 px-[10px]">
              <div className="flex flex-row items-center justify-center">
                {' '}
                <Avatar name={userName} />
              </div>

              {isExpanded && (
                <div className="flex w-full flex-row items-center justify-between">
                  <div className=" flex flex-col space-y-1">
                    <span className="font-sans text-sm font-semibold text-blueGray-300">
                      {userName}
                    </span>
                    <span className="font-sans text-xs font-normal text-gray-400">
                      {userEmail}
                    </span>
                  </div>
                  <Image
                    onClick={logout}
                    className=" cursor-pointer"
                    width={24}
                    height={24}
                    priority
                    src="/assets/images/logout.svg"
                    alt="logout"
                  />
                </div>
              )}
            </div>
            <div className=" flex flex-row">
              {imagePaths.map((imagePath: any, index: any) => (
                <Image
                  className={`${showImages ? 'flex' : 'hidden'}`}
                  key={index}
                  src={imagePath}
                  alt={`Image ${index}`}
                  width={0.2}
                  height={0.2}
                />
              ))}
            </div>
            <div className="flex h-[40px] w-full flex-row justify-end bg-gray-300 pr-4">
              <Image
                onClick={togglePinSidebar}
                src={`${getPinImage()}`}
                width={24}
                priority
                height={24}
                alt="pin"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
