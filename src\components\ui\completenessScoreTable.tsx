/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
import React from 'react';

import { PopoverComp } from './Popover';

const DataCompletenessTable: React.FC<{
  scrollPosition?: any;
  tableIndex?: number;
  headers: any[];
  rows: any[];
}> = ({ scrollPosition, tableIndex, headers, rows }) => {
  return (
    <div className="my-2 bg-white-200">
      <table className="w-full table-fixed">
        <thead>
          <tr>
            {headers.map((header: any, index: any) => (
              <th
                key={index}
                className={`border-[1px] border-lightgray-100 p-4  ${
                  tableIndex === 0
                    ? ' w-[100px]'
                    : tableIndex === 1
                    ? 'w-[25%]'
                    : 'w-[200px] sm:w-[30%]'
                } `}
              >
                <span className="font-sans text-sm font-semibold uppercase text-gray-500">
                  {header}
                </span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {Object.values(row).map((cell: any, cellIndex: any) => (
                <td
                  key={cellIndex}
                  className="border-[1px] border-lightgray-100 p-4  text-center "
                >
                  <PopoverComp
                    scrollPosition={scrollPosition}
                    data={cell.tableData}
                    score={cell.completenessScore}
                  />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DataCompletenessTable;
