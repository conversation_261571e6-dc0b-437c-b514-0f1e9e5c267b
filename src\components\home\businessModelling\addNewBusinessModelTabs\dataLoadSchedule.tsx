import 'react-js-cron/dist/styles.css';

import React from 'react';
import Cron from 'react-js-cron';
import { useDispatch, useSelector } from 'react-redux';

import { setSchedulingConfig } from '@/slices/businessModelCrudSlice';

const DataLoadSchedule: React.FC<{
  mode: string | string[] | undefined;
}> = () => {
  // Essentials
  const dispatch = useDispatch();

  // Selectors
  const schedulingConfig = useSelector(
    (state: any) => state.businessModelCrud.schedulingConfig,
  );

  return (
    <div className="h-[68vh] w-full">
      <div className="flex h-full w-full flex-row space-x-6 overflow-auto p-6 pt-2">
        <div className="flex w-full flex-col space-y-1">
          <span className="font-sans text-sm font-semibold text-gray-500">
            Select Frequency
          </span>

          <div className="flex flex-row items-center justify-start space-x-3">
            <span className="flex font-sans text-sm leading-4 tracking-[0.2px] text-gray-500">
              <div className="inline w-full">
                <Cron
                  value={schedulingConfig || '* * * * *'}
                  setValue={(v: any) => {
                    dispatch(setSchedulingConfig(v));
                  }}
                  humanizeLabels
                  humanizeValue
                  clearButton
                />
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataLoadSchedule;
