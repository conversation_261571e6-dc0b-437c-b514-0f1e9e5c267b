export const tourStepsAdmin = [
  {
    selector: '.home',
    content:
      'Welcome to the Homepage of the astRai Platform. This page will provide the pathways to access the rich capabilities provided by the platform.',
  },
  {
    selector: '.insight-cards',
    content:
      "This home page area's cards display the Insights provided by the platform. Each functional area has one card and the listings of dashboards for specific subject areas. The users can click on the link to a specific dashboard for viewing Insights in the specific area as the name would suggest.",
  },
  {
    selector: '.insight-card',
    content:
      'Each card has links to the reports or dashboards of a specific subject area. By clicking on the names of the subject area, users can access the platform provided contextualised data visualisations.',
  },
  {
    selector: '.data-management',
    content:
      'These cards will provide the pathways to access the features for Data Management in the astRai platform. Users can click on the feature names in each of the cards to perform a specific function of view the contents of the data hub.',
  },
  {
    selector: '.data-management-dash',
    content: 'Users can click on the entity to navigate to specific pages.',
  },
  {
    selector: '.notification',
    content:
      'Notifications can be accessed by clicking on the bell icon. Upon clicking, a panel will open, displaying all notifications on the status of actions submitted to the astRai Platform.',
  },
  {
    selector: '.dataset',
    content: 'Manage datasets from various connections collectively.',
  },
  {
    selector: '.dataset-table',
    content:
      'This table aggregates all the datasets created within the system, providing a comprehensive list of available datasets for easy reference and management.',
  },
  {
    selector: '.business-views',
    content:
      'The Business Views feature offers a listing of the various business context oriented data views built by the astRai Platform which are used for providing Insights. This Business Views feature offers the capability to see the data in the views, filter the data as needed and export the data for offline analysis.',
  },
  {
    selector: '.business-views-table',
    content:
      'This is a compiled table showcasing all the business views created subsequent to generating datasets.',
  },
  {
    selector: '.upload-file',
    content:
      'Navigate to the upload file section to submit your file, then access the file history section to view recent uploads or modifications and select a file for details or previous versions.',
  },
  {
    selector: '.add-new-file',
    content:
      'Users can upload files and view the uploaded files from this screen.',
  },

  {
    selector: '.history',
    content: 'Users can view, update and delete the history from this table.',
  },

  {
    selector: '.data-quality-dashboard',
    content:
      'The data quality dashboard provides comprehensive insights into yearly, monthly, and quarterly data, accompanied by an indicator table delineating datasets alongside their corresponding status.',
  },
  {
    selector: '.data-quality-summary-tabs',
    content:
      'The Test Scenarios Summary tab on this page provides the overview of the Data Quality KPls. in the form of cards with specific metrics in the form of pie charts. Clicking each card will take the user to the specific tab of the quality monitoring area to provide further insights.',
  },
  {
    selector: '.end',
    content:
      'Thank you for your time and participation in our app tour, we hope it enriches your experience with our platform.',
  },
];

export const tourStepsCFO = [
  {
    selector: '.home',
    content:
      'Welcome to the central hub of the application, serving as the landing page.',
  },
  {
    selector: '.insight-cards',
    content:
      'These cards will display the insight dashboard details. Users can click on the dashboard for effective data visualization.',
  },
  {
    selector: '.insight-card',
    content:
      'Each card will have dashboards. By clicking on the dashboards, users can seamlessly access visual representations on Power BI.',
  },
  {
    selector: '.notification',
    content:
      'Notifications can be accessed by clicking on the bell icon. Upon clicking, a professional right panel will open, displaying all notifications for easy viewing',
  },
  {
    selector: '.end',
    content:
      'Thank you for your time and participation in our app tour; we hope it enriches your experience with our platform.',
  },
];
